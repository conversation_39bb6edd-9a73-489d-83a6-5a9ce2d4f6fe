<!-- 资金流向分析 -->
<div class="col-md-6">
  <div class="card h-100">
    <div class="card-header bg-light">
      <div class="d-flex align-items-center">
        <div class="avatar bg-blue-lt me-2">
          <i class="ti ti-cash"></i>
        </div>
        <h3 class="card-title m-0">资金流向分析</h3>
      </div>
    </div>
    <div class="card-body p-3">
      <!-- 主力资金流向 -->
      <div class="d-flex justify-content-between align-items-center mb-2">
        <span class="text-muted">主力资金净流入</span>
        <span class="badge {% if market_sentiment.up_count > market_sentiment.down_count %}bg-danger{% else %}bg-success{% endif %} text-white">
          {% if market_sentiment.up_count > market_sentiment.down_count %}+{% else %}-{% endif %}{{
          market_sentiment.up_count|add:market_sentiment.down_count|default:0|floatformat:2 }}亿元
        </span>
      </div>

      <div class="progress mb-3" style="height: 8px; border-radius: 4px">
        <div
          class="progress-bar {% if market_sentiment.up_count > market_sentiment.down_count %}bg-danger{% else %}bg-success{% endif %}"
          style="width: {% if market_sentiment.up_count > market_sentiment.down_count %}65{% else %}35{% endif %}%"
          role="progressbar"
        ></div>
      </div>

      <div class="row g-2 text-center mb-4">
        <div class="col-6">
          <div class="card bg-light border-0">
            <div class="card-body p-2">
              <h5 class="{% if market_sentiment.up_count > market_sentiment.down_count %}text-danger{% else %}text-success{% endif %} mb-0">
                {% if market_sentiment.up_count > market_sentiment.down_count %}+{% else %}-{% endif %}{{ market_sentiment.up_count|default:0|floatformat:2 }}亿
              </h5>
              <div class="small text-muted">大单净流入</div>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="card bg-light border-0">
            <div class="card-body p-2">
              <h5 class="{% if market_sentiment.down_count > market_sentiment.up_count %}text-danger{% else %}text-success{% endif %} mb-0">
                {% if market_sentiment.down_count > market_sentiment.up_count %}+{% else %}-{% endif %}{{ market_sentiment.down_count|default:0|floatformat:2
                }}亿
              </h5>
              <div class="small text-muted">小单净流入</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 北向资金流向 -->
      <div class="d-flex justify-content-between align-items-center mb-2">
        <span class="text-muted">北向资金净流入</span>
        <span class="badge {% if market_sentiment.up_count > market_sentiment.down_count %}bg-danger{% else %}bg-success{% endif %} text-white">
          {% if market_sentiment.up_count > market_sentiment.down_count %}+{% else %}-{% endif %}{{
          market_sentiment.up_count|add:market_sentiment.down_count|default:0|floatformat:2 }}亿元
        </span>
      </div>

      <div class="progress mb-3" style="height: 8px; border-radius: 4px">
        <div
          class="progress-bar {% if market_sentiment.up_count > market_sentiment.down_count %}bg-danger{% else %}bg-success{% endif %}"
          style="width: {% if market_sentiment.up_count > market_sentiment.down_count %}65{% else %}35{% endif %}%"
          role="progressbar"
        ></div>
      </div>

      <div class="row g-2 text-center mb-4">
        <div class="col-6">
          <div class="card bg-light border-0">
            <div class="card-body p-2">
              <h5 class="{% if market_sentiment.up_count > market_sentiment.down_count %}text-danger{% else %}text-success{% endif %} mb-0">
                {% if market_sentiment.up_count > market_sentiment.down_count %}+{% else %}-{% endif %}{{ market_sentiment.up_count|default:0|floatformat:2 }}亿
              </h5>
              <div class="small text-muted">沪股通</div>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="card bg-light border-0">
            <div class="card-body p-2">
              <h5 class="{% if market_sentiment.down_count > market_sentiment.up_count %}text-danger{% else %}text-success{% endif %} mb-0">
                {% if market_sentiment.down_count > market_sentiment.up_count %}+{% else %}-{% endif %}{{ market_sentiment.down_count|default:0|floatformat:2
                }}亿
              </h5>
              <div class="small text-muted">深股通</div>
            </div>
          </div>
        </div>
      </div>

      {% include "market_data/temp_industry_flow.html" %}
    </div>
  </div>
</div>
