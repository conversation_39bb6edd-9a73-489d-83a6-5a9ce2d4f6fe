{% extends 'base.html' %} {% block title %}{{ sector_name }}资金流向详情 - 股票数据分析系统{% endblock %} {% block extra_css %}
<style>
  /* 中国市场习惯：红涨绿跌 */
  .text-up {
    color: #f44336 !important;
    font-weight: 500;
  }
  .text-down {
    color: #4caf50 !important;
    font-weight: 500;
  }

  .chart-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .metric-card {
    transition: transform 0.2s ease;
  }

  .metric-card:hover {
    transform: translateY(-2px);
  }

  .sector-info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .sector-info-card .card-body {
    padding: 2rem;
  }
</style>
{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'market_data:index' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'market_data:sector_fund_flow' %}">行业资金流向</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ sector_name }}</li>
          </ol>
        </nav>
        <h2 class="page-title">{{ sector_name }}资金流向详情</h2>
        <div class="text-muted mt-1">查看{{ sector_name }}的历史资金流向数据和趋势</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <a href="{% url 'market_data:sector_fund_flow' %}" class="btn btn-outline-secondary"> <i class="ti ti-arrow-left me-1"></i>返回列表 </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 行业基本信息卡片 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card sector-info-card">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-3">
                <div class="text-center">
                  <h3 class="mb-1">{{ sector_name }}</h3>
                  <div class="text-white-50">{{ sector_type }}</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="text-center">
                  <div class="text-white-50 mb-1">最新涨跌幅</div>
                  <h4 class="mb-0">
                    {% if latest_data.net_inflow_rate > 0 %}
                    <span class="text-white">+{{ latest_data.net_inflow_rate|floatformat:2 }}%</span>
                    {% else %}
                    <span class="text-white">{{ latest_data.net_inflow_rate|floatformat:2 }}%</span>
                    {% endif %}
                  </h4>
                </div>
              </div>
              <div class="col-md-3">
                <div class="text-center">
                  <div class="text-white-50 mb-1">最新主力净流入</div>
                  <h4 class="mb-0">
                    {% if latest_data.net_inflow_amount > 0 %}
                    <span class="text-white">+{{ latest_data.net_inflow_amount|floatformat:2 }}亿</span>
                    {% else %}
                    <span class="text-white">{{ latest_data.net_inflow_amount|floatformat:2 }}亿</span>
                    {% endif %}
                  </h4>
                </div>
              </div>
              <div class="col-md-3">
                <div class="text-center">
                  <div class="text-white-50 mb-1">30日累计净流入</div>
                  <h4 class="mb-0">
                    {% if cumulative_inflow > 0 %}
                    <span class="text-white">+{{ cumulative_inflow|floatformat:2 }}亿</span>
                    {% else %}
                    <span class="text-white">{{ cumulative_inflow|floatformat:2 }}亿</span>
                    {% endif %}
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">近30日累计净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="total-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">日均净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="avg-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">最大单日净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="max-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">净流入正向天数</div>
            </div>
            <div class="h2 mb-0 mt-1" id="positive-days">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行业资金流向趋势图 -->
    {% include 'components/sector_fund_flow_chart.html' %}

    <!-- 资金类型分布图和历史数据表格 -->
    <div class="row">
      <div class="col-md-6">
        <div class="card chart-container">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-chart-pie me-2 text-success"></i>
              资金类型分布
            </h3>
          </div>
          <div class="card-body">
            <div id="fund-type-chart" style="height: 300px"></div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-table me-2 text-info"></i>
              近期资金流向数据
            </h3>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter card-table">
                <thead>
                  <tr>
                    <th>日期</th>
                    <th>涨跌幅</th>
                    <th>主力净流入</th>
                    <th>净占比</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in sector_fund_flow_data|slice:":10" %}
                  <tr>
                    <td>{{ item.trade_date|date:"m-d" }}</td>
                    <td>
                      {% if item.net_inflow_rate > 0 %}
                      <span class="text-up">+{{ item.net_inflow_rate|floatformat:2 }}%</span>
                      {% elif item.net_inflow_rate < 0 %}
                      <span class="text-down">{{ item.net_inflow_rate|floatformat:2 }}%</span>
                      {% else %}
                      <span class="text-muted">{{ item.net_inflow_rate|floatformat:2 }}%</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if item.net_inflow_amount > 0 %}
                      <span class="text-up">+{{ item.net_inflow_amount|floatformat:2 }}亿</span>
                      {% elif item.net_inflow_amount < 0 %}
                      <span class="text-down">{{ item.net_inflow_amount|floatformat:2 }}亿</span>
                      {% else %}
                      <span class="text-muted">{{ item.net_inflow_amount|floatformat:2 }}亿</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if item.main_net_inflow_pct > 0 %}
                      <span class="text-up">+{{ item.main_net_inflow_pct|floatformat:2 }}%</span>
                      {% elif item.main_net_inflow_pct < 0 %}
                      <span class="text-down">{{ item.main_net_inflow_pct|floatformat:2 }}%</span>
                      {% else %}
                      <span class="text-muted">{{ item.main_net_inflow_pct|floatformat:2 }}%</span>
                      {% endif %}
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="4" class="text-center text-muted">暂无数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 确保ECharts已加载
    if (typeof echarts === 'undefined') {
      console.error('ECharts库未加载')
      return
    }

    // 延迟初始化，确保DOM完全加载
    setTimeout(function () {
      // 初始化行业资金流向趋势图
      if (window.initSectorFundFlowChart) {
        window.initSectorFundFlowChart('sector-fund-flow-chart', '{{ sector_name }}', {
          sectorType: '{{ sector_type }}',
          showLegend: true,
          showToolbox: true,
          onPeriodChange: function (days) {
            // 当时间周期改变时，更新资金类型分布图和统计指标
            initFundTypeChart(days)
            updateMetrics(days)
          },
        })
      }

      // 初始化资金类型分布图（默认60天）
      initFundTypeChart(60)

      // 更新统计指标（默认60天）
      updateMetrics(60)
    }, 300)
  })

  // 更新统计指标
  function updateMetrics(days = 60) {
    const apiUrl = `/market_data/api/sector-fund-flow-trend/${encodeURIComponent('{{ sector_name }}')}/?days=${days}&sector_type=${encodeURIComponent(
      '{{ sector_type }}'
    )}`

    fetch(apiUrl)
      .then((response) => response.json())
      .then((result) => {
        if (result.success && result.summary) {
          const summary = result.summary

          // 更新指标显示
          document.getElementById('total-inflow').innerHTML = `<span class="${summary.total_main_inflow >= 0 ? 'text-up' : 'text-down'}">
                ${summary.total_main_inflow >= 0 ? '+' : ''}${summary.total_main_inflow}亿
              </span>`

          document.getElementById('avg-inflow').innerHTML = `<span class="${summary.avg_main_inflow >= 0 ? 'text-up' : 'text-down'}">
                ${summary.avg_main_inflow >= 0 ? '+' : ''}${summary.avg_main_inflow}亿
              </span>`

          document.getElementById('max-inflow').innerHTML = `<span class="text-primary">${summary.max_inflow}亿</span>`

          document.getElementById('positive-days').innerHTML = `<span class="text-primary">${summary.positive_days}天</span>`
        }
      })
      .catch((error) => {
        console.error('更新统计指标失败:', error)
      })
  }

  // 资金类型分布图
  function initFundTypeChart(days = 60) {
    const fundTypeChart = echarts.init(document.getElementById('fund-type-chart'))
    const apiUrl = `/market_data/api/sector-fund-flow-trend/${encodeURIComponent('{{ sector_name }}')}/?days=${days}&sector_type=${encodeURIComponent(
      '{{ sector_type }}'
    )}`

    fetch(apiUrl)
      .then((response) => response.json())
      .then((result) => {
        if (result.success && result.summary && result.summary.fund_type_summary) {
          const summary = result.summary.fund_type_summary

          const option = {
            backgroundColor: 'transparent',
            title: {
              text: `近${days}日资金类型分布`,
              left: 'center',
              top: 10,
              textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
                color: '#333',
              },
            },
            tooltip: {
              trigger: 'item',
              formatter: function (params) {
                const value = params.value.toFixed(2)
                const percent = params.percent.toFixed(1)
                const isPositive = params.data.originalValue >= 0
                const symbol = isPositive ? '+' : ''
                const color = isPositive ? '#ef4444' : '#22c55e'

                return `<div style="font-weight:bold;margin-bottom:5px;">${params.name}</div>
                        <div>净流入: <span style="color:${color};">${symbol}${params.data.originalValue.toFixed(2)}亿</span></div>
                        <div>占比: ${percent}%</div>`
              },
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              top: 'middle',
              textStyle: {
                fontSize: 12,
              },
              formatter: function (name) {
                return name
              },
            },
            series: [
              {
                name: '资金类型',
                type: 'pie',
                radius: ['30%', '60%'], // 环形图
                center: ['60%', '50%'],
                avoidLabelOverlap: false,
                label: {
                  show: true,
                  position: 'outside',
                  formatter: function (params) {
                    const isPositive = params.data.originalValue >= 0
                    const symbol = isPositive ? '+' : ''
                    return `${params.name}\n${symbol}${params.data.originalValue.toFixed(1)}亿`
                  },
                  fontSize: 11,
                },
                labelLine: {
                  show: true,
                  length: 10,
                  length2: 5,
                },
                data: [
                  {
                    value: Math.abs(summary.super_big),
                    name: '超大单',
                    originalValue: summary.super_big,
                    itemStyle: { color: '#dc2626' },
                  },
                  {
                    value: Math.abs(summary.big),
                    name: '大单',
                    originalValue: summary.big,
                    itemStyle: { color: '#f97316' },
                  },
                  {
                    value: Math.abs(summary.medium),
                    name: '中单',
                    originalValue: summary.medium,
                    itemStyle: { color: '#eab308' },
                  },
                  {
                    value: Math.abs(summary.small),
                    name: '小单',
                    originalValue: summary.small,
                    itemStyle: { color: '#22c55e' },
                  },
                ],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                  label: {
                    show: true,
                    fontSize: 12,
                    fontWeight: 'bold',
                  },
                },
              },
            ],
          }

          fundTypeChart.setOption(option)
        }
      })
      .catch((error) => {
        console.error('加载资金类型分布数据失败:', error)
      })
  }
</script>
{% endblock %}
