{% if page_obj %}
<div class="card-footer d-flex align-items-center flex-wrap bg-light">
  <div class="d-flex align-items-center mb-2 mb-md-0">
    <div class="badge bg-primary text-white me-3">
      <i class="ti ti-file-text me-1"></i>
      总计: {{ page_obj.paginator.count }} 条记录
    </div>
    <p class="m-0 text-muted">
      第 <span class="fw-medium text-primary">{{ page_obj.number }}</span> 页，共 <span class="fw-medium">{{ page_obj.paginator.num_pages }}</span> 页
    </p>
    <div class="ms-3">
      <form class="d-inline-flex" method="get">
        {% for key, value in request.GET.items %}
          {% if key != 'page' and key != 'rows_per_page' %}
            <input type="hidden" name="{{ key }}" value="{{ value }}">
          {% endif %}
        {% endfor %}
        <div class="d-flex align-items-center">
          <span class="text-muted me-2">每页显示</span>
          <select class="form-select form-select-sm" name="rows_per_page" onchange="this.form.submit()" style="width: 70px;">
            <option value="5" {% if rows_per_page == 5 %}selected{% endif %}>5</option>
            <option value="10" {% if rows_per_page == 10 or rows_per_page == None %}selected{% endif %}>10</option>
            <option value="20" {% if rows_per_page == 20 %}selected{% endif %}>20</option>
            <option value="50" {% if rows_per_page == 50 %}selected{% endif %}>50</option>
            <option value="100" {% if rows_per_page == 100 %}selected{% endif %}>100</option>
          </select>
          <span class="text-muted ms-2">条</span>
        </div>
      </form>
    </div>
  </div>
  <div class="ms-auto me-4">
    {% if show_flow_indicator %}
    <div class="d-flex align-items-center">
      <div class="d-flex align-items-center me-3">
        <span class="badge bg-danger-subtle text-danger rounded-circle me-1" style="width: 10px; height: 10px;"></span>
        <span class="text-muted">净流入</span>
      </div>
      <div class="d-flex align-items-center">
        <span class="badge bg-success-subtle text-success rounded-circle me-1" style="width: 10px; height: 10px;"></span>
        <span class="text-muted">净流出</span>
      </div>
    </div>
    {% endif %}
  </div>

  <div class="d-flex align-items-center ms-auto">
    <!-- 页码跳转表单 -->
    <form class="d-flex align-items-center me-3" method="get" onsubmit="return validatePageNumber(this);">
      {% for key, value in request.GET.items %}
        {% if key != 'page' %}
          <input type="hidden" name="{{ key }}" value="{{ value }}">
        {% endif %}
      {% endfor %}
      <span class="text-muted me-2">跳转到</span>
      <input type="number" name="page" class="form-control form-control-sm" style="width: 60px;" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}">
      <button type="submit" class="btn btn-sm btn-primary ms-2">确定</button>
    </form>

    <ul class="pagination pagination-sm m-0">
      <!-- 首页和上一页按钮 -->
      <li class="page-item {% if not page_obj.has_previous %}disabled{% endif %}">
        <a
          class="page-link text-primary"
          href="{% if page_obj.has_previous %}?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% else %}#{% endif %}"
          {% if not page_obj.has_previous %}aria-disabled="true"{% endif %}
          title="首页"
        >
          <i class="ti ti-chevrons-left"></i>
        </a>
      </li>
      <li class="page-item {% if not page_obj.has_previous %}disabled{% endif %}">
        <a
          class="page-link text-primary"
          href="{% if page_obj.has_previous %}?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% else %}#{% endif %}"
          {% if not page_obj.has_previous %}aria-disabled="true"{% endif %}
          title="上一页"
        >
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>

      <!-- 页码按钮 -->
      {% with ''|center:page_obj.paginator.num_pages as range %}
        {% for _ in range %}
          {% with forloop.counter as i %}
            {% if page_obj.paginator.num_pages <= 7 or i == 1 or i == page_obj.paginator.num_pages or i >= page_obj.number|add:"-2" and i <= page_obj.number|add:"2" %}
              <li class="page-item {% if i == page_obj.number %}active{% endif %}">
                <a class="page-link {% if i != page_obj.number %}text-primary{% endif %}" href="{% if i != page_obj.number %}?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% else %}#{% endif %}">{{ i }}</a>
              </li>
            {% elif i == 2 and page_obj.number > 4 or i == page_obj.paginator.num_pages|add:"-1" and page_obj.number < page_obj.paginator.num_pages|add:"-3" %}
              <li class="page-item disabled">
                <a class="page-link" href="#">...</a>
              </li>
            {% endif %}
          {% endwith %}
        {% endfor %}
      {% endwith %}

      <!-- 下一页和末页按钮 -->
      <li class="page-item {% if not page_obj.has_next %}disabled{% endif %}">
        <a
          class="page-link text-primary"
          href="{% if page_obj.has_next %}?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% else %}#{% endif %}"
          {% if not page_obj.has_next %}aria-disabled="true"{% endif %}
          title="下一页"
        >
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
      <li class="page-item {% if not page_obj.has_next %}disabled{% endif %}">
        <a
          class="page-link text-primary"
          href="{% if page_obj.has_next %}?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% else %}#{% endif %}"
          {% if not page_obj.has_next %}aria-disabled="true"{% endif %}
          title="末页"
        >
          <i class="ti ti-chevrons-right"></i>
        </a>
      </li>
    </ul>
  </div>
</div>
{% endif %}

<script>
  function validatePageNumber(form) {
    const input = form.querySelector('input[name="page"]');
    const pageNumber = parseInt(input.value);
    const maxPage = parseInt(input.getAttribute('max'));

    if (isNaN(pageNumber) || pageNumber < 1 || pageNumber > maxPage) {
      alert('请输入有效的页码，范围为 1 - ' + maxPage);
      input.value = {{ page_obj.number }};
      return false;
    }
    return true;
  }
</script>
