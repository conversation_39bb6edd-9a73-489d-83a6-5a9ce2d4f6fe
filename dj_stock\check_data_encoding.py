#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票数据编码检查工具

该脚本用于诊断和调试股票数据中的编码问题，特别是：
1. 检查数据库中的字段编码格式
2. 查找可能导致JSON序列化失败的数据
3. 分析K线图数据处理过程中的编码问题
4. 提供详细的二进制数据分析

使用场景：
- 当股票详情页面出现编码错误时
- 当JSON序列化失败时
- 当图表数据显示异常时

使用方法：
python check_data_encoding.py
"""

import os
import sys
import django
import binascii

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dj_stock.settings')
django.setup()

from market_data.models import StockBasic, StockDailyQuote
from datetime import datetime

def check_field_encoding(value, field_name):
    """检查字段的二进制表示，查找编码问题"""
    if value is None:
        return f"{field_name}: None"
    
    try:
        if isinstance(value, bytes):
            hex_value = binascii.hexlify(value).decode('ascii')
            return f"{field_name}: 二进制数据 - Hex: {hex_value[:40]}{'...' if len(hex_value) > 40 else ''}"
        elif isinstance(value, str):
            return f"{field_name}: 字符串 - '{value[:40]}{'...' if len(value) > 40 else ''}'"
        elif isinstance(value, (int, float)):
            return f"{field_name}: 数值 - {value}"
        elif isinstance(value, datetime):
            return f"{field_name}: 日期 - {value.strftime('%Y-%m-%d')}"
        else:
            return f"{field_name}: {type(value).__name__} - {str(value)[:40]}{'...' if len(str(value)) > 40 else ''}"
    except Exception as e:
        return f"{field_name}: 检查时出错 - {str(e)}"

def inspect_stock_data(stock_code):
    print(f"\n===== 检查股票 {stock_code} 的数据 =====")
    
    # 检查基本信息
    try:
        stock = StockBasic.objects.get(stock_code=stock_code)
        print(f"基本信息: {stock.stock_name} - {stock.industry} - {stock.market}")
    except StockBasic.DoesNotExist:
        print(f"股票 {stock_code} 基本信息不存在")
        return
    except Exception as e:
        print(f"获取基本信息时出错: {str(e)}")
    
    # 检查行情数据
    try:
        quotes = StockDailyQuote.objects.filter(stock_code=stock_code).order_by('-trade_date')
        print(f"找到 {quotes.count()} 条行情数据记录")
        
        if quotes.exists():
            # 检查最新的记录
            latest_quote = quotes.first()
            print("\n检查最新记录:")
            
            # 检查每个字段的二进制表示
            for field in StockDailyQuote._meta.fields:
                field_name = field.name
                try:
                    value = getattr(latest_quote, field_name)
                    print(check_field_encoding(value, field_name))
                except Exception as e:
                    print(f"{field_name}: 访问字段时出错 - {str(e)}")
            
            # 输出前5条记录的关键字段，查找模式
            print("\n检查前5条记录的关键字段:")
            for i, quote in enumerate(quotes[:5]):
                print(f"\n记录 #{i+1} - 日期: {quote.trade_date}")
                try:
                    # 检查可能有问题的字段
                    for field_name in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                        value = getattr(quote, field_name)
                        print(check_field_encoding(value, field_name))
                except Exception as e:
                    print(f"检查记录时出错: {str(e)}")
                    
            # 检查是否有异常数据
            print("\n查找可能存在问题的记录...")
            problematic_records = []
            
            for quote in quotes:
                try:
                    # 检查是否有任何一个价格字段无法转换为浮点数
                    has_problem = False
                    
                    for field_name in ['open_price', 'close_price', 'high_price', 'low_price']:
                        value = getattr(quote, field_name)
                        if value is not None:
                            try:
                                float(value)
                            except (ValueError, TypeError, UnicodeDecodeError):
                                has_problem = True
                                break
                    
                    if has_problem:
                        problematic_records.append(quote)
                        if len(problematic_records) >= 3:  # 限制显示的问题记录数量
                            break
                except Exception as e:
                    print(f"检查问题记录时出错: {str(e)}")
            
            if problematic_records:
                print(f"\n找到 {len(problematic_records)} 条可能存在问题的记录:")
                for i, quote in enumerate(problematic_records):
                    print(f"\n问题记录 #{i+1} - 日期: {quote.trade_date}")
                    for field_name in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                        try:
                            value = getattr(quote, field_name)
                            print(check_field_encoding(value, field_name))
                            
                            # 额外调试：打印二进制表示
                            if value is not None and not isinstance(value, (int, float, str, datetime)):
                                raw_bytes = str(value).encode('latin1')
                                hex_bytes = binascii.hexlify(raw_bytes).decode('ascii')
                                print(f"  - 二进制: {hex_bytes[:60]}...")
                        except Exception as e:
                            print(f"  {field_name}: 检查出错 - {str(e)}")
            else:
                print("未找到明显存在问题的记录")
                
    except Exception as e:
        print(f"检查行情数据时出错: {str(e)}")

def search_all_problematic_data():
    """在整个数据库中搜索可能有问题的记录"""
    print("\n===== 搜索所有可能存在编码问题的数据 =====")
    problematic_stocks = set()
    
    try:
        # 获取一个股票样本进行测试
        stocks = StockBasic.objects.all()[:50]  # 限制最多检查50只股票
        
        for stock in stocks:
            stock_code = stock.stock_code
            print(f"\n检查股票: {stock_code} - {stock.stock_name}")
            
            # 检查最新的几条记录
            quotes = StockDailyQuote.objects.filter(stock_code=stock_code).order_by('-trade_date')[:10]
            
            for quote in quotes:
                try:
                    has_problem = False
                    problem_fields = []
                    
                    # 检查每个价格字段
                    for field_name in ['open_price', 'close_price', 'high_price', 'low_price']:
                        value = getattr(quote, field_name)
                        if value is not None:
                            try:
                                float(value)
                            except (ValueError, TypeError, UnicodeDecodeError):
                                has_problem = True
                                problem_fields.append(field_name)
                    
                    if has_problem:
                        problematic_stocks.add(stock_code)
                        print(f"  - 发现问题：日期 {quote.trade_date}, 问题字段: {', '.join(problem_fields)}")
                        break
                except Exception as e:
                    print(f"  - 检查时出错: {str(e)}")
                    problematic_stocks.add(stock_code)
                    break
    
    except Exception as e:
        print(f"搜索问题数据时出错: {str(e)}")
    
    print(f"\n总共发现 {len(problematic_stocks)} 只可能存在问题的股票:")
    for code in problematic_stocks:
        print(f"- {code}")
    
    return problematic_stocks

def test_k_line_data(stock_code):
    """测试K线图数据准备过程，模拟视图函数的操作"""
    print(f"\n===== 测试股票 {stock_code} 的K线图数据 =====")
    
    try:
        # 获取股票历史数据
        history_data = StockDailyQuote.objects.filter(stock_code=stock_code).order_by('-trade_date')
        print(f"找到 {history_data.count()} 条历史数据")
        
        # 转换为列表并反转
        try:
            history_list = list(history_data)
            history_list.reverse()
            print(f"成功转换为列表并反转，长度: {len(history_list)}")
            
            # 测试数据处理流程
            kline_dates = []
            kline_values = []
            volumes = []
            
            for i, quote in enumerate(history_list):
                try:
                    # 只处理前10条用于测试
                    if i >= 10:
                        break
                        
                    # 格式化日期
                    if quote.trade_date:
                        date_str = quote.trade_date.strftime('%Y-%m-%d')
                        kline_dates.append(date_str)
                    else:
                        print(f"记录 #{i+1}: 无日期")
                        continue
                    
                    # 尝试处理价格数据
                    print(f"\n尝试处理记录 #{i+1} - 日期: {date_str}")
                    
                    # 安全转换价格字段
                    open_price, close_price, low_price, high_price = 0, 0, 0, 0
                    
                    try:
                        if quote.open_price is not None:
                            open_price = float(quote.open_price)
                        print(f"  - open_price: {open_price}")
                    except Exception as e:
                        print(f"  - open_price 转换失败: {str(e)}")
                    
                    try:
                        if quote.close_price is not None:
                            close_price = float(quote.close_price)
                        print(f"  - close_price: {close_price}")
                    except Exception as e:
                        print(f"  - close_price 转换失败: {str(e)}")
                    
                    try:
                        if quote.low_price is not None:
                            low_price = float(quote.low_price)
                        print(f"  - low_price: {low_price}")
                    except Exception as e:
                        print(f"  - low_price 转换失败: {str(e)}")
                    
                    try:
                        if quote.high_price is not None:
                            high_price = float(quote.high_price)
                        print(f"  - high_price: {high_price}")
                    except Exception as e:
                        print(f"  - high_price 转换失败: {str(e)}")
                    
                    # 添加价格数据到列表
                    kline_values.append([open_price, close_price, low_price, high_price])
                    
                    # 处理成交量
                    volume = 0
                    try:
                        if quote.volume is not None:
                            volume = int(float(quote.volume))
                        print(f"  - volume: {volume}")
                    except Exception as e:
                        print(f"  - volume 转换失败: {str(e)}")
                    
                    volumes.append(volume)
                
                except Exception as e:
                    print(f"处理记录 #{i+1} 时出错: {str(e)}")
                    
            # 尝试JSON序列化
            print("\n尝试对处理后的数据进行JSON序列化:")
            
            import json
            from decimal import Decimal
            
            class SafeJSONEncoder(json.JSONEncoder):
                def default(self, obj):
                    if isinstance(obj, Decimal):
                        return float(obj)
                    return super().default(obj)
            
            try:
                kline_dates_json = json.dumps(kline_dates, ensure_ascii=True)
                print(f"日期数据序列化成功，长度: {len(kline_dates_json)}")
            except Exception as e:
                print(f"日期数据序列化失败: {str(e)}")
            
            try:
                kline_values_json = json.dumps(kline_values, cls=SafeJSONEncoder, ensure_ascii=True)
                print(f"价格数据序列化成功，长度: {len(kline_values_json)}")
            except Exception as e:
                print(f"价格数据序列化失败: {str(e)}")
            
            try:
                volumes_json = json.dumps(volumes, cls=SafeJSONEncoder, ensure_ascii=True)
                print(f"成交量数据序列化成功，长度: {len(volumes_json)}")
            except Exception as e:
                print(f"成交量数据序列化失败: {str(e)}")
                
        except Exception as e:
            print(f"转换历史数据时出错: {str(e)}")
        
    except Exception as e:
        print(f"测试K线图数据时出错: {str(e)}")

if __name__ == "__main__":
    # 尝试检查不同股票的数据
    stock_codes = ["000001", "300230"]
    
    for code in stock_codes:
        inspect_stock_data(code)
        test_k_line_data(code)
    
    # 搜索所有可能有问题的数据
    problematic_stocks = search_all_problematic_data()
    
    # 如果有问题的股票，抽样几个进行深入检查
    if problematic_stocks:
        for code in list(problematic_stocks)[:2]:  # 最多检查2只股票
            print(f"\n对有问题的股票 {code} 进行深入检查:")
            test_k_line_data(code) 