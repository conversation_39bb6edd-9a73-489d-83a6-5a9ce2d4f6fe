from django.contrib import admin
from .models import (
    StockBasic,
    StockRiskWarning,
    StockIPO,
    StockMarketIndex,
    StockDailyQuote,
    HKStockConnect,
    StockBoardConcept,
    StockIndustryBoard,
    StockIndustryBoardRelation,
    StockBoardConceptRelation,
    StockDataStatus,
    StockTopList,
    StockMargin,
    StockComment,
    StockActiveBroker,
    StockLimitList,
    StockFundFlow,
    StockMarketFundFlow,
    StockSectorFundFlow,
)


@admin.register(StockBasic)
class StockBasicAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "industry",
        "market",
        "create_time",
        "update_time",
    ]
    search_fields = ["stock_code", "stock_name", "industry"]
    list_filter = ["industry", "market"]
    ordering = ["stock_code"]
    list_per_page = 20


@admin.register(StockRiskWarning)
class StockRiskWarningAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "warning_reason",
        "warning_date",
        "status",
    ]
    search_fields = ["stock_code", "stock_name", "warning_reason"]
    list_filter = ["status", "warning_date"]
    date_hierarchy = "warning_date"
    ordering = ["-warning_date"]
    list_per_page = 20


@admin.register(StockIPO)
class StockIPOAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "listing_date",
        "issue_price",
        "latest_price",
    ]
    search_fields = ["stock_code", "stock_name"]
    list_filter = ["listing_date"]
    date_hierarchy = "listing_date"
    ordering = ["-listing_date"]
    list_per_page = 20


@admin.register(StockMarketIndex)
class StockMarketIndexAdmin(admin.ModelAdmin):
    list_display = [
        "index_code",
        "index_name",
        "trade_date",
        "close_price",
    ]
    search_fields = ["index_code", "index_name"]
    list_filter = ["trade_date"]
    date_hierarchy = "trade_date"
    ordering = ["-trade_date", "index_code"]
    list_per_page = 20


@admin.register(StockDailyQuote)
class StockDailyQuoteAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "trade_date",
        "open_price",
        "close_price",
        "change_percent",
    ]
    search_fields = ["stock_code", "stock_name"]
    list_filter = ["trade_date"]
    date_hierarchy = "trade_date"
    ordering = ["-trade_date"]
    list_per_page = 20


@admin.register(HKStockConnect)
class HKStockConnectAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "date",
        "direction",
        "status",
    ]
    search_fields = ["stock_code", "stock_name"]
    list_filter = ["date", "direction", "status"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


# 注册行业板块模型
@admin.register(StockIndustryBoard)
class StockIndustryBoardAdmin(admin.ModelAdmin):
    list_display = [
        "board_code",
        "board_name",
        "date",
        "change_percent",
        "turnover_rate",
    ]
    search_fields = ["board_code", "board_name"]
    list_filter = ["date"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


# 注册概念板块模型
@admin.register(StockBoardConcept)
class StockBoardConceptAdmin(admin.ModelAdmin):
    list_display = [
        "board_code",
        "board_name",
        "date",
        "change_percent",
        "turnover_rate",
    ]
    search_fields = ["board_code", "board_name"]
    list_filter = ["date"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


# 注册行业板块关系模型
@admin.register(StockIndustryBoardRelation)
class StockIndustryBoardRelationAdmin(admin.ModelAdmin):
    list_display = ["stock_code", "stock_name", "board_code", "board_name", "rank"]
    search_fields = ["stock_code", "stock_name", "board_code", "board_name"]
    list_filter = ["board_code", "date"]
    ordering = ["board_code", "stock_code"]
    list_per_page = 20


@admin.register(StockBoardConceptRelation)
class StockBoardConceptRelationAdmin(admin.ModelAdmin):
    list_display = ["stock_code", "stock_name", "board_code", "board_name"]
    search_fields = ["stock_code", "stock_name", "board_code", "board_name"]
    list_filter = ["board_code", "date"]
    ordering = ["board_code", "stock_code"]
    list_per_page = 20


@admin.register(StockDataStatus)
class StockDataStatusAdmin(admin.ModelAdmin):
    """股票数据状态管理"""

    list_display = (
        "stock_code",
        "status",
        "is_complete",
        "date_from",
        "date_to",
        "record_count",
        "last_update",
    )
    list_filter = ("status", "is_complete")
    search_fields = ("stock_code",)
    ordering = ("-last_update",)
    list_per_page = 20

    actions = ["mark_for_update"]

    def mark_for_update(self, request, queryset):
        """标记需要更新的数据"""
        queryset.update(status="never_fetched", is_complete=False)
        self.message_user(request, f"已将 {queryset.count()} 条记录标记为需要更新")

    mark_for_update.short_description = "标记为需要更新"


@admin.register(StockTopList)
class StockTopListAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "date",
        "close_price",
        "change_ratio",
        "net_buy",
        "total_turnover",
        "turnover_rate",
    ]
    search_fields = ["stock_code", "stock_name"]
    list_filter = ["date"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


@admin.register(StockMargin)
class StockMarginAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "trade_date",
        "margin_balance",
        "margin_buy",
        "margin_repay",
        "short_balance",
        "total_balance",
    ]
    search_fields = ["stock_code", "stock_name"]
    list_filter = ["trade_date", "exchange"]
    date_hierarchy = "trade_date"
    ordering = ["-trade_date"]
    list_per_page = 20


@admin.register(StockComment)
class StockCommentAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "date",
        "current_price",
        "change_ratio",
        "comprehensive_score",
        "current_rank",
        "rise_rank",
    ]
    search_fields = ["stock_code", "stock_name"]
    list_filter = ["date"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


@admin.register(StockActiveBroker)
class StockActiveBrokerAdmin(admin.ModelAdmin):
    list_display = [
        "broker_name",
        "trade_date",
        "buy_stock_count",
        "sell_stock_count",
        "buy_amount",
        "sell_amount",
        "net_amount",
    ]
    search_fields = ["broker_name"]
    list_filter = ["trade_date"]
    date_hierarchy = "trade_date"
    ordering = ["-trade_date"]
    list_per_page = 20


@admin.register(StockLimitList)
class StockLimitListAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "date",
        "limit_type",
        "change_ratio",
        "latest_price",
        "continuous_limit",
        "break_count",
        "last_time",
    ]
    search_fields = ["stock_code", "stock_name"]
    list_filter = ["date", "limit_type", "industry"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


@admin.register(StockFundFlow)
class StockFundFlowAdmin(admin.ModelAdmin):
    list_display = [
        "code",
        "name",
        "date",
        "type",
        "main_net_inflow",
        "retail_net_inflow",
        "total_net_inflow",
    ]
    search_fields = ["code", "name"]
    list_filter = ["date", "type"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


@admin.register(StockMarketFundFlow)
class StockMarketFundFlowAdmin(admin.ModelAdmin):
    list_display = [
        "trade_date",
        "sh_index_close",
        "sh_index_change_pct",
        "sz_index_close",
        "sz_index_change_pct",
        "main_net_inflow",
        "main_net_inflow_pct",
    ]
    list_filter = ["trade_date"]
    date_hierarchy = "trade_date"
    ordering = ["-trade_date"]
    list_per_page = 20


@admin.register(StockSectorFundFlow)
class StockSectorFundFlowAdmin(admin.ModelAdmin):
    list_display = [
        "sector_name",
        "sector_type",
        "trade_date",
        "rank",
        "net_inflow_rate",
        "net_inflow_amount",
        "main_net_inflow_pct",
    ]
    search_fields = ["sector_name"]
    list_filter = ["trade_date", "sector_type"]
    date_hierarchy = "trade_date"
    ordering = ["-trade_date"]
    list_per_page = 20
