# -*- coding: utf-8 -*-
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
import logging

from .base_views import (
    StockTopList,
    StockMargin,
    StockActiveBroker,
    StockComment,
    HKStockConnect,
    get_pagination,
    StockDailyQuote,
)

logger = logging.getLogger(__name__)


def top_list(request):
    """龙虎榜列表页面"""
    # 获取查询参数
    date = request.GET.get("date")
    stock_code = request.GET.get("stock_code")
    stock_name = request.GET.get("stock_name")

    # 构建查询
    queryset = StockTopList.objects.all()

    # 应用过滤条件
    if date:
        queryset = queryset.filter(date=date)
    if stock_code:
        queryset = queryset.filter(stock_code__icontains=stock_code)
    if stock_name:
        queryset = queryset.filter(stock_name__icontains=stock_name)

    # 排序
    queryset = queryset.order_by("-date")

    # 分页
    page = request.GET.get("page", 1)
    top_lists = get_pagination(queryset, page, per_page=20)

    # 将成交额和净买额转换为万元
    for top_list in top_lists:
        top_list.net_buy = top_list.net_buy / 10000
        top_list.total_turnover = top_list.total_turnover / 10000

    context = {
        "page_obj": top_lists,
        "date": date,
        "stock_code": stock_code,
        "stock_name": stock_name,
    }
    return render(request, "market_data/top_list.html", context)


def top_list_detail(request, pk):
    """龙虎榜详情页面"""
    top_list = get_object_or_404(StockTopList, pk=pk)

    # 将龙虎榜净买额、龙虎榜成交额、龙虎榜买入额、市场总成交额、龙虎榜卖出额单位转换
    top_list.net_buy = top_list.net_buy / 10000
    top_list.total_turnover = top_list.total_turnover / 10000
    top_list.buy_amount = top_list.buy_amount / 10000
    top_list.market_total_turnover = top_list.market_total_turnover / 10000
    top_list.sell_amount = top_list.sell_amount / 10000

    context = {"top_list": top_list}
    return render(request, "market_data/top_list_detail.html", context)


def margin_list(request):
    """融资融券列表视图"""
    # 获取查询参数
    date = request.GET.get("date", "")
    stock_code = request.GET.get("stock_code", "")
    stock_name = request.GET.get("stock_name", "")

    # 构建查询
    margins = StockMargin.objects.all()

    # 应用过滤条件
    if date:
        margins = margins.filter(trade_date=date)
    if stock_code:
        margins = margins.filter(stock_code__icontains=stock_code)
    if stock_name:
        margins = margins.filter(stock_name__icontains=stock_name)

    # 排序
    margins = margins.order_by("-trade_date", "stock_code")

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(margins, page, per_page=20)

    context = {
        "page_obj": page_obj,
        "date": date,
        "stock_code": stock_code,
        "stock_name": stock_name,
    }

    return render(request, "market_data/margin_list.html", context)


def margin_detail(request, pk):
    """融资融券详情视图"""
    margin = get_object_or_404(StockMargin, pk=pk)

    context = {
        "margin": margin,
    }

    return render(request, "market_data/margin_detail.html", context)


def comment_list(request):
    """千股千评列表视图"""
    # 获取查询参数
    date = request.GET.get("date")
    stock_code = request.GET.get("stock_code")
    stock_name = request.GET.get("stock_name")
    sort_by = request.GET.get("sort", "-comprehensive_score")  # 默认按综合评分排序

    # 构建查询
    queryset = StockComment.objects.all()

    # 应用过滤条件
    if date:
        queryset = queryset.filter(date=date)
    if stock_code:
        queryset = queryset.filter(stock_code__icontains=stock_code)
    if stock_name:
        queryset = queryset.filter(stock_name__icontains=stock_name)

    # 排序
    if sort_by:
        queryset = queryset.order_by(sort_by)
    else:
        queryset = queryset.order_by("-date", "-comprehensive_score")

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(queryset, page, per_page=20)

    # 获取可用日期列表
    available_dates = StockComment.objects.dates("date", "day", order="DESC")[:30]

    context = {
        "page_obj": page_obj,
        "date": date,
        "stock_code": stock_code,
        "stock_name": stock_name,
        "sort_by": sort_by,
        "available_dates": available_dates,
    }
    return render(request, "market_data/comment_list.html", context)


def comment_detail(request, code):
    """千股千评详情视图"""
    # 获取查询参数
    date = request.GET.get("date")

    # 获取可用日期列表
    available_dates = StockComment.objects.filter(stock_code=code).dates(
        "date", "day", order="DESC"
    )[:30]

    # 如果没有指定日期，使用最新日期
    if not date and available_dates:
        date = available_dates[0].strftime("%Y-%m-%d")

    # 获取评论数据
    comment = get_object_or_404(StockComment, stock_code=code, date=date)

    # 查询同一行业的评分对比
    industry = None
    from .base_views import StockBasic

    stock = StockBasic.objects.filter(stock_code=code).first()
    if stock and stock.industry:
        industry = stock.industry
        industry_stocks = StockBasic.objects.filter(industry=industry)
        industry_codes = [s.stock_code for s in industry_stocks]

        # 获取同行业股票的评分
        industry_comments = StockComment.objects.filter(
            stock_code__in=industry_codes, date=date
        ).order_by("-comprehensive_score")[:10]
    else:
        industry_comments = []

    context = {
        "comment": comment,
        "available_dates": available_dates,
        "selected_date": date,
        "industry": industry,
        "industry_comments": industry_comments,
    }
    return render(request, "market_data/comment_detail.html", context)


def hk_stock_connect(request):
    """港股通成份股视图"""
    # 获取查询参数
    date = request.GET.get("date")
    stock_code = request.GET.get("stock_code")
    stock_name = request.GET.get("stock_name")
    direction = request.GET.get("direction")  # 沪股通/深股通

    # 构建查询
    queryset = HKStockConnect.objects.all()

    # 应用过滤条件
    if date:
        queryset = queryset.filter(date=date)
    if stock_code:
        queryset = queryset.filter(stock_code__icontains=stock_code)
    if stock_name:
        queryset = queryset.filter(stock_name__icontains=stock_name)
    if direction:
        queryset = queryset.filter(direction=direction)

    # 获取统计数据
    total_count = queryset.count()
    sh_count = queryset.filter(direction="沪股通").count()
    sz_count = queryset.filter(direction="深股通").count()

    # 获取最新纳入日期
    latest_date = queryset.order_by("-date").values("date").first()
    latest_date = latest_date["date"] if latest_date else None

    # 排序
    queryset = queryset.order_by("-date", "stock_code")

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(queryset, page, per_page=20)

    # 为每只股票添加涨跌幅信息
    for item in page_obj:
        try:
            # 获取最新行情数据
            quote = (
                StockDailyQuote.objects.filter(stock_code=item.stock_code)
                .order_by("-trade_date")
                .first()
            )

            if quote:
                item.change_percent = quote.change_percent
                item.close_price = quote.close_price
                item.trade_date = quote.trade_date
            else:
                item.change_percent = None
                item.close_price = None
                item.trade_date = None
        except Exception as e:
            logger.error(f"获取股票 {item.stock_code} 行情数据失败: {str(e)}")
            item.change_percent = None
            item.close_price = None
            item.trade_date = None

    context = {
        "page_obj": page_obj,
        "date": date,
        "stock_code": stock_code,
        "stock_name": stock_name,
        "direction": direction,
        "total_count": total_count,
        "sh_count": sh_count,
        "sz_count": sz_count,
        "latest_date": latest_date,
    }

    return render(request, "market_data/hk_stock_connect.html", context)
