{% extends 'base.html' %}

{% block title %}{{ broker_name }} - 营业部详情{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">
          营业部详情
        </div>
        <h2 class="page-title">
          {{ broker_name }}
        </h2>
      </div>
      <div class="col-auto ms-auto">
        <div class="btn-list">
          <a href="{% url 'market_data:active_broker_list' %}" class="btn btn-outline-primary d-none d-sm-inline-block">
            <i class="ti ti-arrow-left"></i>
            返回活跃营业部列表
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="page-body">
  <div class="container-xl">
    <div class="row">
      <div class="col-md-4">
        <div class="card mb-3">
          <div class="card-header">
            <h3 class="card-title">营业部统计</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <div class="form-label">总交易次数</div>
                <div class="h3">{{ total_records }}</div>
              </div>
              <div class="col-md-6 mb-3">
                <div class="form-label">总净买入额(万)</div>
                <div class="h3 {% if total_net_amount > 0 %}text-danger{% elif total_net_amount < 0 %}text-success{% endif %}">
                  {{ total_net_amount|floatformat:2 }}
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <div class="form-label">总买入额(万)</div>
                <div class="h3 text-danger">{{ total_buy_amount|floatformat:2 }}</div>
              </div>
              <div class="col-md-6 mb-3">
                <div class="form-label">总卖出额(万)</div>
                <div class="h3 text-success">{{ total_sell_amount|floatformat:2 }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">筛选</h3>
          </div>
          <div class="card-body">
            <form method="get">
              <div class="mb-3">
                <label class="form-label">选择日期</label>
                <select name="date" class="form-select">
                  <option value="">全部日期</option>
                  {% for date_obj in available_dates %}
                  <option value="{{ date_obj|date:'Y-m-d' }}" {% if date == date_obj|date:'Y-m-d' %}selected{% endif %}>
                    {{ date_obj|date:"Y-m-d" }}
                  </option>
                  {% endfor %}
                </select>
              </div>
              <button type="submit" class="btn btn-primary">
                <i class="ti ti-filter me-1"></i>
                筛选
              </button>
            </form>
          </div>
        </div>
      </div>
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">交易明细</h3>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter table-hover table-striped card-table">
                <thead>
                  <tr>
                    <th>交易日期</th>
                    <th>买入金额(万)</th>
                    <th>卖出金额(万)</th>
                    <th>净买入(万)</th>
                    <th>买入个股</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in page_obj %}
                  <tr>
                    <td>{{ item.trade_date|date:"Y-m-d" }}</td>
                    <td class="text-danger fw-bold">{{ item.buy_amount|floatformat:2 }}</td>
                    <td class="text-success fw-bold">{{ item.sell_amount|floatformat:2 }}</td>
                    <td class="{% if item.net_amount > 0 %}text-danger fw-bold{% elif item.net_amount < 0 %}text-success fw-bold{% endif %}">
                      {{ item.net_amount|floatformat:2 }}
                    </td>
                    <td>
                      <div class="stock-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(110px, 1fr)); gap: 8px; max-width: 100%;">
                        {% for stock in item.parsed_buy_stocks %}
                          <a href="{% url 'market_data:stock_detail' stock.code %}" 
                             class="stock-card" 
                             style="display: block; background: {% if stock.change_percent > 0 %}rgba(255,235,235,1){% elif stock.change_percent < 0 %}rgba(235,255,235,1){% else %}#f0f0f0{% endif %}; 
                                    border-radius: 4px; padding: 6px; text-decoration: none; color: #333; position: relative;
                                    border-left: 3px solid {% if stock.change_percent > 0 %}#dc3545{% elif stock.change_percent < 0 %}#28a745{% else %}#6c757d{% endif %};">
                            
                            {% if stock.is_recent %}
                              <span style="position: absolute; top: 2px; right: 2px; width: 6px; height: 6px; border-radius: 50%; background-color: #f59f00;" 
                                    data-bs-toggle="tooltip" title="非当日数据"></span>
                            {% endif %}
                            
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 3px;">
                              <span class="fw-bold" style="font-size: 0.8rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 60px;">{{ stock.code }}</span>
                              {% if stock.change_percent is not None %}
                                <span class="badge {% if stock.change_percent > 0 %}bg-danger{% elif stock.change_percent < 0 %}bg-success{% else %}bg-secondary{% endif %} text-white" style="font-size: 0.7rem;">
                                  {% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:2 }}%
                                </span>
                              {% endif %}
                            </div>
                            
                            <span style="display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 0.8rem; margin-bottom: 2px;">{{ stock.name|default:'未知' }}</span>
                            
                            {% if stock.close_price %}
                              <span style="display: block; font-size: 0.75rem; color: #666;">价格: {{ stock.close_price|floatformat:2 }}</span>
                            {% endif %}
                          </a>
                        {% empty %}
                          <div style="grid-column: span 3; text-align: center; color: #666; padding: 10px;">
                            {% if item.buy_stock_count > 0 %}
                              买入了 {{ item.buy_stock_count }} 只股票，但无详细信息
                            {% else %}
                              {{ item.buy_stocks|default:'无买入股票数据' }}
                            {% endif %}
                          </div>
                        {% endfor %}
                      </div>
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="5" class="text-center">暂无交易数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% include "includes/pagination.html" with page_obj=page_obj %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });
</script>
{% endblock %}
{% endblock %} 