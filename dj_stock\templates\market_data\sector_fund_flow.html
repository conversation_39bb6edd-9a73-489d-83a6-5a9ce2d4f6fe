{% extends 'base.html' %}

{% block title %}行业资金流向 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">{{ sector_type }}资金流向</h2>
        <div class="text-muted mt-1">查看各{{ sector_type }}的资金流向数据</div>
      </div>

    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 总净流入统计卡片 -->
    <div class="row mb-3">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row">
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">总净流入</div>
                  <div class="h4 mb-0 number-display">
                    {% if total_net_inflow > 0 %}
                    <span class="text-up">+{{ total_net_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_net_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">超大单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_super_big_inflow > 0 %}
                    <span class="text-up">+{{ total_super_big_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_super_big_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">大单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_big_inflow > 0 %}
                    <span class="text-up">+{{ total_big_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_big_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">中单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_medium_inflow > 0 %}
                    <span class="text-up">+{{ total_medium_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_medium_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">小单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_small_inflow > 0 %}
                    <span class="text-up">+{{ total_small_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_small_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">板块统计</div>
                  <div class="h6 mb-0 number-display">
                    <span class="text-up">{{ inflow_sectors_count }}</span> /
                    <span class="text-down">{{ outflow_sectors_count }}</span>
                  </div>
                  <div class="text-muted small">流入/流出</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行业资金流向表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">{{ sector_type }}资金流向数据 ({{ selected_date|date:"Y-m-d" }})</h3>
        <div class="col-auto ms-auto d-print-none">
          <div class="d-flex">
            <div class="me-2">
              <form method="get" class="d-flex">
                <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
                <select class="form-select ms-2" name="sector_type">
                  {% for type in sector_types %}
                  <option value="{{ type }}" {% if sector_type == type %}selected{% endif %}>{{ type }}</option>
                  {% endfor %}
                </select>
                <button type="submit" class="btn ms-2">查询</button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="card-body border-bottom">
        <form method="get" class="row g-3">
          <!-- 保持现有参数 -->
          <input type="hidden" name="date" value="{{ selected_date|date:'Y-m-d' }}">
          <input type="hidden" name="sector_type" value="{{ sector_type }}">

          <div class="col-md-2">
            <label class="form-label">搜索板块</label>
            <input type="text" class="form-control" name="q" value="{{ search_query }}" placeholder="板块名称">
          </div>

          <div class="col-md-2">
            <label class="form-label">净流入范围(亿)</label>
            <div class="input-group">
              <input type="number" class="form-control" name="min_inflow" value="{{ min_inflow }}" placeholder="最小" step="0.1">
              <span class="input-group-text">~</span>
              <input type="number" class="form-control" name="max_inflow" value="{{ max_inflow }}" placeholder="最大" step="0.1">
            </div>
          </div>

          <div class="col-md-2">
            <label class="form-label">涨跌幅范围(%)</label>
            <div class="input-group">
              <input type="number" class="form-control" name="min_rate" value="{{ min_rate }}" placeholder="最小" step="0.1">
              <span class="input-group-text">~</span>
              <input type="number" class="form-control" name="max_rate" value="{{ max_rate }}" placeholder="最大" step="0.1">
            </div>
          </div>

          <div class="col-md-2">
            <label class="form-label">排序方式</label>
            <select class="form-select" name="sort">
              <option value="-net_inflow_amount" {% if sort_by == '-net_inflow_amount' %}selected{% endif %}>主力净流入↓</option>
              <option value="net_inflow_amount" {% if sort_by == 'net_inflow_amount' %}selected{% endif %}>主力净流入↑</option>
              <option value="-net_inflow_rate" {% if sort_by == '-net_inflow_rate' %}selected{% endif %}>涨跌幅↓</option>
              <option value="net_inflow_rate" {% if sort_by == 'net_inflow_rate' %}selected{% endif %}>涨跌幅↑</option>
              <option value="-main_net_inflow_pct" {% if sort_by == '-main_net_inflow_pct' %}selected{% endif %}>净占比↓</option>
              <option value="main_net_inflow_pct" {% if sort_by == 'main_net_inflow_pct' %}selected{% endif %}>净占比↑</option>
            </select>
          </div>

          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">筛选</button>
              <a href="{% url 'market_data:sector_fund_flow' %}?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}" class="btn btn-outline-secondary">重置</a>
            </div>
          </div>
        </form>

        <!-- 快捷筛选按钮 -->
        <div class="mt-3">
          <div class="d-flex flex-wrap gap-2">
            <span class="text-muted me-2">快捷筛选：</span>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_inflow=1&sort=-net_inflow_amount" class="btn btn-sm btn-outline-success">净流入>1亿</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_inflow=5&sort=-net_inflow_amount" class="btn btn-sm btn-outline-success">净流入>5亿</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_inflow=10&sort=-net_inflow_amount" class="btn btn-sm btn-outline-success">净流入>10亿</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_rate=3&sort=-net_inflow_rate" class="btn btn-sm btn-outline-danger">涨幅>3%</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_rate=5&sort=-net_inflow_rate" class="btn btn-sm btn-outline-danger">涨幅>5%</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&max_inflow=-1&sort=net_inflow_amount" class="btn btn-sm btn-outline-warning">净流出>1亿</a>
          </div>
        </div>
      </div>


      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>排名</th>
              <th>板块名称</th>
              <th>涨跌幅</th>
              <th>主力净流入</th>
              <th>主力净占比</th>
              <th>超大单净流入</th>
              <th>大单净流入</th>
              <th>中单净流入</th>
              <th>小单净流入</th>
              <th>领涨股</th>
            </tr>
          </thead>
          <tbody>
            {% for sector in sectors %}
            <tr>
              <td>{{ sector.rank }}</td>
              <td>
                <a href="{% url 'market_data:sector_fund_flow_detail' sector.sector_name %}?sector_type={{ sector_type }}" class="text-decoration-none">
                  {{ sector.sector_name }}
                </a>
              </td>
              <td>
                {% if sector.net_inflow_rate > 0 %}
                <span class="text-up">+{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% elif sector.net_inflow_rate < 0 %}
                <span class="text-down">{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% else %}
                <span class="text-muted">{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if sector.net_inflow_amount > 0 %}
                <span class="text-up">+{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% elif sector.net_inflow_amount < 0 %}
                <span class="text-down">{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.main_net_inflow_pct > 0 %}
                <span class="text-up">+{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% elif sector.main_net_inflow_pct < 0 %}
                <span class="text-down">{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="text-muted">{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if sector.super_big_net_inflow > 0 %}
                <span class="text-up">+{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.super_big_net_inflow < 0 %}
                <span class="text-down">{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.big_net_inflow > 0 %}
                <span class="text-up">+{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.big_net_inflow < 0 %}
                <span class="text-down">{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.medium_net_inflow > 0 %}
                <span class="text-up">+{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.medium_net_inflow < 0 %}
                <span class="text-down">{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.small_net_inflow > 0 %}
                <span class="text-up">+{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.small_net_inflow < 0 %}
                <span class="text-down">{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>{{ sector.max_net_inflow_stock }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="10" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前日期没有行业资金流向数据，请尝试选择其他日期。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% include "includes/pagination.html" with page_obj=sectors rows_per_page=rows_per_page show_flow_indicator=True %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  /* 优化统计卡片样式 */
  .stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  /* 优化数值显示 */
  .number-display {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  /* 表格行悬停效果 */
  .table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    transition: all 0.2s ease;
  }

  /* 优化表格数值列 */
  .table td:nth-child(n+3) {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // 添加数值格式化动画
    const numberElements = document.querySelectorAll('.number-display');
    numberElements.forEach(el => {
      el.style.opacity = '0';
      el.style.transform = 'translateY(10px)';
      setTimeout(() => {
        el.style.transition = 'all 0.5s ease';
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
      }, 100);
    });
  });
</script>
{% endblock %}
