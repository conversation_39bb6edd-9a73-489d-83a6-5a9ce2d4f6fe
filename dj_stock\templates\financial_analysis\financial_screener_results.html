<!-- 股票列表 -->
<div class="table-card">
    <div class="table-header d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <i class="ti ti-table me-2"></i>
            <h6 class="mb-0">筛选结果</h6>
        </div>
        <div class="d-flex align-items-center stats-info">
            <div class="badge bg-white text-primary me-3">
                <i class="ti ti-chart-bar me-1"></i>
                {{ page_obj.paginator.count }}支股票
            </div>
            <div class="text-white small me-3" style="font-size: 0.85rem;">
                <i class="ti ti-calendar me-1"></i>
                行情：{{ latest_trade_date|date:"Y-m-d" }}
            </div>
            <div class="text-white small" style="font-size: 0.85rem;">
                <i class="ti ti-report-money me-1"></i>
                财报：{{ latest_report_date|date:"Y-m-d" }}
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-hover table-striped table-sm" style="font-size: 0.75rem;">
            <thead class="table-light">
                <tr>
                    <th width="60px" class="text-center">收藏</th>
                    <th width="90px"><a href="?sort=stock_code&order={% if sort_by == 'stock_code' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">代码 <i class="sort-icon {% if sort_by == 'stock_code' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="120px"><a href="?sort=stock_name&order={% if sort_by == 'stock_name' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">名称 <i class="sort-icon {% if sort_by == 'stock_name' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="100px"><a href="?sort=industry&order={% if sort_by == 'industry' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">行业 <i class="sort-icon {% if sort_by == 'industry' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="90px"><a href="?sort=latest_price&order={% if sort_by == 'latest_price' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">当前价(元) <i class="sort-icon {% if sort_by == 'latest_price' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="90px"><a href="?sort=change_percent&order={% if sort_by == 'change_percent' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">涨跌幅(%) <i class="sort-icon {% if sort_by == 'change_percent' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="90px"><a href="?sort=market_cap&order={% if sort_by == 'market_cap' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">市值(亿) <i class="sort-icon {% if sort_by == 'market_cap' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="80px"><a href="?sort=pe_ratio&order={% if sort_by == 'pe_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PE(倍) <i class="sort-icon {% if sort_by == 'pe_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="80px"><a href="?sort=pb_ratio&order={% if sort_by == 'pb_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PB(倍) <i class="sort-icon {% if sort_by == 'pb_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="80px"><a href="?sort=roe&order={% if sort_by == 'roe' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">ROE(%) <i class="sort-icon {% if sort_by == 'roe' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="100px"><a href="?sort=net_profit_growth&order={% if sort_by == 'net_profit_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">净利增长(%) <i class="sort-icon {% if sort_by == 'net_profit_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="100px"><a href="?sort=revenue_growth&order={% if sort_by == 'revenue_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">营收增长(%) <i class="sort-icon {% if sort_by == 'revenue_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="90px"><a href="?sort=gross_profit_margin&order={% if sort_by == 'gross_profit_margin' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">毛利率(%) <i class="sort-icon {% if sort_by == 'gross_profit_margin' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="90px"><a href="?sort=debt_ratio&order={% if sort_by == 'debt_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">负债率(%) <i class="sort-icon {% if sort_by == 'debt_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                    <th width="80px">连续ROE</th>
                </tr>
            </thead>
            <tbody>
                {% for stock in page_obj %}
                <tr class="align-middle">
                    <td class="text-center">
                        <button class="btn btn-sm btn-warning favorite-btn"
                                data-code="{{ stock.stock_code }}"
                                data-name="{{ stock.stock_name }}"
                                title="添加到收藏">
                            <i class="ti ti-star" style="font-size: 0.8rem;"></i>
                        </button>
                        <span class="favorite-icon d-none" data-code="{{ stock.stock_code }}">
                            <i class="ti ti-star-filled text-warning" style="font-size: 0.8rem;"></i>
                        </span>
                    </td>
                    <td><a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-primary fw-semibold">{{ stock.stock_code }}</a></td>
                    <td><span class="fw-medium">{{ stock.stock_name|truncatechars:12 }}</span></td>
                    <td><span class="badge bg-light text-dark" style="font-size: 0.7rem;">{{ stock.industry|truncatechars:8 }}</span></td>
                    <td>
                        {% if stock.latest_price %}
                            <span class="fw-medium">{{ stock.latest_price|floatformat:2 }}</span>
                        {% else %}-{% endif %}
                    </td>
                    <td>
                        {% if stock.change_percent != None %}
                            <span class="badge {% if stock.change_percent > 0 %}bg-danger-subtle text-danger{% elif stock.change_percent < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                {% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:1 }}%
                            </span>
                        {% else %}-{% endif %}
                    </td>
                    <td>{% if stock.market_cap %}{{ stock.market_cap|floatformat:0 }}{% else %}-{% endif %}</td>
                    <td>{% if stock.pe_ratio %}{{ stock.pe_ratio|floatformat:1 }}{% else %}-{% endif %}</td>
                    <td>{% if stock.pb_ratio %}{{ stock.pb_ratio|floatformat:1 }}{% else %}-{% endif %}</td>
                    <td>
                        {% if stock.roe != None %}
                            <span class="fw-medium {% if stock.roe > 15 %}text-danger{% elif stock.roe > 8 %}text-warning{% else %}text-muted{% endif %}">
                                {{ stock.roe|floatformat:1 }}
                            </span>
                        {% else %}-{% endif %}
                    </td>
                    <td>
                        {% if stock.net_profit_growth != None %}
                            <span class="badge {% if stock.net_profit_growth > 0 %}bg-danger-subtle text-danger{% elif stock.net_profit_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                {% if stock.net_profit_growth > 0 %}+{% endif %}{{ stock.net_profit_growth|floatformat:0 }}%
                            </span>
                        {% else %}-{% endif %}
                    </td>
                    <td>
                        {% if stock.revenue_growth != None %}
                            <span class="badge {% if stock.revenue_growth > 0 %}bg-danger-subtle text-danger{% elif stock.revenue_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                {% if stock.revenue_growth > 0 %}+{% endif %}{{ stock.revenue_growth|floatformat:0 }}%
                            </span>
                        {% else %}-{% endif %}
                    </td>
                    <td>
                        {% if stock.gross_profit_margin != None %}
                            <span class="fw-medium {% if stock.gross_profit_margin > 30 %}text-danger{% elif stock.gross_profit_margin > 15 %}text-warning{% else %}text-muted{% endif %}">
                                {{ stock.gross_profit_margin|floatformat:1 }}
                            </span>
                        {% else %}-{% endif %}
                    </td>
                    <td>
                        {% if stock.debt_ratio != None %}
                            <span class="fw-medium {% if stock.debt_ratio > 60 %}text-danger{% elif stock.debt_ratio > 40 %}text-warning{% else %}text-success{% endif %}">
                                {{ stock.debt_ratio|floatformat:1 }}
                            </span>
                        {% else %}-{% endif %}
                    </td>
                    <td class="text-center">
                        {% if stock.continuous_roe %}
                            <span class="badge bg-success-subtle text-success" style="font-size: 0.7rem;">✓</span>
                        {% else %}
                            <span class="badge bg-secondary-subtle text-secondary" style="font-size: 0.7rem;">✗</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="15" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center">
                            <i class="ti ti-search-off fs-1 text-muted mb-3"></i>
                            <p class="mb-1 fw-medium">没有找到符合条件的股票</p>
                            <p class="text-muted small">请尝试调整筛选条件</p>
                        </div>
                    </td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

    <!-- 分页 -->
    <div class="mt-4 p-3 pagination-container" style="background: #f8f9fa; border-radius: 0 0 12px 12px;">
        {% include "includes/pagination.html" with page_obj=page_obj rows_per_page=rows_per_page %}
    </div>
</div>
