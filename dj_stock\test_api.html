<!DOCTYPE html>
<html>
  <head>
    <title>API测试页面</title>
    <script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
  </head>
  <body>
    <h1>市场资金流向趋势图测试</h1>

    <div>
      <h2>统计指标</h2>
      <div id="metrics">
        <p>累计净流入: <span id="total-inflow">加载中...</span></p>
        <p>日均净流入: <span id="avg-inflow">加载中...</span></p>
        <p>最大单日流入: <span id="max-inflow">加载中...</span></p>
        <p>净流入天数: <span id="positive-days">加载中...</span></p>
      </div>
    </div>

    <div>
      <h2>主力资金流向趋势图</h2>
      <div id="fund-flow-chart" style="width: 100%; height: 400px; border: 1px solid #ccc"></div>
    </div>

    <div>
      <h2>资金类型分布图</h2>
      <div id="fund-type-chart" style="width: 100%; height: 400px"></div>
    </div>

    <div>
      <h2>指数与资金流向关系图</h2>
      <div id="index-fund-chart" style="width: 100%; height: 400px"></div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        console.log('页面加载完成，开始测试API')

        // 测试API接口
        fetch('/market_data/api/market-fund-flow-trend/?days=30')
          .then((response) => {
            console.log('API响应状态:', response.status)
            return response.json()
          })
          .then((result) => {
            console.log('API返回数据:', result)

            if (result.success) {
              updateMetrics(result)
              initFundFlowChart(result.data)
              initFundTypeChart(result.summary.fund_type_summary)
              initIndexFundChart(result.data)
            } else {
              console.error('API返回错误:', result.error)
              document.getElementById('metrics').innerHTML = '<p style="color: red;">API错误: ' + result.error + '</p>'
            }
          })
          .catch((error) => {
            console.error('网络请求失败:', error)
            document.getElementById('metrics').innerHTML = '<p style="color: red;">网络请求失败: ' + error.message + '</p>'
          })
      })

      function updateMetrics(result) {
        const summary = result.summary

        document.getElementById('total-inflow').innerHTML = `<span style="color: ${summary.total_main_inflow >= 0 ? 'red' : 'green'}">
                    ${summary.total_main_inflow}亿
                </span>`

        document.getElementById('avg-inflow').innerHTML = `<span style="color: ${summary.avg_main_inflow >= 0 ? 'red' : 'green'}">
                    ${summary.avg_main_inflow}亿
                </span>`

        const maxInflow = Math.max(...result.data.map((item) => item.main_net_inflow))
        document.getElementById('max-inflow').innerHTML = `<span style="color: red">${maxInflow.toFixed(2)}亿</span>`

        const positiveDays = result.data.filter((item) => item.main_net_inflow > 0).length
        document.getElementById('positive-days').innerHTML = `<span style="color: blue">${positiveDays}天</span>`
      }

      function initFundFlowChart(data) {
        const chart = echarts.init(document.getElementById('fund-flow-chart'))

        const dates = data.map((item) => item.trade_date)
        const mainInflows = data.map((item) => item.main_net_inflow)

        const option = {
          title: {
            text: '主力资金流向趋势',
          },
          tooltip: {
            trigger: 'axis',
          },
          xAxis: {
            type: 'category',
            data: dates,
          },
          yAxis: {
            type: 'value',
            name: '资金流入(亿元)',
          },
          series: [
            {
              name: '主力净流入',
              type: 'line',
              data: mainInflows,
              itemStyle: {
                color: function (params) {
                  return params.value >= 0 ? '#ef4444' : '#22c55e'
                },
              },
            },
          ],
        }

        chart.setOption(option)
      }

      function initFundTypeChart(summary) {
        const chart = echarts.init(document.getElementById('fund-type-chart'))

        const option = {
          title: {
            text: '资金类型分布',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}亿 ({d}%)',
          },
          series: [
            {
              name: '资金类型',
              type: 'pie',
              radius: '50%',
              data: [
                { value: Math.abs(summary.super_big), name: '超大单' },
                { value: Math.abs(summary.big), name: '大单' },
                { value: Math.abs(summary.medium), name: '中单' },
                { value: Math.abs(summary.small), name: '小单' },
              ],
            },
          ],
        }

        chart.setOption(option)
      }

      function initIndexFundChart(data) {
        const chart = echarts.init(document.getElementById('index-fund-chart'))

        const dates = data.map((item) => item.trade_date)
        const shIndex = data.map((item) => item.sh_index_close)
        const mainInflow = data.map((item) => item.main_net_inflow)

        const option = {
          title: {
            text: '指数与资金流向关系',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: ['上证指数', '主力净流入'],
          },
          xAxis: {
            type: 'category',
            data: dates,
          },
          yAxis: [
            {
              type: 'value',
              name: '指数点位',
              position: 'left',
            },
            {
              type: 'value',
              name: '资金流入(亿)',
              position: 'right',
            },
          ],
          series: [
            {
              name: '上证指数',
              type: 'line',
              yAxisIndex: 0,
              data: shIndex,
            },
            {
              name: '主力净流入',
              type: 'bar',
              yAxisIndex: 1,
              data: mainInflow,
              itemStyle: {
                color: function (params) {
                  return params.value >= 0 ? '#ef4444' : '#22c55e'
                },
              },
            },
          ],
        }

        chart.setOption(option)
      }
    </script>
  </body>
</html>
