{% extends "base.html" %}
{% load static %}

{% block title %}股票列表 - 股票分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">
          股票列表
        </h2>
        <div class="text-muted mt-1">
          <span class="text-nowrap">共 {{ total_stocks }} 只股票</span>
          {% if latest_trade_date %}
          <span class="ms-2">数据日期: {{ latest_trade_date|date:"Y-m-d" }}</span>
          {% endif %}
      </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 市场情绪和交易所分布 -->
    <div class="row mb-3">
      <div class="col-md-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center mb-3">
              <div class="avatar bg-indigo-lt me-3">
                <i class="ti ti-trending-up"></i>
              </div>
              <h3 class="card-title m-0">市场情绪</h3>
            </div>
            <div class="progress mb-2">
              <div class="progress-bar bg-red" style="width: {{ market_sentiment.up_percent|default:0 }}%" role="progressbar" aria-label="涨">
                {{ market_sentiment.up_count|default:0 }}
              </div>
              <div class="progress-bar bg-success" style="width: {{ market_sentiment.down_percent|default:0 }}%" role="progressbar" aria-label="跌">
                {{ market_sentiment.down_count|default:0 }}
              </div>
              <div class="progress-bar bg-muted" style="width: {{ market_sentiment.flat_percent|default:0 }}%" role="progressbar" aria-label="平">
                {{ market_sentiment.flat_count|default:0 }}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div class="text-red">
                <i class="ti ti-arrow-up me-1"></i>
                上涨: {{ market_sentiment.up_count|default:0 }}
              </div>
              <div class="text-success">
                <i class="ti ti-arrow-down me-1"></i>
                下跌: {{ market_sentiment.down_count|default:0 }}
              </div>
              <div class="text-muted">
                <i class="ti ti-minus me-1"></i>
                平盘: {{ market_sentiment.flat_count|default:0 }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
    <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center mb-3">
              <div class="avatar bg-blue-lt me-3">
                <i class="ti ti-building-skyscraper"></i>
              </div>
              <h3 class="card-title m-0">交易所分布</h3>
            </div>
            <div class="row g-3">
              <div class="col-4">
                <div class="text-center">
                  <span class="d-block fs-1 fw-bold text-blue">{{ exchange_stats.sh_count|default:0 }}</span>
                  <span class="d-block text-muted">上证</span>
                </div>
              </div>
              <div class="col-4">
                <div class="text-center">
                  <span class="d-block fs-1 fw-bold text-green">{{ exchange_stats.sz_count|default:0 }}</span>
                  <span class="d-block text-muted">深证</span>
                </div>
              </div>
              <div class="col-4">
                <div class="text-center">
                  <span class="d-block fs-1 fw-bold text-purple">{{ exchange_stats.bj_count|default:0 }}</span>
                  <span class="d-block text-muted">北证</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和分类卡片 -->
    <div class="card mb-3">
      <div class="card-body">
        <form method="get" action="{% url 'market_data:stock_list' %}">
          <div class="row g-3">
            <div class="col-md-3">
              <div class="form-label">所属行业</div>
              <select name="industry" class="form-select">
                <option value="">全部行业</option>
                {% for ind in industries %}
                {% if ind %}
                <option value="{{ ind }}" {% if industry == ind %}selected{% endif %}>{{ ind }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <div class="form-label">所属交易所</div>
              <select name="market" class="form-select">
                <option value="">全部交易所</option>
                <option value="上海" {% if market == "上海" %}selected{% endif %}>上海证券交易所</option>
                <option value="深圳" {% if market == "深圳" %}selected{% endif %}>深圳证券交易所</option>
                <option value="北京" {% if market == "北京" %}selected{% endif %}>北京证券交易所</option>
                <option value="创业板" {% if market == "创业板" %}selected{% endif %}>创业板</option>
              </select>
            </div>
            <div class="col-md-3">
              <div class="form-label">涨跌幅筛选</div>
              <select name="limit_type" class="form-select">
                <option value="">全部股票</option>
                <optgroup label="涨停板">
                  <option value="limit_up" {% if limit_type == "limit_up" %}selected{% endif %}>涨停(主板≥9.5%/创业板≥19.5%)</option>
                  <option value="limit_down" {% if limit_type == "limit_down" %}selected{% endif %}>跌停(主板≤-9.5%/创业板≤-19.5%)</option>
                </optgroup>
                <optgroup label="涨幅区间">
                  <option value="up_5" {% if limit_type == "up_5" %}selected{% endif %}>涨幅5%以上</option>
                  <option value="up_3" {% if limit_type == "up_3" %}selected{% endif %}>涨幅3%以上</option>
                </optgroup>
                <optgroup label="震荡区间">
                  <option value="zero_3" {% if limit_type == "zero_3" %}selected{% endif %}>涨跌幅±3%</option>
                  <option value="zero_5" {% if limit_type == "zero_5" %}selected{% endif %}>涨跌幅±5%</option>
                </optgroup>
                <optgroup label="跌幅区间">
                  <option value="down_3" {% if limit_type == "down_3" %}selected{% endif %}>跌幅3%以上</option>
                  <option value="down_5" {% if limit_type == "down_5" %}selected{% endif %}>跌幅5%以上</option>
                </optgroup>
              </select>
            </div>
            <div class="col-md-3">
              <div class="form-label">价格范围</div>
              <div class="input-group">
                <input type="number" name="min_price" class="form-control" placeholder="最低价" value="{{ min_price|default:'' }}" min="0" step="0.01">
                <span class="input-group-text">-</span>
                <input type="number" name="max_price" class="form-control" placeholder="最高价" value="{{ max_price|default:'' }}" min="0" step="0.01">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-label">排序方式</div>
              <select name="sort" class="form-select">
                <option value="stock_code" {% if sort == "stock_code" %}selected{% endif %}>代码升序</option>
                <option value="-stock_code" {% if sort == "-stock_code" %}selected{% endif %}>代码降序</option>
                <option value="listing_date" {% if sort == "listing_date" %}selected{% endif %}>上市日期升序</option>
                <option value="-listing_date" {% if sort == "-listing_date" %}selected{% endif %}>上市日期降序</option>
                <option value="-close_price" {% if sort == "-close_price" %}selected{% endif %}>收盘价降序</option>
                <option value="close_price" {% if sort == "close_price" %}selected{% endif %}>收盘价升序</option>
                <option value="-change_percent" {% if sort == "-change_percent" %}selected{% endif %}>涨幅降序</option>
                <option value="change_percent" {% if sort == "change_percent" %}selected{% endif %}>涨幅升序</option>
                <option value="-volume" {% if sort == "-volume" %}selected{% endif %}>成交量降序</option>
                <option value="-amount" {% if sort == "-amount" %}selected{% endif %}>成交额降序</option>
                <option value="-turnover_rate" {% if sort == "-turnover_rate" %}selected{% endif %}>换手率降序</option>
              </select>
            </div>
            <div class="col-md-9">
              <div class="form-label">股票搜索</div>
              <div class="input-icon">
                <input type="text" name="q" value="{{ query }}" class="form-control" placeholder="输入股票代码、名称或所属行业...">
                <span class="input-icon-addon">
                  <i class="ti ti-search"></i>
                </span>
              </div>
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button type="submit" class="btn btn-primary w-100">
                <i class="ti ti-search me-1"></i>
                查询
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- 股票列表 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">股票列表</h3>
        <div class="card-actions">
          <a href="{% url 'market_data:stock_list' %}" class="btn btn-outline-primary btn-sm">
            <i class="ti ti-refresh me-1"></i>
            重置筛选
          </a>
        </div>
      </div>
      <div class="card-body p-0">
        {% if page_obj %}
      <div class="table-responsive">
          <table class="table table-vcenter card-table table-striped table-hover">
          <thead>
            <tr>
                <th>
                  <a href="?sort={% if sort == 'stock_code' %}-stock_code{% else %}stock_code{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block">
                    代码
                    {% if sort == 'stock_code' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-stock_code' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th>
                  <a href="?sort={% if sort == 'stock_name' %}-stock_name{% else %}stock_name{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block">
                    名称
                    {% if sort == 'stock_name' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-stock_name' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th>
                  <a href="?sort={% if sort == 'industry' %}-industry{% else %}industry{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block">
                    行业
                    {% if sort == 'industry' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-industry' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th>
                  <a href="?sort={% if sort == 'market' %}-market{% else %}market{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block">
                    交易所
                    {% if sort == 'market' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-market' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-end">
                  <a href="?sort={% if sort == 'close_price' %}-close_price{% else %}close_price{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block text-end">
                    最新价
                    {% if sort == 'close_price' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-close_price' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-end">
                  <a href="?sort={% if sort == 'change_percent' %}-change_percent{% else %}change_percent{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block text-end">
                    涨跌幅
                    {% if sort == 'change_percent' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-change_percent' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-end">
                  <a href="?sort={% if sort == 'change_amount' %}-change_amount{% else %}change_amount{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block text-end">
                    涨跌额
                    {% if sort == 'change_amount' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-change_amount' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-end">
                  <a href="?sort={% if sort == 'volume' %}-volume{% else %}volume{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block text-end">
                    成交量(万手)
                    {% if sort == 'volume' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-volume' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-end">
                  <a href="?sort={% if sort == 'amount' %}-amount{% else %}amount{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block text-end">
                    成交额(亿)
                    {% if sort == 'amount' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-amount' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-end">
                  <a href="?sort={% if sort == 'turnover_rate' %}-turnover_rate{% else %}turnover_rate{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block text-end">
                    换手率
                    {% if sort == 'turnover_rate' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-turnover_rate' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-center">
                  <a href="?sort={% if sort == 'trade_date' %}-trade_date{% else %}trade_date{% endif %}{% for key, value in query_params.items %}{% if key != 'sort' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-reset d-block text-center">
                    交易日期
                    {% if sort == 'trade_date' %}<i class="ti ti-arrow-up"></i>{% elif sort == '-trade_date' %}<i class="ti ti-arrow-down"></i>{% endif %}
                  </a>
                </th>
                <th class="text-center">操作</th>
            </tr>
          </thead>
          <tbody>
              {% for stock in page_obj %}
              <tr>
                <td class="font-monospace">
                  {% if stock.is_hot %}
                  <span class="badge bg-red-lt me-1" title="热门股票" data-bs-toggle="tooltip">
                    <i class="ti ti-flame"></i>
                  </span>
                  {% endif %}
                  <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset">
                    {{ stock.stock_code }}
                  </a>
                </td>
                <td>
                  <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset fw-bold">
                    {{ stock.stock_name }}
                  </a>
                </td>
                <td>
                  {% if stock.industry %}
                  <span class="badge bg-azure-lt">
                    <a href="{% url 'market_data:industry_stocks' stock.industry %}" class="text-reset">
                      {{ stock.industry }}
                    </a>
                  </span>
                  {% else %}
                  <span class="badge bg-muted-lt">未分类</span>
                  {% endif %}
                </td>
                <td>
                  {% if stock.market == "沪市" %}
                  <span class="badge bg-blue-lt">上证</span>
                  {% elif stock.market == "深市" %}
                  <span class="badge bg-green-lt">深证</span>
                  {% elif stock.market == "北市" %}
                  <span class="badge bg-purple-lt">北证</span>
                  {% else %}
                  <span class="badge bg-muted-lt">{{ stock.market }}</span>
                  {% endif %}
                </td>
                <td class="text-end font-weight-bold">
                  {% if stock.close_price %}{{ stock.close_price|floatformat:2 }}{% else %}--{% endif %}
                </td>
                <td class="text-end {% if stock.change_percent > 0 %}text-danger{% elif stock.change_percent < 0 %}text-success{% endif %}">
                  {% if stock.change_percent %}
                    {% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:2 }}%
                  {% else %}--{% endif %}
                </td>
                <td class="text-end {% if stock.change_amount > 0 %}text-danger{% elif stock.change_amount < 0 %}text-success{% endif %}">
                  {% if stock.change_amount %}
                    {% if stock.change_amount > 0 %}+{% endif %}{{ stock.change_amount|floatformat:2 }}
                  {% else %}--{% endif %}
                </td>
                <td class="text-end">
                  {% if stock.volume %}{{ stock.volume|floatformat:2 }}{% else %}--{% endif %}
                </td>
                <td class="text-end">
                  {% if stock.amount %}{{ stock.amount|floatformat:2 }}{% else %}--{% endif %}
                </td>
                <td class="text-end">
                  {% if stock.turnover_rate %}{{ stock.turnover_rate|floatformat:2 }}%{% else %}--{% endif %}
                </td>
                <td class="text-center">
                  {% if stock.trade_date %}{{ stock.trade_date|date:"Y-m-d" }}{% else %}--{% endif %}
                </td>
                <td class="text-center">
                  <div class="btn-list">
                    <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="btn btn-sm btn-icon">
                      <i class="ti ti-chart-line"></i>
                    </a>
                    <a href="#" class="btn btn-sm btn-icon">
                      <i class="ti ti-star"></i>
                    </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
        {% else %}
        <div class="empty">
          <div class="empty-img">
            <i class="ti ti-search icon-lg text-muted"></i>
          </div>
          <p class="empty-title">未找到股票</p>
          <p class="empty-subtitle text-muted">
            没有找到符合条件的股票，请尝试修改搜索条件或筛选条件。
          </p>
          <div class="empty-action">
            <a href="{% url 'market_data:stock_list' %}" class="btn btn-primary">
              <i class="ti ti-refresh me-1"></i>
              显示所有股票
            </a>
          </div>
        </div>
        {% endif %}
      </div>

      {% include 'includes/pagination.html' with page_obj=page_obj rows_per_page=rows_per_page %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<style>
  th a {
    cursor: pointer;
    text-decoration: none;
  }
  th a:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }
</style>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 已经使用链接实现了排序功能，不再需要JavaScript处理
  });
</script>
{% endblock %}
