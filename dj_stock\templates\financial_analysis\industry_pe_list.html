{% extends 'base.html' %}

{% block title %}行业市盈率数据 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">行业市盈率数据</h2>
        <div class="text-muted mt-1">查看各行业的市盈率、市净率等估值数据</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <form method="get" class="d-flex">
              <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
              <select class="form-select ms-2" name="type">
                <option value="">全部行业分类</option>
                {% for type in industry_types %}
                  {% if type %}
                  <option value="{{ type }}" {% if industry_type == type %}selected{% endif %}>{{ type }}</option>
                  {% endif %}
                {% endfor %}
              </select>
              <select class="form-select ms-2" name="sort">
                <option value="pe_ttm" {% if sort_by == 'pe_ttm' %}selected{% endif %}>市盈率升序</option>
                <option value="-pe_ttm" {% if sort_by == '-pe_ttm' %}selected{% endif %}>市盈率降序</option>
                <option value="pb" {% if sort_by == 'pb' %}selected{% endif %}>市净率升序</option>
                <option value="-pb" {% if sort_by == '-pb' %}selected{% endif %}>市净率降序</option>
                <option value="ps_ttm" {% if sort_by == 'ps_ttm' %}selected{% endif %}>市销率升序</option>
                <option value="-ps_ttm" {% if sort_by == '-ps_ttm' %}selected{% endif %}>市销率降序</option>
                <option value="dv_ratio" {% if sort_by == 'dv_ratio' %}selected{% endif %}>股息率升序</option>
                <option value="-dv_ratio" {% if sort_by == '-dv_ratio' %}selected{% endif %}>股息率降序</option>
                <option value="total_mv" {% if sort_by == 'total_mv' %}selected{% endif %}>总市值升序</option>
                <option value="-total_mv" {% if sort_by == '-total_mv' %}selected{% endif %}>总市值降序</option>
              </select>
              <button type="submit" class="btn ms-2">查询</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">平均市盈率(TTM)</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">{{ avg_pe|floatformat:2 }}</div>
            <div class="d-flex mb-2">
              <div>行业数量</div>
              <div class="ms-auto">{{ industries.paginator.count }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">平均市净率</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">{{ avg_pb|floatformat:2 }}</div>
            <div class="d-flex mb-2">
              <div>行业分类</div>
              <div class="ms-auto">{{ industry_type|default:"全部" }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">最高市盈率行业</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if industries.0.pe_ttm %}
              {{ industries.0.pe_ttm|floatformat:2 }}
              {% else %}
              -
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>行业名称</div>
              <div class="ms-auto">
                {% if industries.0.industry_name %}
                {{ industries.0.industry_name }}
                {% else %}
                -
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">最低市盈率行业</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if industries.paginator.count > 0 %}
              {{ industries.paginator.object_list.0.pe_ttm|floatformat:2 }}
              {% else %}
              -
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>行业名称</div>
              <div class="ms-auto">
                {% if industries.paginator.count > 0 %}
                {{ industries.paginator.object_list.0.industry_name }}
                {% else %}
                -
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行业市盈率表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">行业市盈率数据 ({{ selected_date|date:"Y-m-d" }})</h3>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted">
            显示
            <div class="mx-2 d-inline-block">
              <select class="form-select form-select-sm" id="page-size">
                <option value="10">10</option>
                <option value="20" selected>20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </div>
            条记录
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>行业代码</th>
              <th>行业名称</th>
              <th>市盈率</th>
              <th>市盈率(TTM)</th>
              <th>市净率</th>
              <th>市销率</th>
              <th>市销率(TTM)</th>
              <th>股息率</th>
              <th>股息率(TTM)</th>
              <th>总市值(亿元)</th>
              <th>行业分类标准</th>
            </tr>
          </thead>
          <tbody>
            {% for industry in industries %}
            <tr>
              <td>{{ industry.industry_code }}</td>
              <td>{{ industry.industry_name }}</td>
              <td>{{ industry.pe|floatformat:2 }}</td>
              <td>{{ industry.pe_ttm|floatformat:2 }}</td>
              <td>{{ industry.pb|floatformat:2 }}</td>
              <td>{{ industry.ps|floatformat:2 }}</td>
              <td>{{ industry.ps_ttm|floatformat:2 }}</td>
              <td>{{ industry.dv_ratio|floatformat:2 }}%</td>
              <td>{{ industry.dv_ttm|floatformat:2 }}%</td>
              <td>{{ industry.total_mv|floatformat:2 }}</td>
              <td>{{ industry.industry_type }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="11" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前日期没有行业市盈率数据，请尝试选择其他日期。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <div class="card-footer d-flex align-items-center">
        <p class="m-0 text-muted">显示 <span>{{ industries.start_index }}</span> 到 <span>{{ industries.end_index }}</span> 条，共 <span>{{ industries.paginator.count }}</span> 条记录</p>
        <ul class="pagination m-0 ms-auto">
          {% if industries.has_previous %}
          <li class="page-item">
            <a class="page-link" href="?page=1&date={{ selected_date|date:'Y-m-d' }}&type={{ industry_type }}&sort={{ sort_by }}">
              <i class="ti ti-chevrons-left"></i>
            </a>
          </li>
          <li class="page-item">
            <a class="page-link" href="?page={{ industries.previous_page_number }}&date={{ selected_date|date:'Y-m-d' }}&type={{ industry_type }}&sort={{ sort_by }}">
              <i class="ti ti-chevron-left"></i>
            </a>
          </li>
          {% else %}
          <li class="page-item disabled">
            <a class="page-link" href="#">
              <i class="ti ti-chevrons-left"></i>
            </a>
          </li>
          <li class="page-item disabled">
            <a class="page-link" href="#">
              <i class="ti ti-chevron-left"></i>
            </a>
          </li>
          {% endif %}
          
          {% for i in industries.paginator.page_range %}
            {% if industries.number == i %}
              <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
            {% elif i > industries.number|add:"-3" and i < industries.number|add:"3" %}
              <li class="page-item"><a class="page-link" href="?page={{ i }}&date={{ selected_date|date:'Y-m-d' }}&type={{ industry_type }}&sort={{ sort_by }}">{{ i }}</a></li>
            {% endif %}
          {% endfor %}
          
          {% if industries.has_next %}
          <li class="page-item">
            <a class="page-link" href="?page={{ industries.next_page_number }}&date={{ selected_date|date:'Y-m-d' }}&type={{ industry_type }}&sort={{ sort_by }}">
              <i class="ti ti-chevron-right"></i>
            </a>
          </li>
          <li class="page-item">
            <a class="page-link" href="?page={{ industries.paginator.num_pages }}&date={{ selected_date|date:'Y-m-d' }}&type={{ industry_type }}&sort={{ sort_by }}">
              <i class="ti ti-chevrons-right"></i>
            </a>
          </li>
          {% else %}
          <li class="page-item disabled">
            <a class="page-link" href="#">
              <i class="ti ti-chevron-right"></i>
            </a>
          </li>
          <li class="page-item disabled">
            <a class="page-link" href="#">
              <i class="ti ti-chevrons-right"></i>
            </a>
          </li>
          {% endif %}
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 页面大小切换
    const pageSizeSelect = document.getElementById('page-size');
    pageSizeSelect.addEventListener('change', function() {
      const pageSize = this.value;
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.set('per_page', pageSize);
      window.location.href = currentUrl.toString();
    });
  });
</script>
{% endblock %}
