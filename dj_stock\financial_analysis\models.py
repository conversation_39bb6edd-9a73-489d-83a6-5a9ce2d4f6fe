# utf-8
from django.db import models
from django.contrib.auth.models import User
from datetime import datetime
import re

# Create your models here.


class StockFavorite(models.Model):
    """用户收藏的股票"""

    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    stock_code = models.CharField(max_length=10, verbose_name="股票代码")
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        db_table = "stock_favorite"
        verbose_name = "股票收藏"
        verbose_name_plural = verbose_name
        unique_together = [("user", "stock_code")]

    def __str__(self):
        return f"{self.user.username} - {self.stock_name}({self.stock_code})"


class StockDividend(models.Model):
    """股票分红记录"""

    stock_code = models.Char<PERSON>ield(max_length=10, verbose_name="股票代码")
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    announce_date = models.DateField(verbose_name="公告日期")
    registration_date = models.DateField(verbose_name="登记日期")
    ex_dividend_date = models.DateField(
        null=True, blank=True, verbose_name="除权除息日"
    )
    payment_date = models.DateField(null=True, blank=True, verbose_name="红利发放日")
    dividend_plan = models.TextField(verbose_name="分红方案")
    cash_dividend = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="现金分红"
    )
    share_dividend = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="送股"
    )
    share_transfer = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="转增"
    )
    implementation_status = models.CharField(max_length=20, verbose_name="实施状态")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = "stock_dividend"
        verbose_name = "股票分红"
        verbose_name_plural = verbose_name
        ordering = ["-registration_date"]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.registration_date}"


class StockFinancialIndicator(models.Model):
    """股票财务指标"""

    stock_code = models.CharField(max_length=10, verbose_name="股票代码")
    report_date = models.DateField(
        verbose_name="报告期"
    )  # 实际的财报日期（3/31, 6/30, 9/30, 12/31）

    # 基本财务数据
    net_profit = models.DecimalField(
        max_digits=20, decimal_places=2, null=True, blank=True, verbose_name="净利润"
    )
    net_profit_growth = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="净利润同比增长率",
    )
    deducted_net_profit = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="扣非净利润",
    )
    total_revenue = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="营业总收入",
    )
    total_revenue_growth = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="营业总收入同比增长率",
    )

    # 资产负债表数据
    total_assets = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="总资产",
    )
    net_assets = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="净资产(股东权益合计)",
    )
    goodwill = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="商誉",
    )

    # 现金流量表数据
    operating_cash_flow = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="经营现金流量净额",
    )

    # 每股指标
    eps = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="基本每股收益",
    )
    diluted_eps = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="稀释每股收益",
    )
    nav = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股净资产",
    )
    capital_reserve = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股资本公积金",
    )
    undistributed_profit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股未分配利润",
    )
    surplus_reserve = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股盈余公积金",
    )
    retained_earnings = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股留存收益",
    )
    ocf_per_share = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股经营现金流",
    )
    cash_flow_per_share = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股现金流",
    )
    free_cash_flow_per_share = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股企业自由现金流量",
    )
    shareholder_free_cash_flow_per_share = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股股东自由现金流量",
    )
    revenue_per_share = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股营业收入",
    )
    ebit_per_share = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="每股息税前利润",
    )

    # 盈利能力指标
    net_profit_margin = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="销售净利率",
    )
    gross_profit_margin = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="销售毛利率",
    )
    operating_profit_margin = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="营业利润率",
    )
    ebit_margin = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="息税前利润率",
    )
    expense_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="期间费用率",
    )
    cost_expense_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="成本费用率",
    )

    # 收益率指标
    roe = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="净资产收益率",
    )
    diluted_roe = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="净资产收益率-摊薄",
    )
    roa = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="总资产报酬率",
    )
    total_capital_return = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="总资本回报率",
    )
    invested_capital_return = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="投入资本回报率",
    )

    # 现金流指标
    cash_to_sales_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="经营活动净现金/销售收入",
    )
    cash_to_revenue_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="经营性现金净流量/营业总收入",
    )
    cash_to_profit_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="经营活动净现金/归属母公司的净利润",
    )
    tax_to_profit_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="所得税/利润总额",
    )

    inventory_turnover = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="存货周转率",
    )
    inventory_days = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="存货周转天数",
    )
    receivable_days = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="应收账款周转天数",
    )
    accounts_payable_turnover = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="应付账款周转率",
    )
    total_assets_turnover = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="总资产周转率",
    )
    total_assets_turnover_days = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="总资产周转天数",
    )
    current_assets_turnover = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="流动资产周转率",
    )
    current_assets_turnover_days = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="流动资产周转天数",
    )

    # 偿债能力指标
    current_ratio = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="流动比率"
    )
    quick_ratio = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="速动比率"
    )
    conservative_quick_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="保守速动比率",
    )
    cash_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="现金比率",
    )
    equity_ratio = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="产权比率"
    )
    equity_multiplier = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="权益乘数",
    )
    debt_asset_ratio = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="资产负债率",
    )

    # 元数据
    last_update = models.DateTimeField(auto_now=True, verbose_name="最后更新时间")
    data_source = models.CharField(
        max_length=50, default="同花顺", verbose_name="数据来源"
    )
    collection_date = models.DateTimeField(
        default=datetime.now, verbose_name="数据采集时间"
    )

    class Meta:
        db_table = "stock_financial_indicator"  # 指定表名
        verbose_name = "股票财务指标"
        verbose_name_plural = verbose_name
        unique_together = ("stock_code", "report_date")
        indexes = [
            models.Index(fields=["stock_code", "report_date"]),
        ]
        managed = False

    def __str__(self):
        return f"{self.stock_code} - {self.report_date}"

    @staticmethod
    def get_latest_report_date():
        """获取最新的报告期日期"""
        today = datetime.now()
        month = today.month
        year = today.year

        # 根据当前月份确定最近的报告期
        if month < 4:  # 1-3月，最近的报告期是上一年的12月31日
            return datetime(year - 1, 12, 31).date()
        elif month < 7:  # 4-6月，最近的报告期是3月31日
            return datetime(year, 3, 31).date()
        elif month < 10:  # 7-9月，最近的报告期是6月30日
            return datetime(year, 6, 30).date()
        else:  # 10-12月，最近的报告期是9月30日
            return datetime(year, 9, 30).date()

    @staticmethod
    def get_report_dates(num_quarters=4):
        """获取最近几个季度的报告期日期列表"""
        latest_date = StockFinancialIndicator.get_latest_report_date()
        dates = []
        year = latest_date.year
        month = latest_date.month

        for i in range(num_quarters):
            dates.append(datetime(year, month, 31 if month in [3, 12] else 30).date())
            month -= 3
            if month <= 0:
                month += 12
                year -= 1

        return sorted(dates, reverse=True)

    def needs_update(self):
        """检查是否需要更新数据"""
        if not self.last_update:
            return True

        # 如果最后更新时间距离现在超过7天，则需要更新
        return (datetime.now() - self.last_update.replace(tzinfo=None)).days >= 7


class StockIndustryChain(models.Model):
    """股票产业链"""

    stock_code = models.CharField(max_length=10, verbose_name="股票代码")
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    industry_chain = models.CharField(max_length=100, verbose_name="产业链")
    position = models.CharField(max_length=50, verbose_name="产业链位置")
    market_share = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="市场份额"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = "stock_industry_chain"
        verbose_name = "股票产业链"
        verbose_name_plural = verbose_name
        ordering = ["stock_code"]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.industry_chain}"


class StockShareholderStatistics(models.Model):
    """十大股东持股统计数据"""

    class Meta:
        db_table = "stock_shareholder_statistics"
        unique_together = [["shareholder_name", "report_date"]]
        verbose_name = "十大股东持股统计数据"
        verbose_name_plural = verbose_name
        ordering = ["-report_date", "shareholder_name"]
        managed = False

    id = models.AutoField(primary_key=True, verbose_name="主键ID")
    shareholder_name = models.CharField(
        max_length=255, db_index=True, verbose_name="股东名称"
    )
    shareholder_type = models.CharField(max_length=50, verbose_name="股东类型")
    report_date = models.DateField(verbose_name="统计截止日期", null=True, blank=True)
    count = models.IntegerField(verbose_name="统计次数")
    avg_return_10d = models.FloatField(
        null=True, blank=True, verbose_name="10日平均涨幅"
    )
    max_return_10d = models.FloatField(
        null=True, blank=True, verbose_name="10日最大涨幅"
    )
    min_return_10d = models.FloatField(
        null=True, blank=True, verbose_name="10日最小涨幅"
    )
    avg_return_30d = models.FloatField(
        null=True, blank=True, verbose_name="30日平均涨幅"
    )
    max_return_30d = models.FloatField(
        null=True, blank=True, verbose_name="30日最大涨幅"
    )
    min_return_30d = models.FloatField(
        null=True, blank=True, verbose_name="30日最小涨幅"
    )
    avg_return_60d = models.FloatField(
        null=True, blank=True, verbose_name="60日平均涨幅"
    )
    max_return_60d = models.FloatField(
        null=True, blank=True, verbose_name="60日最大涨幅"
    )
    min_return_60d = models.FloatField(
        null=True, blank=True, verbose_name="60日最小涨幅"
    )
    holding_stocks = models.TextField(
        null=True, blank=True, verbose_name="持有个股列表"
    )
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    def __str__(self):
        return f"{self.shareholder_name} - {self.report_date}"


class StockShareholderDetail(models.Model):
    """股东持股明细-十大流通股东"""

    shareholder_name = models.CharField("股东名称", max_length=100)
    shareholder_type = models.CharField(
        "股东类型", max_length=50, null=True, blank=True
    )
    stock_code = models.CharField("股票代码", max_length=10)
    stock_name = models.CharField("股票名称", max_length=50)
    report_period = models.DateField("报告期")
    holding_amount = models.FloatField("期末持股数量（股）", null=True, blank=True)
    holding_change = models.FloatField("期末持股数量变化（股）", null=True, blank=True)
    holding_change_ratio = models.FloatField(
        "期末持股数量变化比例（%）", null=True, blank=True
    )
    holding_trend = models.CharField(
        "期末持股变动趋势", max_length=20, null=True, blank=True
    )
    market_value = models.FloatField("期末持股流通市值（元）", null=True, blank=True)
    announce_date = models.DateField("公告日", null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        db_table = "stock_shareholder_detail"
        verbose_name = "股东持股明细"
        verbose_name_plural = verbose_name
        unique_together = ["stock_code", "shareholder_name", "report_period"]
        indexes = [
            models.Index(fields=["stock_code"]),
            models.Index(fields=["report_period"]),
        ]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.shareholder_name} - {self.report_period}"


class StockIndustryPE(models.Model):
    """行业市盈率数据"""

    industry_code = models.CharField("行业代码", max_length=20)
    industry_name = models.CharField("行业名称", max_length=50)
    trade_date = models.DateField("交易日期")
    pe = models.FloatField("市盈率", null=True, blank=True)
    pe_ttm = models.FloatField("市盈率TTM", null=True, blank=True)
    pb = models.FloatField("市净率", null=True, blank=True)
    ps = models.FloatField("市销率", null=True, blank=True)
    ps_ttm = models.FloatField("市销率TTM", null=True, blank=True)
    pcf = models.FloatField("市现率", null=True, blank=True)
    pcf_ttm = models.FloatField("市现率TTM", null=True, blank=True)
    dv_ratio = models.FloatField("股息率", null=True, blank=True)
    dv_ttm = models.FloatField("股息率TTM", null=True, blank=True)
    total_mv = models.FloatField("总市值(亿元)", null=True, blank=True)
    industry_type = models.CharField("行业分类标准", max_length=20)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        db_table = "stock_industry_pe"
        verbose_name = "行业市盈率数据"
        verbose_name_plural = verbose_name
        unique_together = ["industry_code", "trade_date"]
        indexes = [
            models.Index(fields=["trade_date"]),
            models.Index(fields=["industry_code"]),
        ]
        managed = False

    def __str__(self):
        return f"{self.industry_name}({self.industry_code}) - {self.trade_date}"


class StockComment(models.Model):
    """千股千评数据"""

    stock_code = models.CharField("股票代码", max_length=10, db_index=True)
    stock_name = models.CharField("股票名称", max_length=100)
    date = models.DateField("评级日期")
    rating = models.CharField("最新评级", max_length=50, null=True, blank=True)
    target_price = models.FloatField("目标价格(元)", null=True, blank=True)
    current_price = models.FloatField("当前价格(元)", null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "千股千评"
        verbose_name_plural = verbose_name
        db_table = "stock_comment"
        unique_together = [["stock_code", "date"]]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.date}"


class TechnicalIndicator(models.Model):
    """技术指标数据"""

    stock_code = models.CharField("股票代码", max_length=10)
    stock_name = models.CharField("股票名称", max_length=100)
    date = models.DateField("统计日期")
    indicator_type = models.CharField("指标类型", max_length=50)
    indicator_value = models.FloatField("指标值", null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "技术指标"
        verbose_name_plural = verbose_name
        unique_together = [["stock_code", "date", "indicator_type"]]
        db_table = "technical_indicator"
        indexes = [
            models.Index(fields=["stock_code", "date"]),
            models.Index(fields=["indicator_type"]),
        ]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.indicator_type} - {self.date}"
