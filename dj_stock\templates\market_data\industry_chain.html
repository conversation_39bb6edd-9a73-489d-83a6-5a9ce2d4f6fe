{% extends "base.html" %} {% block title %}产业链关系 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">特色分析</div>
        <h2 class="page-title">产业链关系</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:index' %}" class="btn btn-outline-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="icon"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M5 12l-2 0l9 -9l9 9l-2 0" />
              <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
              <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />
            </svg>
            返回首页
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">产业链关系</h3>
            <div class="card-actions">
              <form method="get" action="{% url 'market_data:industry_chain' %}">
                <div class="input-group">
                  <input type="text" class="form-control" placeholder="搜索股票代码或名称..." name="search" value="{{ search_query }}" />
                  <select class="form-select" name="industry">
                    <option value="">所有产业链...</option>
                    {% for ind in industries %}
                    <option value="{{ ind }}" {% if selected_industry == ind %}selected{% endif %}>{{ ind }}</option>
                    {% endfor %}
                  </select>
                  <select class="form-select" name="relation_type">
                    <option value="">所有关系类型</option>
                    <option value="上游" {% if relation_type == '上游' %}selected{% endif %}>上游</option>
                    <option value="下游" {% if relation_type == '下游' %}selected{% endif %}>下游</option>
                    <option value="同业" {% if relation_type == '同业' %}selected{% endif %}>同业</option>
                  </select>
                  <button type="submit" class="btn btn-primary">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" />
                      <path d="M21 21l-6 -6" />
                    </svg>
                    搜索
                  </button>
                </div>
              </form>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter card-table">
                <thead>
                  <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>关联股票代码</th>
                    <th>关联股票名称</th>
                    <th>关系类型</th>
                    <th>关联度权重</th>
                    <th>所属产业链</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in page_obj %}
                  <tr>
                    <td>{{ item.stock_code }}</td>
                    <td>{{ item.stock_name }}</td>
                    <td>{{ item.related_stock_code }}</td>
                    <td>{{ item.related_stock_name }}</td>
                    <td>
                      <span class="badge bg-{% if item.relation_type == '上游' %}primary{% elif item.relation_type == '下游' %}success{% else %}info{% endif %}">
                        {{ item.relation_type }}
                      </span>
                    </td>
                    <td>{{ item.relation_weight|floatformat:2 }}</td>
                    <td>{{ item.industry_name }}</td>
                    <td>
                      <div class="btn-list flex-nowrap">
                        <a href="{% url 'market_data:stock_detail' item.stock_code %}" class="btn btn-primary btn-sm">主体详情</a>
                        <a href="{% url 'market_data:stock_detail' item.related_stock_code %}" class="btn btn-secondary btn-sm">关联详情</a>
                      </div>
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="8" class="text-center">暂无产业链关系数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% include "includes/pagination.html" with page_obj=page_obj total_items=total_items %}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 产业链说明 -->
    <div class="row mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">产业链关系说明</h3>
          </div>
          <div class="card-body">
            <div class="datagrid">
              <div class="datagrid-item">
                <div class="datagrid-title">上游关系</div>
                <div class="datagrid-content">指提供原材料、零部件等生产要素的企业，位于产业链的前端。</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">下游关系</div>
                <div class="datagrid-content">指采购产品或服务的企业，通常是产业链中的消费端或分销渠道。</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">同业关系</div>
                <div class="datagrid-content">指处于相同产业链位置的竞争企业，提供类似的产品或服务。</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">关联度权重</div>
                <div class="datagrid-content">数值越高，表示两家公司之间的业务关联程度越紧密。</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} 