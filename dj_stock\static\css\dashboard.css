/* 
 * 首页仪表盘专用样式
 * 优化市场概览和指数展示
 */

/* 主要指数卡片样式 */
.index-card {
  border-radius: 10px;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.index-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.index-card .card-body {
  padding: 1rem;
}

.index-card .index-name {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.index-card .index-price {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.index-card .index-change {
  font-size: 1rem;
  font-weight: 500;
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* 市场概览卡片 */
.market-overview-card {
  border-radius: 10px;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.market-overview-card .card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.25rem;
}

.market-overview-card .card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0;
}

/* 市场情绪指示器 */
.market-sentiment-indicator {
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

/* 涨跌分布图表 */
.market-distribution-chart {
  height: 200px;
  margin-bottom: 1rem;
}

/* 交易所统计 */
.exchange-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.exchange-stat-item {
  text-align: center;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  flex: 1;
  margin: 0 0.5rem;
}

.exchange-stat-item:first-child {
  margin-left: 0;
}

.exchange-stat-item:last-child {
  margin-right: 0;
}

.exchange-stat-item .stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.exchange-stat-item .stat-label {
  font-size: 0.875rem;
  color: #6c757d;
}

/* 涨跌停统计 */
.limit-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.limit-stat-item {
  text-align: center;
  padding: 0.75rem;
  border-radius: 8px;
  flex: 1;
  margin: 0 0.5rem;
}

.limit-stat-item:first-child {
  margin-left: 0;
  background-color: rgba(244, 67, 54, 0.1);
}

.limit-stat-item:last-child {
  margin-right: 0;
  background-color: rgba(76, 175, 80, 0.1);
}

.limit-stat-item .stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.limit-stat-item .stat-label {
  font-size: 0.875rem;
}

/* 日期选择器样式 */
.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.date-selector .date-label {
  margin-right: 0.5rem;
  color: #6c757d;
}

.date-selector .date-input {
  border: none;
  background-color: transparent;
  color: #495057;
  font-weight: 500;
  padding: 0;
  cursor: pointer;
}

.date-selector .date-input:focus {
  outline: none;
}

/* 指数表格样式 */
.index-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.index-table th {
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.index-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.index-table tr:last-child td {
  border-bottom: none;
}

.index-table tr:hover {
  background-color: #f8f9fa;
}

/* 涨跌颜色 */
.text-up {
  color: #f44336 !important;
}

.text-down {
  color: #4caf50 !important;
}

.bg-up {
  background-color: rgba(244, 67, 54, 0.1);
}

.bg-down {
  background-color: rgba(76, 175, 80, 0.1);
}

/* 响应式调整 */
@media (max-width: 767.98px) {
  .index-card .index-price {
    font-size: 1.25rem;
  }
  
  .index-card .index-change {
    font-size: 0.875rem;
  }
  
  .exchange-stat-item .stat-value {
    font-size: 1rem;
  }
  
  .limit-stat-item .stat-value {
    font-size: 1.25rem;
  }
}
