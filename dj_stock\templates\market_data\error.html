{% extends "base.html" %} {% block title %}错误 | 股票数据分析系统{% endblock %} {% block content %}
<div class="page-body">
  <div class="container-xl">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card shadow-sm mt-3">
          <div class="card-header">
            <h3 class="card-title">系统错误</h3>
          </div>
          <div class="card-body p-5">
            <div class="empty">
              <div class="empty-img">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="icon icon-tabler icon-tabler-alert-triangle"
                  width="80"
                  height="80"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                  <path d="M12 9v2m0 4v.01"></path>
                  <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                </svg>
              </div>
              <p class="empty-title">系统发生错误</p>
              <p class="empty-subtitle text-muted">{{ message }}</p>

              {% if error_details %}
              <div class="alert alert-danger mt-3">
                <h4 class="alert-title">错误详情</h4>
                <div class="text-start">{{ error_details }}</div>
              </div>
              {% endif %}

              <div class="empty-action">
                <a href="javascript:history.back()" class="btn btn-primary">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="icon icon-tabler icon-tabler-arrow-back"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 11l-4 4l4 4m-4 -4h11a4 4 0 0 0 0 -8h-1"></path>
                  </svg>
                  返回上一页
                </a>
                <a href="{% url 'market_data:index' %}" class="btn btn-outline-secondary ms-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="icon icon-tabler icon-tabler-home"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M5 12l-2 0l9 -9l9 9l-2 0"></path>
                    <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                    <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                  </svg>
                  返回首页
                </a>
              </div>
            </div>
          </div>
          <div class="card-footer text-muted">如果问题持续存在，请联系系统管理员或刷新页面重试。</div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
