# -*- coding: utf-8 -*-
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.core.paginator import Paginator
from datetime import datetime

from .base_views import StockMarketIndex


def market_index_list(request):
    """市场指数列表视图"""
    # 获取查询参数
    search_query = request.GET.get("search", "")
    date_str = request.GET.get("date", "")
    sort_by = request.GET.get("sort", "-trade_date")

    # 获取可用日期列表
    available_dates = (
        StockMarketIndex.objects.values_list("trade_date", flat=True)
        .distinct()
        .order_by("-trade_date")
    )

    # 构建查询
    indices = StockMarketIndex.objects.all()

    if search_query:
        indices = indices.filter(
            Q(index_code__icontains=search_query)
            | Q(index_name__icontains=search_query)
        )

    selected_date = None
    if date_str:
        try:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            indices = indices.filter(trade_date=selected_date)
        except ValueError:
            pass
    else:
        # 如果没有指定日期，使用最新日期
        if available_dates.exists():
            selected_date = available_dates.first()
            indices = indices.filter(trade_date=selected_date)

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        indices = indices.order_by(f"-{sort_field}")
    else:
        indices = indices.order_by(sort_by)

    # 分页
    rows_per_page = int(request.GET.get("rows_per_page", 10))
    paginator = Paginator(indices, rows_per_page)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    # 处理数据单位
    for index in page_obj:
        # 计算涨跌幅 - 已经是百分比，不需要再乘以100
        if (
            index.close_price
            and index.pre_close
            and index.pre_close > 0
            and not hasattr(index, "change_percent")
        ):
            index.change_percent = (
                (float(index.close_price) - float(index.pre_close))
                / float(index.pre_close)
                * 100
            )

        # 成交量转为亿手
        if index.volume:
            index.volume = float(index.volume) / 100000000
        # 成交额转为亿元
        if index.amount:
            index.amount = float(index.amount) / 100000000

    context = {
        "page_obj": page_obj,
        "total_items": indices.count(),
        "search_query": search_query,
        "available_dates": available_dates[:30],
        "selected_date": selected_date,
        "sort_by": sort_by,
        "rows_per_page": rows_per_page,
    }
    return render(request, "market_data/market_index_list.html", context)


def market_index_detail(request, code):
    """市场指数详情视图"""
    # 获取查询参数
    start_date_str = request.GET.get("start_date", "")
    end_date_str = request.GET.get("end_date", "")
    sort_by = request.GET.get("sort", "-trade_date")

    # 获取指数基本信息
    indices = StockMarketIndex.objects.filter(index_code=code)
    if not indices.exists():
        return render(request, "market_data/error.html", {"message": "指数不存在"})

    index_info = indices.first()
    index_name = index_info.index_name

    # 构建日期范围查询
    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            indices = indices.filter(trade_date__gte=start_date)
        except ValueError:
            pass

    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            indices = indices.filter(trade_date__lte=end_date)
        except ValueError:
            pass

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        indices = indices.order_by(f"-{sort_field}")
    else:
        indices = indices.order_by(sort_by)

    # 分页
    rows_per_page = int(request.GET.get("rows_per_page", 20))
    paginator = Paginator(indices, rows_per_page)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    # 处理数据单位
    for index in page_obj:
        # 计算涨跌幅 - 已经是百分比，不需要再乘以100
        if (
            index.close_price
            and index.pre_close
            and index.pre_close > 0
            and not hasattr(index, "change_percent")
        ):
            index.change_percent = (
                (float(index.close_price) - float(index.pre_close))
                / float(index.pre_close)
                * 100
            )

        # 成交量转为亿手
        if index.volume:
            index.volume = float(index.volume) / 100000000
        # 成交额转为亿元
        if index.amount:
            index.amount = float(index.amount) / 100000000

    # 获取图表数据（最近500个交易日）
    import json

    chart_data_queryset = StockMarketIndex.objects.filter(index_code=code).order_by(
        "-trade_date"
    )[:500]
    chart_data_list = list(reversed(chart_data_queryset))  # 按时间正序排列

    # 转换为图表数据格式
    chart_data = []
    for item in chart_data_list:
        # 单位转换：成交量转为亿手，成交额转为亿元
        volume_in_yi = float(item.volume) / 100000000 if item.volume else 0  # 转为亿手
        amount_in_yi = float(item.amount) / 100000000 if item.amount else 0  # 转为亿元

        chart_data.append(
            {
                "date": item.trade_date.strftime("%Y-%m-%d"),
                "open": float(item.open_price) if item.open_price else 0,
                "close": float(item.close_price) if item.close_price else 0,
                "high": float(item.high_price) if item.high_price else 0,
                "low": float(item.low_price) if item.low_price else 0,
                "volume": volume_in_yi,
                "amount": amount_in_yi,
            }
        )

    context = {
        "index_code": code,
        "index_name": index_name,
        "page_obj": page_obj,
        "total_items": indices.count(),
        "start_date": start_date_str,
        "end_date": end_date_str,
        "sort_by": sort_by,
        "rows_per_page": rows_per_page,
        "chart_data": json.dumps(chart_data),  # 添加图表数据
    }
    return render(request, "market_data/market_index_detail.html", context)


def api_index_chart_data(request, code):
    """API: 获取市场指数图表数据"""
    from django.http import JsonResponse

    # 获取参数
    days = int(request.GET.get("days", 500))  # 获取天数，默认500个交易日

    try:
        # 获取指数基本信息
        index_info = StockMarketIndex.objects.filter(index_code=code).first()
        if not index_info:
            return JsonResponse({"success": False, "error": "指数不存在"})

        # 获取历史数据
        history_data = StockMarketIndex.objects.filter(index_code=code).order_by(
            "-trade_date"
        )[:days]

        # 按时间正序排列
        history_data = list(reversed(history_data))

        # 转换为图表数据格式
        chart_data = []
        for item in history_data:
            # 单位转换：成交量转为亿手，成交额转为亿元
            volume_in_yi = (
                float(item.volume) / 100000000 if item.volume else 0
            )  # 转为亿手
            amount_in_yi = (
                float(item.amount) / 100000000 if item.amount else 0
            )  # 转为亿元

            chart_data.append(
                {
                    "date": item.trade_date.strftime("%Y-%m-%d"),
                    "open": float(item.open_price) if item.open_price else 0,
                    "close": float(item.close_price) if item.close_price else 0,
                    "high": float(item.high_price) if item.high_price else 0,
                    "low": float(item.low_price) if item.low_price else 0,
                    "volume": volume_in_yi,
                    "amount": amount_in_yi,
                }
            )

        return JsonResponse({"success": True, "data": chart_data})

    except Exception as e:
        return JsonResponse({"success": False, "error": str(e)})
