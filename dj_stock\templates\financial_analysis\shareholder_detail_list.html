{% extends 'base.html' %}
{% load humanize %}

{% block title %}股东持股明细 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <div class="page-pretitle">数据分析</div>
        <h2 class="page-title">
          <i class="ti ti-users me-2"></i>股东持股明细
        </h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="#" class="btn btn-primary d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#search-modal">
            <i class="ti ti-filter me-2"></i>高级筛选
          </a>
          <a href="#" class="btn btn-primary d-sm-none" data-bs-toggle="modal" data-bs-target="#search-modal">
            <i class="ti ti-filter"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 高级筛选模态框 -->
<div class="modal modal-blur fade" id="search-modal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">高级筛选</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="get">
        <div class="modal-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <label class="form-label">报告期</label>
              <input type="date" class="form-control" name="period" value="{{ selected_period|date:'Y-m-d' }}" max="{{ latest_period|date:'Y-m-d' }}" />
            </div>
            <div class="col-md-6">
              <label class="form-label">股东类型</label>
              <select class="form-select" name="type">
                <option value="">全部股东类型</option>
                {% for type in shareholder_types %}
                  {% if type %}
                  <option value="{{ type }}" {% if shareholder_type == type %}selected{% endif %}>{{ type }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label class="form-label">变动趋势</label>
              <select class="form-select" name="trend">
                <option value="">全部变动趋势</option>
                {% for trend in holding_trends %}
                  {% if trend %}
                  <option value="{{ trend }}" {% if holding_trend == trend %}selected{% endif %}>{{ trend }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label">关键词搜索</label>
              <div class="input-icon">
                <span class="input-icon-addon">
                  <i class="ti ti-search"></i>
                </span>
                <input type="text" class="form-control" name="q" placeholder="股票代码、名称或股东名称" value="{{ search_query }}" />
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <a href="{% url 'financial_analysis:shareholder_detail_list' %}" class="btn btn-link link-secondary">重置</a>
          <button type="submit" class="btn btn-primary ms-auto">
            <i class="ti ti-filter me-2"></i>应用筛选
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- 当前筛选条件 -->
<div class="container-xl mt-3">
  <div class="filter-badges">
    {% if selected_period %}
    <span class="badge bg-azure-lt me-2">
      <i class="ti ti-calendar me-1"></i>报告期: {{ selected_period|date:'Y-m-d' }}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'period' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="ms-1 text-decoration-none">×</a>
    </span>
    {% endif %}

    {% if shareholder_type %}
    <span class="badge bg-purple-lt me-2">
      <i class="ti ti-user me-1"></i>股东类型: {{ shareholder_type }}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'type' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="ms-1 text-decoration-none">×</a>
    </span>
    {% endif %}

    {% if holding_trend %}
    <span class="badge bg-green-lt me-2">
      <i class="ti ti-trending-up me-1"></i>变动趋势: {{ holding_trend }}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'trend' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="ms-1 text-decoration-none">×</a>
    </span>
    {% endif %}

    {% if search_query %}
    <span class="badge bg-yellow-lt me-2">
      <i class="ti ti-search me-1"></i>搜索: {{ search_query }}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'q' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="ms-1 text-decoration-none">×</a>
    </span>
    {% endif %}

    {% if selected_period or shareholder_type or holding_trend or search_query %}
    <a href="{% url 'financial_analysis:shareholder_detail_list' %}" class="btn btn-sm btn-outline-secondary">
      <i class="ti ti-trash me-1"></i>清除全部
    </a>
    {% endif %}
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 股东持股明细表格 -->
    <div class="card shadow-sm">
      <div class="card-header d-flex align-items-center">
        <h3 class="card-title">
          <i class="ti ti-table me-2"></i>股东持股明细数据
          <span class="badge bg-blue-lt ms-2">{{ shareholders.paginator.count }} 条记录</span>
        </h3>
        <div class="ms-auto">
          <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="ti ti-download me-1"></i>导出数据
            </button>
            <div class="dropdown-menu dropdown-menu-end">
              <a class="dropdown-item" href="#">
                <i class="ti ti-file-spreadsheet me-1"></i>导出为Excel
              </a>
              <a class="dropdown-item" href="#">
                <i class="ti ti-file-csv me-1"></i>导出为CSV
              </a>
              <a class="dropdown-item" href="#">
                <i class="ti ti-file-text me-1"></i>导出为PDF
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted d-flex align-items-center">
            <span>每页显示</span>
            <div class="mx-2">
              <select class="form-select form-select-sm" id="page-size">
                <option value="10">10</option>
                <option value="20" selected>20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </div>
            <span>条记录</span>
          </div>
          <div class="ms-auto d-flex align-items-center">
            <span class="text-muted me-2">排序方式:</span>
            <select class="form-select form-select-sm" id="sort-by">
              <option value="-holding_amount" {% if sort_by == '-holding_amount' %}selected{% endif %}>持股数量降序</option>
              <option value="holding_amount" {% if sort_by == 'holding_amount' %}selected{% endif %}>持股数量升序</option>
              <option value="-holding_change" {% if sort_by == '-holding_change' %}selected{% endif %}>持股变动降序</option>
              <option value="holding_change" {% if sort_by == 'holding_change' %}selected{% endif %}>持股变动升序</option>
              <option value="-market_value" {% if sort_by == '-market_value' %}selected{% endif %}>持股市值降序</option>
              <option value="market_value" {% if sort_by == 'market_value' %}selected{% endif %}>持股市值升序</option>
            </select>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter table-striped text-nowrap datatable">
          <thead class="sticky-top bg-light">
            <tr>
              <th class="w-1">序号</th>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>股东名称</th>
              <th>股东类型</th>
              <th class="text-end">持股数量</th>
              <th class="text-end">持股变动</th>
              <th class="text-end">变动比例</th>
              <th>变动趋势</th>
              <th class="text-end">持股市值</th>
              <th>公告日期</th>
              <th class="w-1">操作</th>
            </tr>
          </thead>
          <tbody>
            {% for shareholder in shareholders %}
            <tr>
              <td class="text-muted">{{ shareholders.start_index|add:forloop.counter0 }}</td>
              <td>
                <a href="{% url 'financial_analysis:shareholder_detail_by_stock' shareholder.stock_code %}" class="text-reset">
                  {{ shareholder.stock_code }}
                </a>
              </td>
              <td>
                <span class="text-truncate d-inline-block" style="max-width: 150px;" title="{{ shareholder.stock_name }}">
                  {{ shareholder.stock_name }}
                </span>
              </td>
              <td>
                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ shareholder.shareholder_name }}">
                  {{ shareholder.shareholder_name }}
                </span>
              </td>
              <td>
                {% if shareholder.shareholder_type %}
                  <span class="badge bg-blue-lt">{{ shareholder.shareholder_type }}</span>
                {% else %}
                  <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td class="text-end">
                <span class="font-weight-bold">{{ shareholder.holding_amount|floatformat:0|intcomma }}</span>
              </td>
              <td class="text-end">
                {% if shareholder.holding_change > 0 %}
                <span class="text-danger">
                  <i class="ti ti-arrow-up-right"></i>
                  +{{ shareholder.holding_change|floatformat:0|intcomma }}
                </span>
                {% elif shareholder.holding_change < 0 %}
                <span class="text-success">
                  <i class="ti ti-arrow-down-right"></i>
                  {{ shareholder.holding_change|floatformat:0|intcomma }}
                </span>
                {% else %}
                <span class="text-muted">0</span>
                {% endif %}
              </td>
              <td class="text-end">
                {% if shareholder.holding_change_ratio > 0 %}
                <span class="text-danger">
                  +{{ shareholder.holding_change_ratio|floatformat:2 }}%
                </span>
                {% elif shareholder.holding_change_ratio < 0 %}
                <span class="text-success">
                  {{ shareholder.holding_change_ratio|floatformat:2 }}%
                </span>
                {% else %}
                <span class="text-muted">0.00%</span>
                {% endif %}
              </td>
              <td>
                {% if shareholder.holding_trend == '增持' %}
                <span class="badge bg-red-lt">
                  <i class="ti ti-trending-up me-1"></i>{{ shareholder.holding_trend }}
                </span>
                {% elif shareholder.holding_trend == '减持' %}
                <span class="badge bg-green-lt">
                  <i class="ti ti-trending-down me-1"></i>{{ shareholder.holding_trend }}
                </span>
                {% elif shareholder.holding_trend == '不变' %}
                <span class="badge bg-secondary-lt">
                  <i class="ti ti-minus me-1"></i>{{ shareholder.holding_trend }}
                </span>
                {% elif shareholder.holding_trend == '新进' %}
                <span class="badge bg-blue-lt">
                  <i class="ti ti-plus me-1"></i>{{ shareholder.holding_trend }}
                </span>
                {% elif shareholder.holding_trend == '退出' %}
                <span class="badge bg-yellow-lt">
                  <i class="ti ti-x me-1"></i>{{ shareholder.holding_trend }}
                </span>
                {% else %}
                <span class="badge">{{ shareholder.holding_trend|default:"-" }}</span>
                {% endif %}
              </td>
              <td class="text-end">
                {{ shareholder.market_value|floatformat:2|intcomma }}
              </td>
              <td>
                {% if shareholder.announce_date %}
                <span class="text-muted" title="{{ shareholder.announce_date|date:"Y年m月d日" }}">
                  {{ shareholder.announce_date|date:"Y-m-d" }}
                </span>
                {% else %}
                <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{% url 'financial_analysis:shareholder_detail_by_stock' shareholder.stock_code %}" class="btn btn-sm btn-icon btn-primary" title="查看详情">
                    <i class="ti ti-eye"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="12" class="text-center py-5">
                <div class="empty">
                  <div class="empty-img">
                    <img src="/static/img/undraw_no_data.svg" height="128" alt="暂无数据">
                  </div>
                  <p class="empty-title">暂无股东持股数据</p>
                  <p class="empty-subtitle text-muted">
                    当前筛选条件下没有找到相关数据，请尝试调整筛选条件。
                  </p>
                  <div class="empty-action">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#search-modal">
                      <i class="ti ti-filter me-2"></i>重新筛选
                    </button>
                    <a href="{% url 'financial_analysis:shareholder_detail_list' %}" class="btn btn-outline-secondary ms-2">
                      <i class="ti ti-refresh me-2"></i>重置所有条件
                    </a>
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% if shareholders %}
      <div class="card-footer d-flex flex-column flex-md-row align-items-center">
        <div class="d-flex align-items-center mb-2 mb-md-0">
          <p class="m-0 text-muted">
            显示 <span class="font-weight-bold">{{ shareholders.start_index }}</span> 到
            <span class="font-weight-bold">{{ shareholders.end_index }}</span> 条，共
            <span class="font-weight-bold">{{ shareholders.paginator.count }}</span> 条记录
          </p>
          <div class="ms-3">
            <div class="btn-group btn-group-sm" role="group">
              <a href="#" class="btn btn-outline-secondary" id="prev-page" {% if not shareholders.has_previous %}disabled{% endif %}>
                <i class="ti ti-chevron-left"></i>
                上一页
              </a>
              <a href="#" class="btn btn-outline-secondary" id="next-page" {% if not shareholders.has_next %}disabled{% endif %}>
                下一页
                <i class="ti ti-chevron-right"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="ms-auto">
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
              跳转到页面
            </button>
            <div class="dropdown-menu dropdown-menu-end" style="max-height: 300px; overflow-y: auto;">
              {% for i in shareholders.paginator.page_range %}
                <a class="dropdown-item {% if shareholders.number == i %}active{% endif %}" href="?page={{ i }}&period={{ selected_period|date:'Y-m-d' }}&type={{ shareholder_type }}&trend={{ holding_trend }}&q={{ search_query }}&sort={{ sort_by }}">
                  {{ i }}
                </a>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 页面大小切换
    const pageSizeSelect = document.getElementById('page-size');
    pageSizeSelect.addEventListener('change', function() {
      const pageSize = this.value;
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.set('per_page', pageSize);
      window.location.href = currentUrl.toString();
    });

    // 排序方式切换
    const sortBySelect = document.getElementById('sort-by');
    sortBySelect.addEventListener('change', function() {
      const sortBy = this.value;
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.set('sort', sortBy);
      window.location.href = currentUrl.toString();
    });

    // 分页导航按钮
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');

    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', function(e) {
        if (!this.hasAttribute('disabled')) {
          const currentUrl = new URL(window.location.href);
          const currentPage = parseInt(currentUrl.searchParams.get('page') || '1');
          currentUrl.searchParams.set('page', Math.max(1, currentPage - 1));
          window.location.href = currentUrl.toString();
        }
        e.preventDefault();
      });
    }

    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', function(e) {
        if (!this.hasAttribute('disabled')) {
          const currentUrl = new URL(window.location.href);
          const currentPage = parseInt(currentUrl.searchParams.get('page') || '1');
          const maxPage = {{ shareholders.paginator.num_pages|default:1 }};
          currentUrl.searchParams.set('page', Math.min(maxPage, currentPage + 1));
          window.location.href = currentUrl.toString();
        }
        e.preventDefault();
      });
    }

    // 高亮表格行
    const tableRows = document.querySelectorAll('.datatable tbody tr');
    tableRows.forEach(row => {
      row.addEventListener('mouseenter', function() {
        this.classList.add('bg-light-hover');
      });
      row.addEventListener('mouseleave', function() {
        this.classList.remove('bg-light-hover');
      });
    });

    // 添加数字格式化
    function formatNumber(num) {
      return new Intl.NumberFormat('zh-CN').format(num);
    }

    // 添加动画效果
    document.querySelectorAll('.text-danger, .text-success').forEach(el => {
      el.classList.add('animate-pulse');
      setTimeout(() => {
        el.classList.remove('animate-pulse');
      }, 2000);
    });

    // 添加自定义样式
    const style = document.createElement('style');
    style.textContent = `
      .bg-light-hover {
        background-color: rgba(32, 107, 196, 0.03);
      }
      .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) 1;
      }
      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }
      .sticky-top {
        position: sticky;
        top: 0;
        z-index: 1;
      }
      .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    `;
    document.head.appendChild(style);
  });
</script>
{% endblock %}
