<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预设策略测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
    <style>
        .preset-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .preset-card:hover {
            border-color: #007bff;
            box-shadow: 0 8px 25px rgba(0,123,255,0.15);
            transform: translateY(-3px);
        }
        
        .preset-card.active {
            border-color: #007bff;
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
            box-shadow: 0 8px 25px rgba(0,123,255,0.2);
        }
        
        .preset-icon {
            font-size: 2.5rem;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>预设策略测试</h2>
        
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#presetModal">
            打开预设策略
        </button>
        
        <div class="mt-3">
            <p>选中的策略: <span id="selectedStrategy">无</span></p>
        </div>
    </div>

    <!-- 预设策略模态框 -->
    <div class="modal fade" id="presetModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择预设筛选策略</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="preset-card" data-preset="value_stocks">
                                <div class="d-flex align-items-start mb-3">
                                    <i class="ti ti-shield-check preset-icon me-3"></i>
                                    <div>
                                        <h6>价值股筛选</h6>
                                        <p class="text-muted">低估值、高分红、财务稳健的价值股</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="preset-card" data-preset="growth_stocks">
                                <div class="d-flex align-items-start mb-3">
                                    <i class="ti ti-trending-up preset-icon me-3"></i>
                                    <div>
                                        <h6>成长股筛选</h6>
                                        <p class="text-muted">高增长、高ROE的成长型股票</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="preset-card" data-preset="quality_stocks">
                                <div class="d-flex align-items-start mb-3">
                                    <i class="ti ti-star preset-icon me-3"></i>
                                    <div>
                                        <h6>优质股筛选</h6>
                                        <p class="text-muted">连续盈利、财务优秀的优质股票</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="applyPreset()">应用策略</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedPreset = null;

        function initPresetStrategies() {
            console.log('Initializing preset strategies...');
            
            const presetCards = document.querySelectorAll('.preset-card');
            console.log('Found preset cards:', presetCards.length);
            
            presetCards.forEach((card, index) => {
                console.log(`Card ${index}:`, card.dataset.preset);
                
                card.addEventListener('click', function() {
                    console.log('Card clicked:', this.dataset.preset);
                    
                    // 移除所有active类
                    document.querySelectorAll('.preset-card').forEach(c => c.classList.remove('active'));
                    
                    // 添加active类到当前卡片
                    this.classList.add('active');
                    
                    // 设置选中的预设
                    selectedPreset = this.dataset.preset;
                    console.log('Selected preset set to:', selectedPreset);
                    
                    // 更新显示
                    document.getElementById('selectedStrategy').textContent = selectedPreset;
                });
            });
        }

        function applyPreset() {
            if (!selectedPreset) {
                alert('请先选择一个预设策略');
                return;
            }
            
            alert('应用策略: ' + selectedPreset);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('presetModal'));
            if (modal) {
                modal.hide();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 监听模态框显示事件
            const presetModal = document.getElementById('presetModal');
            presetModal.addEventListener('shown.bs.modal', function() {
                console.log('Modal shown, initializing...');
                initPresetStrategies();
            });
        });
    </script>
</body>
</html>
