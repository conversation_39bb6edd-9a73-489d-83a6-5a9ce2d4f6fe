from django.contrib import admin
from .models import (
    StockDividend,
    StockFinancialIndicator,
    StockIndustryChain,
    StockShareholderStatistics,
    TechnicalIndicator,
    StockShareholderDetail,
    StockIndustryPE,
)


@admin.register(StockDividend)
class StockDividendAdmin(admin.ModelAdmin):
    list_display = (
        "stock_code",
        "stock_name",
        "cash_dividend",
        "share_dividend",
        "share_transfer",
        "registration_date",
        "implementation_status",
    )
    list_filter = ("implementation_status", "registration_date")
    search_fields = ("stock_code", "stock_name")
    ordering = ("-registration_date",)


@admin.register(StockFinancialIndicator)
class StockFinancialIndicatorAdmin(admin.ModelAdmin):
    """股票财务指标管理"""

    list_display = (
        "stock_code",
        "report_date",
        "net_profit",
        "total_revenue",
        "eps",
        "roe",
        "last_update",
    )
    list_filter = ("report_date",)
    search_fields = ("stock_code",)
    ordering = ("-report_date",)
    date_hierarchy = "report_date"


@admin.register(StockIndustryChain)
class StockIndustryChainAdmin(admin.ModelAdmin):
    list_display = (
        "stock_code",
        "stock_name",
        "industry_chain",
        "position",
        "market_share",
    )
    list_filter = ("industry_chain", "position")
    search_fields = ("stock_code", "stock_name", "industry_chain")
    ordering = ("stock_code",)


@admin.register(StockShareholderStatistics)
class StockShareholderStatisticsAdmin(admin.ModelAdmin):
    list_display = (
        "shareholder_name",
        "shareholder_type",
        "report_date",
        "count",
        "avg_return_10d",
        "avg_return_30d",
        "avg_return_60d",
    )
    list_filter = ("shareholder_type", "report_date")
    search_fields = ("shareholder_name",)
    ordering = ("-report_date", "shareholder_name")
    list_per_page = 20


@admin.register(TechnicalIndicator)
class TechnicalIndicatorAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "date",
        "indicator_type",
        "indicator_value",
    ]
    search_fields = ["stock_code", "stock_name", "indicator_type"]
    list_filter = ["date", "indicator_type"]
    date_hierarchy = "date"
    ordering = ["-date"]
    list_per_page = 20


@admin.register(StockShareholderDetail)
class StockShareholderDetailAdmin(admin.ModelAdmin):
    list_display = [
        "stock_code",
        "stock_name",
        "shareholder_name",
        "shareholder_type",
        "report_period",
        "holding_amount",
        "holding_change",
        "holding_change_ratio",
        "holding_trend",
    ]
    search_fields = ["stock_code", "stock_name", "shareholder_name"]
    list_filter = ["report_period", "shareholder_type", "holding_trend"]
    date_hierarchy = "report_period"
    ordering = ["-report_period"]
    list_per_page = 20


@admin.register(StockIndustryPE)
class StockIndustryPEAdmin(admin.ModelAdmin):
    list_display = [
        "industry_code",
        "industry_name",
        "trade_date",
        "pe",
        "pe_ttm",
        "pb",
        "ps",
        "dv_ratio",
        "total_mv",
    ]
    search_fields = ["industry_code", "industry_name"]
    list_filter = ["trade_date", "industry_type"]
    date_hierarchy = "trade_date"
    ordering = ["-trade_date"]
    list_per_page = 20
