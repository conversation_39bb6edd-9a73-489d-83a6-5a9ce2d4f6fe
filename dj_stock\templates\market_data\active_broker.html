{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/active_broker.css' %}">
<style>
  .stock-container {
    padding: 0;
    max-width: 100%;
  }

  /* 优化表格单元格内边距 */
  .table td {
    padding: 0.3rem 0.4rem;
    vertical-align: middle;
  }

  /* 表格列宽度控制 */
  .table th, .table td {
    max-width: 300px;
    overflow: hidden;
  }

  /* 表格行悬停效果 */
  .table-hover tbody tr:hover {
    background-color: rgba(32, 107, 196, 0.03);
    transition: background-color 0.2s ease;
  }

  /* 表格头部样式 */
  .table thead th {
    font-weight: 600;
    letter-spacing: 0.5px;
    border-bottom: 2px solid rgba(32, 107, 196, 0.2);
  }

  /* 表格行上下样式 */
  tr.row-up {
    background-color: rgba(220, 53, 69, 0.03);
  }

  tr.row-down {
    background-color: rgba(40, 167, 69, 0.03);
  }

  /* 响应式表格优化 */
  @media (max-width: 1200px) {
    .table th, .table td {
      font-size: 0.85rem;
    }
    .broker-name {
      font-size: 0.8rem;
    }
  }

  @media (max-width: 992px) {
    .table th, .table td {
      font-size: 0.8rem;
      padding: 0.25rem 0.3rem;
    }
    .stock-code {
      font-size: 0.6rem;
    }
    .stock-change {
      font-size: 0.55rem;
    }
  }

  @media (max-width: 768px) {
    .table th, .table td {
      font-size: 0.75rem;
      padding: 0.2rem;
    }
    .stock-card {
      padding: 1px 2px;
      margin: 1px;
    }
    .stock-grid {
      gap: 1px;
    }
  }

  /* 营业部名称列样式 */
  .broker-name {
    max-width: 100%;
    line-height: 1.1;
    white-space: normal;
    word-break: break-all;
    font-size: 0.85rem;
    display: block;
  }

  .stock-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    width: 100%;
  }

  .stock-card {
    display: inline-flex;
    align-items: center;
    padding: 2px 4px;
    border-radius: 3px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    border-left: 2px solid transparent;
    margin: 2px 1px;
    font-size: 0.65rem;
    max-width: fit-content;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  }

  .stock-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 1;
  }

  .stock-card.up {
    background: linear-gradient(to right, rgba(220,53,69,0.1), rgba(255,235,235,0.7));
    border-left-color: #dc3545;
  }

  .stock-card.down {
    background: linear-gradient(to right, rgba(40,167,69,0.1), rgba(235,255,235,0.7));
    border-left-color: #28a745;
  }

  .stock-card.flat {
    background: linear-gradient(to right, rgba(108,117,125,0.1), rgba(240,240,240,0.7));
    border-left-color: #6c757d;
  }

  .stock-header {
    display: inline-flex;
    align-items: center;
    margin-right: 2px;
  }

  .stock-code {
    font-weight: bold;
    font-size: 0.65rem;
    margin-right: 2px;
    letter-spacing: 0.5px;
  }

  .stock-change {
    font-size: 0.6rem;
    padding: 1px 3px;
    border-radius: 3px;
    margin-left: 2px;
    font-weight: 600;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }

  .stock-name {
    font-size: 0.7rem;
    max-width: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    white-space: nowrap;
  }

  .toggle-stocks-btn {
    font-size: 0.75rem;
    padding: 2px 8px;
  }

  .toggle-stocks-btn i {
    transition: transform 0.2s;
  }

  .toggle-stocks-btn.expanded i {
    transform: rotate(180deg);
  }
</style>
{% endblock %}

{% block title %}活跃营业部 - 股票市场数据{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">市场数据</div>
        <h2 class="page-title">
          <i class="ti ti-building-bank me-2 text-primary"></i>活跃营业部
        </h2>
        <div class="text-muted mt-1">龙虎榜中活跃的证券营业部交易数据分析</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <span class="d-none d-sm-inline">
            <a href="#" class="btn btn-outline-primary" id="refresh-data">
              <i class="ti ti-refresh me-1"></i>
              刷新数据
            </a>
          </span>
          <a href="#" class="btn btn-primary d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#filter-modal">
            <i class="ti ti-filter me-1"></i>
            高级筛选
          </a>
          <a href="#" class="btn btn-primary d-sm-none" data-bs-toggle="modal" data-bs-target="#filter-modal">
            <i class="ti ti-filter"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 统计卡片 -->
<div class="container-xl mt-3">
  <div class="row row-deck row-cards mb-3">
    <div class="col-sm-6 col-lg-3">
      <div class="card card-sm">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-auto">
              <span class="bg-primary text-white avatar">
                <i class="ti ti-building-bank"></i>
              </span>
            </div>
            <div class="col">
              <div class="font-weight-medium">
                {{ page_obj.paginator.count|default:0 }} 家
              </div>
              <div class="text-muted">
                活跃营业部总数
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-lg-3">
      <div class="card card-sm">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-auto">
              <span class="bg-red text-white avatar">
                <i class="ti ti-arrow-big-up"></i>
              </span>
            </div>
            <div class="col">
              <div class="font-weight-medium">
                {{ total_buy_amount|default:0|floatformat:2 }} 亿
              </div>
              <div class="text-muted">
                总买入金额
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-lg-3">
      <div class="card card-sm">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-auto">
              <span class="bg-green text-white avatar">
                <i class="ti ti-arrow-big-down"></i>
              </span>
            </div>
            <div class="col">
              <div class="font-weight-medium">
                {{ total_sell_amount|default:0|floatformat:2 }} 亿
              </div>
              <div class="text-muted">
                总卖出金额
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-lg-3">
      <div class="card card-sm">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-auto">
              <span class="bg-azure text-white avatar">
                <i class="ti ti-chart-bar"></i>
              </span>
            </div>
            <div class="col">
              <div class="font-weight-medium">
                {{ total_net_amount|default:0|floatformat:2 }} 亿
              </div>
              <div class="text-muted">
                净买入金额
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 当前筛选条件 -->
  <div class="filter-badges mb-3">
    {% if broker_name or date %}
      {% if broker_name %}
      <span class="badge bg-blue-lt me-2">
        <i class="ti ti-building me-1"></i>营业部: {{ broker_name }}
        <a href="?{% if date %}date={{ date }}{% endif %}" class="ms-1 text-decoration-none">×</a>
      </span>
      {% endif %}

      {% if date %}
      <span class="badge bg-purple-lt me-2">
        <i class="ti ti-calendar me-1"></i>日期: {{ date }}
        <a href="?{% if broker_name %}broker_name={{ broker_name }}{% endif %}" class="ms-1 text-decoration-none">×</a>
      </span>
      {% endif %}

      <a href="{% url 'market_data:active_broker_list' %}" class="btn btn-sm btn-outline-secondary">
        <i class="ti ti-trash me-1"></i>清除筛选
      </a>
    {% endif %}
  </div>

  <!-- 筛选模态框 -->
  <div class="modal modal-blur fade" id="filter-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">高级筛选</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="get">
          <div class="modal-body">
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label">营业部名称</label>
                <div class="input-icon">
                  <span class="input-icon-addon">
                    <i class="ti ti-building"></i>
                  </span>
                  <input type="text" class="form-control" name="broker_name" value="{{ broker_name }}" placeholder="输入营业部名称">
                </div>
              </div>
              <div class="col-md-6">
                <label class="form-label">交易日期</label>
                <select class="form-select" name="date">
                  <option value="">全部日期</option>
                  {% for date_obj in available_dates %}
                  <option value="{{ date_obj|date:'Y-m-d' }}" {% if date == date_obj|date:'Y-m-d' %}selected{% endif %}>{{ date_obj|date:'Y-m-d' }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label">排序方式</label>
                <select class="form-select" name="sort">
                  <option value="-trade_date" {% if sort == '-trade_date' %}selected{% endif %}>交易日期降序</option>
                  <option value="trade_date" {% if sort == 'trade_date' %}selected{% endif %}>交易日期升序</option>
                  <option value="-buy_amount" {% if sort == '-buy_amount' %}selected{% endif %}>买入金额降序</option>
                  <option value="buy_amount" {% if sort == 'buy_amount' %}selected{% endif %}>买入金额升序</option>
                  <option value="-sell_amount" {% if sort == '-sell_amount' %}selected{% endif %}>卖出金额降序</option>
                  <option value="sell_amount" {% if sort == 'sell_amount' %}selected{% endif %}>卖出金额升序</option>
                  <option value="-net_amount" {% if sort == '-net_amount' %}selected{% endif %}>净额降序</option>
                  <option value="net_amount" {% if sort == 'net_amount' %}selected{% endif %}>净额升序</option>
                </select>
              </div>
              <div class="col-md-6">
                <label class="form-label">每页显示数量</label>
                <select class="form-select" name="per_page">
                  <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                  <option value="20" {% if per_page == 20 or not per_page %}selected{% endif %}>20</option>
                  <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                  <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                </select>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <a href="{% url 'market_data:active_broker_list' %}" class="btn btn-link link-secondary">重置</a>
            <button type="submit" class="btn btn-primary ms-auto">
              <i class="ti ti-filter me-1"></i>应用筛选
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

    <div class="card shadow-sm">
      <div class="card-header d-flex align-items-center">
        <h3 class="card-title">
          <i class="ti ti-list me-2"></i>活跃营业部列表
          <span class="badge bg-blue-lt ms-2">{{ page_obj.paginator.count }} 条记录</span>
        </h3>
        <div class="ms-auto">
          <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="ti ti-download me-1"></i>导出数据
            </button>
            <div class="dropdown-menu dropdown-menu-end">
              <a class="dropdown-item" href="#">
                <i class="ti ti-file-spreadsheet me-1"></i>导出为Excel
              </a>
              <a class="dropdown-item" href="#">
                <i class="ti ti-file-csv me-1"></i>导出为CSV
              </a>
              <a class="dropdown-item" href="#">
                <i class="ti ti-file-text me-1"></i>导出为PDF
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="d-flex align-items-center">
            <span class="badge bg-blue-lt">
              <i class="ti ti-list me-1"></i>共 {{ page_obj.paginator.count }} 条记录
            </span>
          </div>
          <div class="ms-auto d-flex align-items-center">
            <span class="text-muted me-2">排序方式:</span>
            <select class="form-select form-select-sm" id="sort-by">
              <option value="-trade_date" {% if sort == '-trade_date' %}selected{% endif %}>交易日期降序</option>
              <option value="-buy_amount" {% if sort == '-buy_amount' %}selected{% endif %}>买入金额降序</option>
              <option value="-sell_amount" {% if sort == '-sell_amount' %}selected{% endif %}>卖出金额降序</option>
              <option value="-net_amount" {% if sort == '-net_amount' %}selected{% endif %}>净额降序</option>
            </select>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter table-hover table-striped">
          <thead class="sticky-top bg-azure-lt text-azure-dk">
            <tr>
              <th class="w-1">序号</th>
              <th style="width: 12%">营业部名称</th>
              <th style="width: 7%">上榜日期</th>
              <th class="text-center" style="width: 4%">买入</th>
              <th class="text-center" style="width: 4%">卖出</th>
              <th class="text-end" style="width: 7%">买入(万)</th>
              <th class="text-end" style="width: 7%">卖出(万)</th>
              <th class="text-end" style="width: 7%">净额(万)</th>
              <th style="width: 47%">买入股票</th>
              <th class="w-1">操作</th>
            </tr>
          </thead>
          <tbody>
            {% for item in page_obj %}
            <tr class="{% if item.net_amount > 0 %}row-up{% elif item.net_amount < 0 %}row-down{% endif %}">
              <td class="text-muted">{{ page_obj.start_index|add:forloop.counter0 }}</td>
              <td>
                <div class="d-flex align-items-start">
                  <span class="avatar {% if 'deep' in item.broker_name %}bg-azure{% elif 'china' in item.broker_name %}bg-red{% elif 'guo' in item.broker_name %}bg-orange{% else %}bg-blue-lt{% endif %} me-2">
                    {{ item.broker_name|slice:"0:1" }}
                  </span>
                  <a href="{% url 'market_data:active_broker_detail' item.broker_name %}" class="text-reset fw-bold broker-name" data-bs-toggle="tooltip" title="{{ item.broker_name }}">
                    {{ item.broker_name }}
                  </a>
                </div>
              </td>
              <td>
                <div class="d-flex align-items-center">
                  <span class="avatar avatar-xs bg-azure-lt me-1">
                    <i class="ti ti-calendar-event"></i>
                  </span>
                  <span class="text-azure" title="{{ item.trade_date|date:"Y年m月d日" }}">
                    {{ item.trade_date|date:"Y-m-d" }}
                  </span>
                </div>
              </td>
              <td class="text-center">
                <span class="badge bg-red-lt" data-bs-toggle="tooltip" title="买入{{ item.buy_stock_count }}只股票">
                  <i class="ti ti-arrow-big-up-lines me-1"></i>{{ item.buy_stock_count }}
                </span>
              </td>
              <td class="text-center">
                <span class="badge bg-green-lt" data-bs-toggle="tooltip" title="卖出{{ item.sell_stock_count }}只股票">
                  <i class="ti ti-arrow-big-down-lines me-1"></i>{{ item.sell_stock_count }}
                </span>
              </td>
              <td class="text-end">
                <div class="d-flex justify-content-end align-items-center">
                  <span class="text-danger fw-bold" data-bs-toggle="tooltip" title="买入金额: {{ item.buy_amount|floatformat:2 }}万元">
                    {{ item.buy_amount|floatformat:2 }}
                  </span>
                </div>
              </td>
              <td class="text-end">
                <div class="d-flex justify-content-end align-items-center">
                  <span class="text-success fw-bold" data-bs-toggle="tooltip" title="卖出金额: {{ item.sell_amount|floatformat:2 }}万元">
                    {{ item.sell_amount|floatformat:2 }}
                  </span>
                </div>
              </td>
              <td class="text-end">
                <div class="d-flex justify-content-end align-items-center">
                  {% if item.net_amount > 0 %}
                  <span class="badge bg-red-lt me-1" data-bs-toggle="tooltip" title="净买入">
                    <i class="ti ti-trending-up"></i>
                  </span>
                  <span class="text-danger fw-bold" data-bs-toggle="tooltip" title="净买入: {{ item.net_amount|floatformat:2 }}万元">
                    {{ item.net_amount|floatformat:2 }}
                  </span>
                  {% elif item.net_amount < 0 %}
                  <span class="badge bg-green-lt me-1" data-bs-toggle="tooltip" title="净卖出">
                    <i class="ti ti-trending-down"></i>
                  </span>
                  <span class="text-success fw-bold" data-bs-toggle="tooltip" title="净卖出: {{ item.net_amount|floatformat:2 }}万元">
                    {{ item.net_amount|floatformat:2 }}
                  </span>
                  {% else %}
                  <span class="text-muted">0.00</span>
                  {% endif %}
                </div>
              </td>
              <td>
                <div class="stock-container">
                  <div class="stock-grid">
                    {% for stock in item.parsed_buy_stocks %}
                      <a href="{% url 'market_data:stock_detail' stock.code %}" class="stock-card {% if stock.change_percent > 0 %}up{% elif stock.change_percent < 0 %}down{% else %}flat{% endif %}">
                        <span class="stock-code">{{ stock.code }}</span>{% if stock.change_percent is not None %}<span class="stock-change {% if stock.change_percent > 0 %}bg-danger{% elif stock.change_percent < 0 %}bg-success{% else %}bg-secondary{% endif %}">{% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:1 }}%</span>{% endif %}
                      </a>
                    {% endfor %}
                  </div>
                </div>
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{% url 'market_data:active_broker_detail' item.broker_name %}" class="btn btn-sm btn-icon btn-primary" title="查看详情">
                    <i class="ti ti-eye"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="10" class="text-center py-5">
                <div class="empty">
                  <div class="empty-img">
                    <img src="/static/img/undraw_no_data.svg" height="128" alt="暂无数据">
                  </div>
                  <p class="empty-title">暂无活跃营业部数据</p>
                  <p class="empty-subtitle text-muted">
                    当前筛选条件下没有找到相关数据，请尝试调整筛选条件。
                  </p>
                  <div class="empty-action">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filter-modal">
                      <i class="ti ti-filter me-2"></i>重新筛选
                    </button>
                    <a href="{% url 'market_data:active_broker_list' %}" class="btn btn-outline-secondary ms-2">
                      <i class="ti ti-refresh me-2"></i>重置所有条件
                    </a>
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% include 'includes/pagination.html' with page_obj=page_obj rows_per_page=rows_per_page %}
    </div>
  </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 切换股票展示
  const toggleButtons = document.querySelectorAll('.toggle-stocks-btn');

  toggleButtons.forEach(button => {
    button.addEventListener('click', function() {
      const rowId = this.dataset.rowId;
      const expandIcon = document.querySelector(`.expand-icon-${rowId}`);
      const toggleText = document.querySelector(`.toggle-text-${rowId}`);
      const expandedGrid = document.querySelector(`.stock-grid-${rowId}`);

      if (expandedGrid.style.display === 'none') {
        expandedGrid.style.display = 'grid';
        this.classList.add('expanded');
        expandIcon.classList.add('rotate-180');
        toggleText.textContent = '收起';
      } else {
        expandedGrid.style.display = 'none';
        this.classList.remove('expanded');
        expandIcon.classList.remove('rotate-180');
        toggleText.textContent = `显示全部 ${this.closest('.stock-container').querySelectorAll('.stock-card').length - 6} 只股票`;
      }
    });
  });

  // 已移除每页显示数量切换功能

  // 排序方式切换
  const sortBySelect = document.getElementById('sort-by');
  if (sortBySelect) {
    sortBySelect.addEventListener('change', function() {
      const sortBy = this.value;
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.set('sort', sortBy);
      window.location.href = currentUrl.toString();
    });
  }

  // 刷新数据按钮
  const refreshBtn = document.getElementById('refresh-data');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', function(e) {
      e.preventDefault();
      this.classList.add('btn-loading');
      this.disabled = true;

      // 模拟刷新数据的加载状态
      setTimeout(() => {
        window.location.reload();
      }, 800);
    });
  }

  // 高亮表格行
  const tableRows = document.querySelectorAll('tbody tr');
  tableRows.forEach(row => {
    row.addEventListener('mouseenter', function() {
      this.classList.add('bg-hover');
    });
    row.addEventListener('mouseleave', function() {
      this.classList.remove('bg-hover');
    });
  });

  // 添加动画效果
  document.querySelectorAll('.text-danger, .text-success').forEach(el => {
    el.classList.add('animate-pulse');
    setTimeout(() => {
      el.classList.remove('animate-pulse');
    }, 2000);
  });

  // 添加自定义样式
  const style = document.createElement('style');
  style.textContent = `
    .bg-hover {
      background-color: rgba(32, 107, 196, 0.03);
    }
    .animate-pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) 1;
    }
    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.7;
      }
    }
    .sticky-top {
      position: sticky;
      top: 0;
      z-index: 1;
    }
    .rotate-180 {
      transform: rotate(180deg);
    }
    .row-up {
      border-left: 3px solid var(--color-up);
    }
    .row-down {
      border-left: 3px solid var(--color-down);
    }
  `;
  document.head.appendChild(style);
});
</script>
{% endblock %}
{% endblock %}