{% extends "base.html" %} {% block title %}股东统计 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">特色分析</div>
        <h2 class="page-title">十大股东统计</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:index' %}" class="btn btn-outline-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="icon"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M5 12l-2 0l9 -9l9 9l-2 0" />
              <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
              <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />
            </svg>
            返回首页
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">十大股东持股统计</h3>
            <div class="card-actions">
              <form method="get" action="{% url 'financial_analysis:shareholder_statistics' stock_code %}">
                <div class="input-group">
                  <input type="text" class="form-control" placeholder="搜索股东名称..." name="search" value="{{ search_query }}" />
                  <select class="form-select" name="shareholder_type">
                    <option value="">所有股东类型</option>
                    <option value="机构" {% if shareholder_type == '机构' %}selected{% endif %}>机构</option>
                    <option value="个人" {% if shareholder_type == '个人' %}selected{% endif %}>个人</option>
                    <option value="基金" {% if shareholder_type == '基金' %}selected{% endif %}>基金</option>
                    <option value="QFII" {% if shareholder_type == 'QFII' %}selected{% endif %}>QFII</option>
                    <option value="社保" {% if shareholder_type == '社保' %}selected{% endif %}>社保</option>
                  </select>
                  <button type="submit" class="btn btn-primary">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" />
                      <path d="M21 21l-6 -6" />
                    </svg>
                    搜索
                  </button>
                </div>
              </form>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter card-table">
                <thead>
                  <tr>
                    <th>股东名称</th>
                    <th>股东类型</th>
                    <th>统计截止日期</th>
                    <th>持股数量</th>
                    <th>10日平均涨幅</th>
                    <th>10日最大涨幅</th>
                    <th>30日平均涨幅</th>
                    <th>60日平均涨幅</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in page_obj %}
                  <tr>
                    <td>{{ item.shareholder_name }}</td>
                    <td>{{ item.shareholder_type }}</td>
                    <td>{{ item.report_date|date:"Y-m-d" }}</td>
                    <td>{{ item.count }}</td>
                    <td class="{% if item.avg_return_10d > 0 %}text-green{% elif item.avg_return_10d < 0 %}text-red{% endif %}">
                      {{ item.avg_return_10d|floatformat:2 }}%
                    </td>
                    <td class="{% if item.max_return_10d > 0 %}text-green{% elif item.max_return_10d < 0 %}text-red{% endif %}">
                      {{ item.max_return_10d|floatformat:2 }}%
                    </td>
                    <td class="{% if item.avg_return_30d > 0 %}text-green{% elif item.avg_return_30d < 0 %}text-red{% endif %}">
                      {{ item.avg_return_30d|floatformat:2 }}%
                    </td>
                    <td class="{% if item.avg_return_60d > 0 %}text-green{% elif item.avg_return_60d < 0 %}text-red{% endif %}">
                      {{ item.avg_return_60d|floatformat:2 }}%
                    </td>
                    <td>
                      <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#holdings-{{ forloop.counter }}">
                        查看持股
                      </button>
                    </td>
                  </tr>

                  <!-- 持股明细弹窗 -->
                  <div class="modal modal-blur fade" id="holdings-{{ forloop.counter }}" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h5 class="modal-title">{{ item.shareholder_name }} 持股明细</h5>
                          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                          <div class="markdown">
                            {% if item.holding_stocks %}
                              <div class="table-responsive">
                                <table class="table table-vcenter">
                                  <thead>
                                    <tr>
                                      <th>股票代码</th>
                                      <th>股票名称</th>
                                      <th>持仓比例</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <!-- 改为在视图中处理持股数据，这里只展示处理后的结果 -->
                                    {% if item.parsed_holdings %}
                                      {% for stock in item.parsed_holdings %}
                                        <tr>
                                          <td>{{ stock.code }}</td>
                                          <td>{{ stock.name }}</td>
                                          <td>{{ stock.ratio }}%</td>
                                        </tr>
                                      {% endfor %}
                                    {% else %}
                                      <tr>
                                        <td colspan="3" class="text-center">持股数据格式需转换，请在views.py中处理</td>
                                      </tr>
                                    {% endif %}
                                  </tbody>
                                </table>
                              </div>
                            {% else %}
                              <p class="text-muted">暂无持股明细数据</p>
                            {% endif %}
                          </div>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                      </div>
                    </div>
                  </div>
                  {% empty %}
                  <tr>
                    <td colspan="9" class="text-center">暂无股东统计数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% include "includes/pagination.html" with page_obj=page_obj total_items=total_items %}
          </div>
        </div>
      </div>
    </div>

    <!-- 股东分析说明 -->
    <div class="row mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">股东分析指标说明</h3>
          </div>
          <div class="card-body">
            <div class="datagrid">
              <div class="datagrid-item">
                <div class="datagrid-title">持股数量</div>
                <div class="datagrid-content">指该股东出现在上市公司十大股东的次数，数值越大表示该股东参与的投资越广泛</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">平均涨幅</div>
                <div class="datagrid-content">该股东所持有的所有股票在特定时间段内的平均收益率</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">最大涨幅</div>
                <div class="datagrid-content">该股东所持有的股票在特定时间段内的最大收益率</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">最小涨幅</div>
                <div class="datagrid-content">该股东所持有的股票在特定时间段内的最小收益率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}