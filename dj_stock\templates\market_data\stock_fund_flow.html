{% extends 'base.html' %}

{% block title %}个股资金流向 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">个股资金流向</h2>
        <div class="text-muted mt-1">查看个股的资金流向数据</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <form method="get" class="d-flex">
              <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
              <select class="form-select ms-2" name="sort">
                <option value="-main_net_inflow" {% if sort_by == '-main_net_inflow' %}selected{% endif %}>主力净流入降序</option>
                <option value="main_net_inflow" {% if sort_by == 'main_net_inflow' %}selected{% endif %}>主力净流入升序</option>
                <option value="-total_net_inflow" {% if sort_by == '-total_net_inflow' %}selected{% endif %}>总净流入降序</option>
                <option value="total_net_inflow" {% if sort_by == 'total_net_inflow' %}selected{% endif %}>总净流入升序</option>
              </select>
              <input type="text" class="form-control ms-2" name="q" placeholder="搜索股票代码或名称" value="{{ search_query }}" />
              <button type="submit" class="btn ms-2">查询</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 个股资金流向表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">个股资金流向数据 ({{ selected_date|date:"Y-m-d" }})</h3>
      </div>
      <!-- 移除了显示记录条数的选择器 -->
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>主力净流入</th>
              <th>散户净流入</th>
              <th>总净流入</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for stock in stocks %}
            <tr>
              <td>{{ stock.code }}</td>
              <td>{{ stock.name }}</td>
              <td>
                {% if stock.main_net_inflow > 0 %}
                <span class="text-up">{{ stock.main_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ stock.main_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                {% if stock.retail_net_inflow > 0 %}
                <span class="text-up">{{ stock.retail_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ stock.retail_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                {% if stock.total_net_inflow > 0 %}
                <span class="text-up">{{ stock.total_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ stock.total_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                <a href="{% url 'market_data:stock_fund_flow_detail' stock.code %}" class="btn btn-sm">详情</a>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="6" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前日期没有个股资金流向数据，请尝试选择其他日期。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% include 'includes/pagination.html' with page_obj=stocks rows_per_page=rows_per_page show_flow_indicator=True %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 页面大小切换功能已移除
  });
</script>
{% endblock %}
