/* 登录页面样式 */
:root {
  --primary-color: #066fd1;
  --secondary-color: #f6f8fb;
  --text-color: #182433;
  --border-color: #dce1e7;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --success-color: #2fb344;
  --error-color: #d63939;
}

body {
  font-family: 'Inter Var', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #f6f8fb 0%, #e2e8f0 100%);
  height: 100vh;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-wrapper {
  width: 100%;
  max-width: 420px;
  padding: 0 20px;
}

.login-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.login-header {
  padding: 25px 30px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #0559a7 100%);
  text-align: center;
}

.login-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  text-decoration: none;
}

.login-brand i {
  font-size: 1.8rem;
  margin-right: 10px;
}

.login-form {
  padding: 30px;
}

.form-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #49566c;
  margin-bottom: 8px;
}

.form-label i {
  color: var(--primary-color);
}

.form-control {
  height: 48px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 0 16px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(6, 111, 209, 0.15);
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin-top: 0.2rem;
}

.form-check-label {
  font-size: 0.9rem;
  color: #6c7a91;
}

.btn-login {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #0559a7 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-login:hover {
  background: linear-gradient(135deg, #0559a7 0%, #044e94 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(6, 111, 209, 0.2);
}

.btn-login:active {
  transform: translateY(0);
}

.alert {
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.alert-danger {
  background-color: rgba(214, 57, 57, 0.1);
  border-left: 4px solid var(--error-color);
  color: #b92c2c;
}

.alert i {
  font-size: 1.2rem;
  margin-right: 10px;
}

.login-footer {
  text-align: center;
  margin-top: 25px;
  color: #6c7a91;
  font-size: 0.85rem;
}

.login-footer a {
  color: #49566c;
  text-decoration: none;
  transition: color 0.2s ease;
}

.login-footer a:hover {
  color: var(--primary-color);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card {
  animation: fadeIn 0.5s ease-out forwards;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-wrapper {
    padding: 0 15px;
  }
  
  .login-form {
    padding: 20px;
  }
  
  .login-header {
    padding: 20px;
  }
}
