{% extends "base.html" %}
{% block title %}市场指数列表 | 股票数据分析系统{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>市场指数列表</h2>
    <div class="d-flex">
      <form class="d-flex me-2" method="get">
        <input type="text" name="search" class="form-control me-2" placeholder="搜索指数代码或名称" value="{{ search_query }}">
        <button type="submit" class="btn btn-primary">搜索</button>
      </form>
      <!-- 日期选择器 -->
      <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="dateDropdown" data-bs-toggle="dropdown" aria-expanded="false">
          日期: {{ selected_date|date:"Y-m-d" }}
        </button>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dateDropdown" style="max-height: 300px; overflow-y: auto;">
          {% for date in available_dates %}
          <li><a class="dropdown-item {% if date == selected_date %}active{% endif %}" href="?date={{ date|date:"Y-m-d" }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ date|date:"Y-m-d" }}</a></li>
          {% endfor %}
        </ul>
      </div>
    </div>
  </div>

  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
      <div class="d-flex justify-content-between align-items-center">
        <span>共找到 {{ total_items }} 条记录</span>
        <!-- 排序选项 -->
        <div class="dropdown">
          <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
            排序:
            {% if sort_by == "index_code" %}指数代码 ⬆{% elif sort_by == "-index_code" %}指数代码 ⬇
            {% elif sort_by == "index_name" %}指数名称 ⬆{% elif sort_by == "-index_name" %}指数名称 ⬇
            {% elif sort_by == "close_price" %}收盘价 ⬆{% elif sort_by == "-close_price" %}收盘价 ⬇
            {% elif sort_by == "change_percent" %}涨跌幅 ⬆{% elif sort_by == "-change_percent" %}涨跌幅 ⬇
            {% elif sort_by == "amount" %}成交额 ⬆{% elif sort_by == "-amount" %}成交额 ⬇
            {% else %}日期 ⬇{% endif %}
          </button>
          <ul class="dropdown-menu dropdown-menu-end">
            <li><a class="dropdown-item {% if sort_by == '-trade_date' %}active{% endif %}" href="?sort=-trade_date{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">日期 ⬇</a></li>
            <li><a class="dropdown-item {% if sort_by == 'trade_date' %}active{% endif %}" href="?sort=trade_date{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">日期 ⬆</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item {% if sort_by == 'index_code' %}active{% endif %}" href="?sort=index_code{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">指数代码 ⬆</a></li>
            <li><a class="dropdown-item {% if sort_by == '-index_code' %}active{% endif %}" href="?sort=-index_code{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">指数代码 ⬇</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item {% if sort_by == 'index_name' %}active{% endif %}" href="?sort=index_name{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">指数名称 ⬆</a></li>
            <li><a class="dropdown-item {% if sort_by == '-index_name' %}active{% endif %}" href="?sort=-index_name{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">指数名称 ⬇</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item {% if sort_by == 'close_price' %}active{% endif %}" href="?sort=close_price{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">收盘价 ⬆</a></li>
            <li><a class="dropdown-item {% if sort_by == '-close_price' %}active{% endif %}" href="?sort=-close_price{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">收盘价 ⬇</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item {% if sort_by == 'change_percent' %}active{% endif %}" href="?sort=change_percent{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">涨跌幅 ⬆</a></li>
            <li><a class="dropdown-item {% if sort_by == '-change_percent' %}active{% endif %}" href="?sort=-change_percent{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">涨跌幅 ⬇</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item {% if sort_by == 'amount' %}active{% endif %}" href="?sort=amount{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">成交额 ⬆</a></li>
            <li><a class="dropdown-item {% if sort_by == '-amount' %}active{% endif %}" href="?sort=-amount{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}">成交额 ⬇</a></li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover align-middle">
          <thead class="table-light">
            <tr>
              <th>
                <a href="?sort={% if sort_by == 'index_code' %}-index_code{% else %}index_code{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  指数代码
                  {% if sort_by == 'index_code' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-index_code' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'index_name' %}-index_name{% else %}index_name{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  指数名称
                  {% if sort_by == 'index_name' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-index_name' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'trade_date' %}-trade_date{% else %}trade_date{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  交易日期
                  {% if sort_by == 'trade_date' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-trade_date' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'open_price' %}-open_price{% else %}open_price{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  开盘价
                  {% if sort_by == 'open_price' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-open_price' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'close_price' %}-close_price{% else %}close_price{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  收盘价
                  {% if sort_by == 'close_price' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-close_price' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'high_price' %}-high_price{% else %}high_price{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  最高价
                  {% if sort_by == 'high_price' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-high_price' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'low_price' %}-low_price{% else %}low_price{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  最低价
                  {% if sort_by == 'low_price' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-low_price' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'change_percent' %}-change_percent{% else %}change_percent{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  涨跌幅
                  {% if sort_by == 'change_percent' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-change_percent' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'volume' %}-volume{% else %}volume{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  成交量(亿)
                  {% if sort_by == 'volume' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-volume' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>
                <a href="?sort={% if sort_by == 'amount' %}-amount{% else %}amount{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:"Y-m-d" }}{% endif %}" class="text-reset d-block">
                  成交额(亿)
                  {% if sort_by == 'amount' %}<i class="ti ti-arrow-up"></i>{% elif sort_by == '-amount' %}<i class="ti ti-arrow-down"></i>{% endif %}
                </a>
              </th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for index in page_obj %}
            <tr>
              <td>{{ index.index_code }}</td>
              <td><a href="{% url 'market_data:market_index_detail' index.index_code %}">{{ index.index_name }}</a></td>
              <td>{{ index.trade_date|date:"Y-m-d" }}</td>
              <td>{{ index.open_price|floatformat:2 }}</td>
              <td>{{ index.close_price|floatformat:2 }}</td>
              <td>{{ index.high_price|floatformat:2 }}</td>
              <td>{{ index.low_price|floatformat:2 }}</td>
              <td class="{% if index.change_percent > 0 %}text-danger{% elif index.change_percent < 0 %}text-success{% endif %}">
                {{ index.change_percent|floatformat:2 }}%
              </td>
              <td>{{ index.volume|floatformat:2 }}</td>
              <td>{{ index.amount|floatformat:2 }}</td>
              <td>
                <a href="{% url 'market_data:market_index_detail' index.index_code %}" class="btn btn-sm btn-primary">详情</a>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="11" class="text-center py-3">没有找到市场指数数据</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% include 'includes/pagination.html' with page_obj=page_obj rows_per_page=rows_per_page %}
  </div>
</div>
{% endblock %}