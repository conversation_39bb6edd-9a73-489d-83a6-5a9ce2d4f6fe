#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
财务指标映射匹配检查工具

该脚本用于：
1. 检查akshare API返回的指标与financial_fetcher.py中映射字典的匹配情况
2. 找出缺失的指标映射
3. 找出多余的指标映射
4. 帮助维护指标映射的完整性

使用方法：
python check_indicators_match.py
"""

import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dj_stock.settings')
import django
django.setup()
import akshare as ak
import pandas as pd

# 获取财务摘要数据
df = ak.stock_financial_abstract(symbol='600000')

# 获取所有指标名称
indicators = df['指标'].tolist()

# 从financial_fetcher.py中获取指标映射字典
from financial_analysis.utils.financial_fetcher import FinancialDataFetcher
fetcher = FinancialDataFetcher()

# 获取指标映射字典
indicator_mapping = {}
try:
    for line in open('financial_analysis/utils/financial_fetcher.py', 'r', encoding='utf-8'):
        if '"' in line and ':' in line and 'indicator_mapping' in line:
            parts = line.split('"')
            if len(parts) >= 3:
                key = parts[1]
                value_part = parts[2].split('"')[1] if len(parts[2].split('"')) > 1 else None
                if value_part:
                    indicator_mapping[key] = value_part
except FileNotFoundError:
    print("警告：未找到 financial_fetcher.py 文件")

print("=== 财务指标映射匹配检查结果 ===")

# 检查哪些指标在API返回中存在但在映射字典中不存在
missing_indicators = [ind for ind in indicators if ind not in indicator_mapping]
print(f"\nAPI返回的指标但在映射字典中不存在 ({len(missing_indicators)} 个):")
for ind in missing_indicators:
    print(f"  - {ind}")

# 检查哪些指标在映射字典中存在但在API返回中不存在
extra_indicators = [ind for ind in indicator_mapping if ind not in indicators]
print(f"\n映射字典中存在但在API返回中不存在的指标 ({len(extra_indicators)} 个):")
for ind in extra_indicators:
    print(f"  - {ind}")

# 打印匹配的指标
matching_indicators = [ind for ind in indicators if ind in indicator_mapping]
print(f"\n=== 匹配统计 ===")
print(f"匹配的指标数量: {len(matching_indicators)}/{len(indicators)}")
print(f"匹配率: {len(matching_indicators)/len(indicators)*100:.1f}%")

print(f"\n匹配的指标:")
for ind in matching_indicators:
    print(f"  - {ind} -> {indicator_mapping[ind]}")
