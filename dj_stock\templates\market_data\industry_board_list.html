{% extends "base.html" %} {% block title %}行业板块列表 | 股票数据分析系统{% endblock %} {% block search_form %}
<form class="w-100" action="{% url 'market_data:industry_board_list' %}" method="get">
  <input class="form-control form-control-dark w-100" type="text" name="search" placeholder="搜索行业板块 (板块代码或名称)" value="{{ search_query }}" />
</form>
{% endblock %} {% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
  <h1 class="h2">行业板块列表</h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    {% if available_dates %}
    <div class="dropdown me-2">
      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
        {% if selected_date %}{{ selected_date|date:"Y-m-d" }}{% else %}选择日期{% endif %}
      </button>
      <ul class="dropdown-menu">
        {% for date in available_dates %}
        <li>
          <a
            class="dropdown-item {% if date == selected_date %}active{% endif %}"
            href="?date={{ date|date:'Y-m-d' }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
          >
            {{ date|date:"Y-m-d" }}
          </a>
        </li>
        {% endfor %}
      </ul>
    </div>
    {% endif %}
    <div class="dropdown">
      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">排序方式</button>
      <ul class="dropdown-menu">
        <li>
          <a
            class="dropdown-item {% if sort_by == '-change_percent' %}active{% endif %}"
            href="?sort=-change_percent{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            涨跌幅 (降序)
          </a>
        </li>
        <li>
          <a
            class="dropdown-item {% if sort_by == 'change_percent' %}active{% endif %}"
            href="?sort=change_percent{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            涨跌幅 (升序)
          </a>
        </li>
        <li>
          <a
            class="dropdown-item {% if sort_by == '-total_market_value' %}active{% endif %}"
            href="?sort=-total_market_value{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            总市值 (降序)
          </a>
        </li>
        <li>
          <a
            class="dropdown-item {% if sort_by == '-turnover_rate' %}active{% endif %}"
            href="?sort=-turnover_rate{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            换手率 (降序)
          </a>
        </li>
        <li>
          <a
            class="dropdown-item {% if sort_by == '-up_count' %}active{% endif %}"
            href="?sort=-up_count{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            上涨家数 (降序)
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>

<div class="table-responsive mb-4">
  <div class="d-flex justify-content-between mb-2">
    <p class="text-muted">共找到 {{ total_items }} 个行业板块{% if selected_date %}（{{ selected_date|date:"Y-m-d" }}）{% endif %}</p>
  </div>

  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th scope="col">排名</th>
        <th scope="col">板块代码</th>
        <th scope="col">板块名称</th>
        <th scope="col">最新价</th>
        <th scope="col">涨跌额</th>
        <th scope="col">涨跌幅</th>
        <th scope="col">总市值(亿)</th>
        <th scope="col">换手率</th>
        <th scope="col">领涨股</th>
        <th scope="col">领涨股涨幅</th>
        <th scope="col">上涨/下跌</th>
      </tr>
    </thead>
    <tbody>
      {% for board in page_obj %}
      <tr>
        <td>{{ forloop.counter0|add:page_obj.start_index }}</td>
        <td>{{ board.board_code }}</td>
        <td>
          <a href="{% url 'market_data:industry_board_detail' board.board_code %}"> {{ board.board_name }} </a>
        </td>
        <td>{{ board.latest_price|floatformat:2 }}</td>
        <td>{{ board.change_amount|floatformat:2 }}</td>
        <td class="{% if board.change_percent > 0 %}text-danger{% elif board.change_percent < 0 %}text-success{% endif %}">
          {{ board.change_percent|floatformat:2 }}%
        </td>
        <td>{{ board.total_market_value|floatformat:2 }}</td>
        <td>{{ board.turnover_rate|floatformat:2 }}%</td>
        <td>
          <a href="{% url 'market_data:stock_detail' board.leading_stock %}"> {{ board.leading_stock }} </a>
        </td>
        <td class="{% if board.leading_stock_change_percent > 0 %}text-danger{% elif board.leading_stock_change_percent < 0 %}text-success{% endif %}">
          {{ board.leading_stock_change_percent|floatformat:2 }}%
        </td>
        <td>
          <span class="text-danger">{{ board.up_count }}</span> /
          <span class="text-success">{{ board.down_count }}</span>
        </td>
      </tr>
      {% empty %}
      <tr>
        <td colspan="11" class="text-center">暂无行业板块数据</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<!-- 分页 -->
{% if page_obj.has_other_pages %}
<nav aria-label="Page navigation">
  <ul class="pagination justify-content-center">
    {% if page_obj.has_previous %}
    <li class="page-item">
      <a
        class="page-link"
        href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
        aria-label="First"
      >
        <span aria-hidden="true">&laquo;&laquo;</span>
      </a>
    </li>
    <li class="page-item">
      <a
        class="page-link"
        href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
        aria-label="Previous"
      >
        <span aria-hidden="true">&laquo;</span>
      </a>
    </li>
    {% else %}
    <li class="page-item disabled">
      <a class="page-link" href="#" aria-label="First">
        <span aria-hidden="true">&laquo;&laquo;</span>
      </a>
    </li>
    <li class="page-item disabled">
      <a class="page-link" href="#" aria-label="Previous">
        <span aria-hidden="true">&laquo;</span>
      </a>
    </li>
    {% endif %} {% for num in page_obj.paginator.page_range %} {% if page_obj.number == num %}
    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
    <li class="page-item">
      <a
        class="page-link"
        href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
        >{{ num }}</a
      >
    </li>
    {% endif %} {% endfor %} {% if page_obj.has_next %}
    <li class="page-item">
      <a
        class="page-link"
        href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
        aria-label="Next"
      >
        <span aria-hidden="true">&raquo;</span>
      </a>
    </li>
    <li class="page-item">
      <a
        class="page-link"
        href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
        aria-label="Last"
      >
        <span aria-hidden="true">&raquo;&raquo;</span>
      </a>
    </li>
    {% else %}
    <li class="page-item disabled">
      <a class="page-link" href="#" aria-label="Next">
        <span aria-hidden="true">&raquo;</span>
      </a>
    </li>
    <li class="page-item disabled">
      <a class="page-link" href="#" aria-label="Last">
        <span aria-hidden="true">&raquo;&raquo;</span>
      </a>
    </li>
    {% endif %}
  </ul>
</nav>
{% endif %}

<div class="card mt-4 mb-4">
  <div class="card-header">
    <h5 class="mb-0">数据说明</h5>
  </div>
  <div class="card-body">
    <p><strong>行业板块</strong>：根据上市公司所属行业分类形成的板块，反映各行业整体表现。</p>
    <p><strong>数据字段说明</strong>：</p>
    <ul>
      <li><strong>最新价</strong>：板块当前价格指数</li>
      <li><strong>涨跌额/涨跌幅</strong>：相比上一交易日的变化</li>
      <li><strong>总市值</strong>：板块内所有股票市值总和（单位：亿元）</li>
      <li><strong>换手率</strong>：板块内股票平均换手率</li>
      <li><strong>领涨股</strong>：板块内涨幅最大的股票</li>
      <li><strong>上涨/下跌</strong>：板块内上涨股票数和下跌股票数</li>
    </ul>
    <p class="text-muted small mt-2">数据更新时间：每个交易日收盘后</p>
  </div>
</div>

{% endblock %} {% block extra_css %}
<style>
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }
</style>
{% endblock %}
