{% extends "base.html" %} {% block title %}产业链详情 - {{ chain.chain_name }}{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">财务分析</div>
        <h2 class="page-title">产业链详情</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'financial_analysis:industry_chain_list' %}" class="btn btn-outline-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="icon"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M9 13l-4 -4l4 -4m-4 4h11a4 4 0 0 1 0 8h-1" />
            </svg>
            返回列表
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">{{ chain.chain_name }}</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">基本信息</h3>
                  </div>
                  <div class="card-body">
                    <div class="datagrid">
                      <div class="datagrid-item">
                        <div class="datagrid-title">日期</div>
                        <div class="datagrid-content">{{ chain.date|date:"Y-m-d" }}</div>
                      </div>
                      <div class="datagrid-item">
                        <div class="datagrid-title">涨跌幅</div>
                        <div class="datagrid-content">
                          {% if chain.change_percent > 0 %}
                          <span class="text-success">+{{ chain.change_percent|floatformat:2 }}%</span>
                          {% elif chain.change_percent < 0 %}
                          <span class="text-danger">{{ chain.change_percent|floatformat:2 }}%</span>
                          {% else %}
                          <span>{{ chain.change_percent|floatformat:2 }}%</span>
                          {% endif %}
                        </div>
                      </div>
                      <div class="datagrid-item">
                        <div class="datagrid-title">成交额</div>
                        <div class="datagrid-content">{{ chain.turnover|floatformat:2 }}亿</div>
                      </div>
                      <div class="datagrid-item">
                        <div class="datagrid-title">换手率</div>
                        <div class="datagrid-content">{{ chain.turnover_rate|floatformat:2 }}%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">领涨股信息</h3>
                  </div>
                  <div class="card-body">
                    <div class="datagrid">
                      <div class="datagrid-item">
                        <div class="datagrid-title">领涨股</div>
                        <div class="datagrid-content">{{ chain.leading_stock }}</div>
                      </div>
                      <div class="datagrid-item">
                        <div class="datagrid-title">领涨股涨跌幅</div>
                        <div class="datagrid-content">
                          {% if chain.leading_stock_change > 0 %}
                          <span class="text-success">+{{ chain.leading_stock_change|floatformat:2 }}%</span>
                          {% elif chain.leading_stock_change < 0 %}
                          <span class="text-danger">{{ chain.leading_stock_change|floatformat:2 }}%</span>
                          {% else %}
                          <span>{{ chain.leading_stock_change|floatformat:2 }}%</span>
                          {% endif %}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row mt-3">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">产业链股票列表</h3>
                  </div>
                  <div class="card-body p-0">
                    <div class="table-responsive">
                      <table class="table table-vcenter card-table">
                        <thead>
                          <tr>
                            <th>股票代码</th>
                            <th>股票名称</th>
                            <th>涨跌幅</th>
                            <th>成交额</th>
                            <th>换手率</th>
                            <th>市值</th>
                          </tr>
                        </thead>
                        <tbody>
                          {% for stock in stocks %}
                          <tr>
                            <td>{{ stock.stock_code }}</td>
                            <td>{{ stock.stock_name }}</td>
                            <td>
                              {% if stock.change_percent > 0 %}
                              <span class="text-success">+{{ stock.change_percent|floatformat:2 }}%</span>
                              {% elif stock.change_percent < 0 %}
                              <span class="text-danger">{{ stock.change_percent|floatformat:2 }}%</span>
                              {% else %}
                              <span>{{ stock.change_percent|floatformat:2 }}%</span>
                              {% endif %}
                            </td>
                            <td>{{ stock.turnover|floatformat:2 }}亿</td>
                            <td>{{ stock.turnover_rate|floatformat:2 }}%</td>
                            <td>{{ stock.market_cap|floatformat:2 }}亿</td>
                          </tr>
                          {% empty %}
                          <tr>
                            <td colspan="6" class="text-center">暂无相关股票数据</td>
                          </tr>
                          {% endfor %}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {% if stocks.has_other_pages %}
            <div class="card-footer d-flex align-items-center">
              <p class="m-0 text-muted">
                显示第 <span>{{ stocks.start_index }}</span> 到 <span>{{ stocks.end_index }}</span> 条，共 <span>{{ stocks.paginator.count }}</span> 条
              </p>
              <ul class="pagination m-0 ms-auto">
                {% if stocks.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ stocks.previous_page_number }}">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path d="M15 6l-6 6l6 6" />
                    </svg>
                  </a>
                </li>
                {% endif %} {% for num in stocks.paginator.page_range %} {% if stocks.number == num %}
                <li class="page-item active">
                  <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > stocks.number|add:'-3' and num < stocks.number|add:'3' %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                </li>
                {% endif %} {% endfor %} {% if stocks.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ stocks.next_page_number }}">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path d="M9 6l6 6l-6 6" />
                    </svg>
                  </a>
                </li>
                {% endif %}
              </ul>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
