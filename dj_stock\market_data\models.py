# -*- coding: utf-8 -*-
from django.db import models
from datetime import datetime
import re

# Create your models here.


class StockBasic(models.Model):
    """股票基本信息"""

    stock_code = models.CharField(
        max_length=10, primary_key=True, verbose_name="股票代码"
    )
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    industry = models.CharField(
        max_length=50, null=True, blank=True, verbose_name="所属行业"
    )
    market = models.CharField(
        max_length=20, null=True, blank=True, verbose_name="市场类型"
    )
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = "stock_basic"
        verbose_name = "股票基本信息"
        verbose_name_plural = verbose_name
        ordering = ["stock_code"]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code})"


class StockIndustryBoard(models.Model):
    """行业板块数据"""

    board_code = models.CharField("板块代码", max_length=20, db_index=True)
    board_name = models.CharField("板块名称", max_length=100)
    date = models.DateField("交易日期", db_index=True)
    rank = models.IntegerField("排名", null=True, blank=True)
    latest_price = models.FloatField("最新价", null=True, blank=True)
    change_amount = models.FloatField("涨跌额", null=True, blank=True)
    change_percent = models.FloatField("涨跌幅", null=True, blank=True)
    total_market_value = models.FloatField("总市值(亿)", null=True, blank=True)
    turnover_rate = models.FloatField("换手率", null=True, blank=True)
    up_count = models.IntegerField("上涨家数", null=True, blank=True)
    down_count = models.IntegerField("下跌家数", null=True, blank=True)
    leading_stock = models.CharField("领涨股票", max_length=50, null=True, blank=True)
    leading_stock_change_percent = models.FloatField(
        "领涨股票涨跌幅", null=True, blank=True
    )
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "行业板块"
        verbose_name_plural = verbose_name
        db_table = "stock_board_industry"
        unique_together = [["board_code", "date"]]
        managed = False

    def __str__(self):
        return f"{self.board_name}({self.board_code}) - {self.date}"


class StockIndustryBoardRelation(models.Model):
    """行业板块成分股关系表"""

    board_code = models.CharField("板块代码", max_length=20, db_index=True)
    board_name = models.CharField("板块名称", max_length=100)
    stock_code = models.CharField("股票代码", max_length=10, db_index=True)
    stock_name = models.CharField("股票名称", max_length=100)
    date = models.DateField("交易日期", null=True, blank=True)
    rank = models.IntegerField("序号", null=True, blank=True)
    latest_price = models.FloatField("最新价", null=True, blank=True)
    change_percent = models.FloatField("涨跌幅", null=True, blank=True)
    change_amount = models.FloatField("涨跌额", null=True, blank=True)
    volume = models.FloatField("成交量", null=True, blank=True)
    amount = models.FloatField("成交额", null=True, blank=True)
    amplitude = models.FloatField("振幅", null=True, blank=True)
    high = models.FloatField("最高", null=True, blank=True)
    low = models.FloatField("最低", null=True, blank=True)
    open = models.FloatField("今开", null=True, blank=True)
    pre_close = models.FloatField("昨收", null=True, blank=True)
    turnover_rate = models.FloatField("换手率", null=True, blank=True)
    pe_ratio = models.FloatField("市盈率-动态", null=True, blank=True)
    pb_ratio = models.FloatField("市净率", null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "行业板块成分股"
        verbose_name_plural = verbose_name
        db_table = "stock_board_industry_stock"
        unique_together = [["board_code", "stock_code", "date"]]
        managed = False

    def __str__(self):
        return f"{self.board_name} - {self.stock_name}"


class StockRiskWarning(models.Model):
    """风险警示股票表（ST股票）"""

    RISK_TYPE_CHOICES = [
        ("ST", "ST股票"),
        ("*ST", "*ST股票"),
    ]

    stock_code = models.CharField("股票代码", max_length=10, db_index=True)
    stock_name = models.CharField("股票名称", max_length=100)
    warning_reason = models.TextField("风险警示原因")
    warning_date = models.DateField("风险警示日期")
    status = models.CharField(
        "状态",
        max_length=20,
        default="纳入",
        choices=[("纳入", "纳入"), ("剔除", "剔除")],
    )
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "风险警示股票"
        verbose_name_plural = verbose_name
        unique_together = [["stock_code", "warning_date"]]
        db_table = "stock_risk_warning"
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.warning_reason}"


class StockIPO(models.Model):
    """新股上市数据"""

    stock_code = models.CharField("股票代码", max_length=10, unique=True)
    stock_name = models.CharField("股票名称", max_length=50)
    listing_date = models.DateField("上市日期", db_index=True)
    issue_price = models.FloatField("发行价")
    latest_price = models.FloatField("最新价", null=True, blank=True)
    first_day_open = models.FloatField("首日开盘价", null=True, blank=True)
    first_day_close = models.FloatField("首日收盘价", null=True, blank=True)
    first_day_high = models.FloatField("首日最高价", null=True, blank=True)
    first_day_low = models.FloatField("首日最低价", null=True, blank=True)
    first_day_change_pct = models.FloatField("首日涨跌幅", null=True, blank=True)
    break_issue = models.CharField("是否破发", max_length=10, null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "新股上市"
        verbose_name_plural = verbose_name
        db_table = "stock_ipo"
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code})"


class StockMarketIndex(models.Model):
    """主板指数数据模型"""

    index_code = models.CharField("指数代码", max_length=10)
    index_name = models.CharField("指数名称", max_length=50)
    trade_date = models.DateField("交易日期")
    open_price = models.FloatField("开盘价", null=True, blank=True)
    high_price = models.FloatField("最高价", null=True, blank=True)
    low_price = models.FloatField("最低价", null=True, blank=True)
    close_price = models.FloatField("收盘价", null=True, blank=True)
    pre_close = models.FloatField("昨收价", null=True, blank=True)
    volume = models.FloatField("成交量", null=True, blank=True)
    amount = models.FloatField("成交额", null=True, blank=True)
    change_amount = models.FloatField("涨跌额", null=True, blank=True)
    amplitude = models.FloatField("振幅", null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "主板指数"
        verbose_name_plural = verbose_name
        db_table = "stock_market_index"
        unique_together = [["index_code", "trade_date"]]
        managed = False

    def __str__(self):
        return f"{self.index_name}({self.index_code}) - {self.trade_date}"


class StockDailyQuote(models.Model):
    """股票每日行情"""

    stock_code = models.CharField("股票代码", max_length=10)
    stock_name = models.CharField("股票名称", max_length=50)
    trade_date = models.DateField("交易日期")

    # 价格相关
    open_price = models.FloatField("开盘价", null=True, blank=True)
    close_price = models.FloatField("收盘价", null=True, blank=True)
    high_price = models.FloatField("最高价", null=True, blank=True)
    low_price = models.FloatField("最低价", null=True, blank=True)
    prev_close = models.FloatField("昨日收盘价", null=True, blank=True)

    # 交易量和金额
    volume = models.FloatField("成交量", null=True, blank=True)
    amount = models.FloatField("成交额", null=True, blank=True)
    turnover_rate = models.FloatField("换手率", null=True, blank=True)
    volume_ratio = models.FloatField("量比", null=True, blank=True)

    # 涨跌相关
    change_amount = models.FloatField("涨跌额", null=True, blank=True)
    change_percent = models.FloatField("涨跌幅", null=True, blank=True)
    amplitude = models.FloatField("振幅", null=True, blank=True)

    # 估值指标
    pe_ratio = models.FloatField("市盈率(动态)", null=True, blank=True)
    pb_ratio = models.FloatField("市净率", null=True, blank=True)
    total_value = models.FloatField("总市值", null=True, blank=True)
    float_value = models.FloatField("流通市值", null=True, blank=True)

    # 时间戳
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "股票每日行情"
        verbose_name_plural = verbose_name
        db_table = "stock_daily_quote"
        unique_together = [["stock_code", "trade_date"]]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.trade_date}"


class StockBoardConcept(models.Model):
    """概念板块数据"""

    board_code = models.CharField("板块代码", max_length=20, db_index=True)
    board_name = models.CharField("板块名称", max_length=100)
    date = models.DateField("交易日期", db_index=True)
    rank = models.IntegerField("排名", null=True, blank=True)
    latest_price = models.FloatField("最新价", null=True, blank=True)
    change_amount = models.FloatField("涨跌额", null=True, blank=True)
    change_percent = models.FloatField("涨跌幅", null=True, blank=True)
    total_market_value = models.FloatField("总市值(亿)", null=True, blank=True)
    turnover_rate = models.FloatField("换手率", null=True, blank=True)
    up_count = models.IntegerField("上涨家数", null=True, blank=True)
    down_count = models.IntegerField("下跌家数", null=True, blank=True)
    leading_stock = models.CharField("领涨股票", max_length=50, null=True, blank=True)
    leading_stock_change_percent = models.FloatField(
        "领涨股票涨跌幅", null=True, blank=True
    )
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "概念板块"
        verbose_name_plural = verbose_name
        db_table = "stock_board_concept"
        unique_together = [["board_code", "date"]]
        managed = False

    def __str__(self):
        return f"{self.board_name}({self.board_code}) - {self.date}"


class StockBoardConceptRelation(models.Model):
    """概念板块成分股关系"""

    stock_code = models.CharField(max_length=10, verbose_name="股票代码")
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    board_code = models.CharField(max_length=10, verbose_name="板块代码")
    board_name = models.CharField(max_length=50, verbose_name="板块名称")
    date = models.DateField(verbose_name="日期")
    latest_price = models.FloatField("最新价", null=True, blank=True)
    change_percent = models.FloatField("涨跌幅", null=True, blank=True)
    change_amount = models.FloatField("涨跌额", null=True, blank=True)
    volume = models.FloatField("成交量", null=True, blank=True)
    amount = models.FloatField("成交额", null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = "stock_board_concept_stock"
        verbose_name = "概念板块成分股关系"
        verbose_name_plural = verbose_name
        ordering = ["board_code", "stock_code"]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.board_name}({self.board_code})"


class HKStockConnect(models.Model):
    """港股通成份股数据"""

    stock_code = models.CharField("股票代码", max_length=10, db_index=True)
    stock_name = models.CharField("股票名称", max_length=100)
    date = models.DateField("统计日期")
    direction = models.CharField("交易方向", max_length=20, help_text="沪股通/深股通")
    status = models.CharField("状态", max_length=20, help_text="纳入/剔除")
    create_time = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        db_table = "hk_stock_connect"
        verbose_name = "港股通成份股"
        verbose_name_plural = verbose_name
        unique_together = [["stock_code", "date"]]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.date}"


class StockDataStatus(models.Model):
    """股票数据采集状态元数据表"""

    STATUS_CHOICES = [
        ("never_fetched", "从未获取"),
        ("fetching", "获取中"),
        ("partial", "部分数据"),
        ("complete", "完整数据"),
        ("error", "获取错误"),
    ]

    stock_code = models.CharField("股票代码", max_length=10, primary_key=True)
    last_update = models.DateTimeField("最后更新时间", auto_now=True)
    is_complete = models.BooleanField("数据是否完整", default=False)
    date_from = models.DateField("数据起始日期", null=True, blank=True)
    date_to = models.DateField("数据结束日期", null=True, blank=True)
    record_count = models.IntegerField("记录数量", default=0)
    status = models.CharField(
        "数据状态", max_length=20, choices=STATUS_CHOICES, default="never_fetched"
    )
    error_message = models.TextField("错误信息", blank=True, null=True)

    class Meta:
        verbose_name = "股票数据状态"
        verbose_name_plural = verbose_name
        db_table = "stock_data_status"

    def __str__(self):
        return f"{self.stock_code} - {self.get_status_display()}"


class StockTopList(models.Model):
    """龙虎榜数据"""

    class Meta:
        db_table = "stock_top_list"
        unique_together = [["stock_code", "date"]]
        verbose_name = "龙虎榜"
        verbose_name_plural = verbose_name
        ordering = ["-date", "stock_code"]

    id = models.AutoField(primary_key=True, verbose_name="主键ID")
    stock_code = models.CharField(max_length=10, db_index=True, verbose_name="股票代码")
    stock_name = models.CharField(max_length=100, verbose_name="股票名称")
    date = models.DateField(db_index=True, verbose_name="上榜日期")
    reason = models.TextField(null=True, blank=True, verbose_name="上榜理由")
    detail_reason = models.TextField(null=True, blank=True, verbose_name="上榜明细")
    net_buy = models.FloatField(null=True, blank=True, verbose_name="龙虎榜净买额")
    close_price = models.FloatField(null=True, blank=True, verbose_name="收盘价")
    change_ratio = models.FloatField(null=True, blank=True, verbose_name="涨跌幅")
    buy_amount = models.FloatField(null=True, blank=True, verbose_name="龙虎榜买入额")
    sell_amount = models.FloatField(null=True, blank=True, verbose_name="龙虎榜卖出额")
    total_turnover = models.FloatField(
        null=True, blank=True, verbose_name="龙虎榜成交额"
    )
    market_total_turnover = models.FloatField(
        null=True, blank=True, verbose_name="市场总成交额"
    )
    net_buy_ratio = models.FloatField(
        null=True, blank=True, verbose_name="净买额占总成交比"
    )
    turnover_ratio = models.FloatField(
        null=True, blank=True, verbose_name="成交额占总成交比"
    )
    turnover_rate = models.FloatField(null=True, blank=True, verbose_name="换手率")
    circulation_market_value = models.FloatField(
        null=True, blank=True, verbose_name="流通市值"
    )
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.date}"


class StockMargin(models.Model):
    """融资融券明细数据"""

    class Meta:
        db_table = "stock_margin"
        unique_together = [["trade_date", "stock_code", "exchange"]]
        verbose_name = "融资融券明细"
        verbose_name_plural = verbose_name
        ordering = ["-trade_date", "stock_code"]

    id = models.AutoField(primary_key=True, verbose_name="主键ID")
    trade_date = models.DateField(verbose_name="信用交易日期")
    stock_code = models.CharField(max_length=10, verbose_name="标的证券代码")
    stock_name = models.CharField(max_length=50, verbose_name="标的证券简称")
    margin_balance = models.BigIntegerField(
        null=True, blank=True, verbose_name="融资余额"
    )
    margin_buy = models.BigIntegerField(
        null=True, blank=True, verbose_name="融资买入额"
    )
    margin_repay = models.BigIntegerField(
        null=True, blank=True, verbose_name="融资偿还额"
    )
    short_balance = models.BigIntegerField(
        null=True, blank=True, verbose_name="融券余量"
    )
    short_sell = models.BigIntegerField(
        null=True, blank=True, verbose_name="融券卖出量"
    )
    short_repay = models.BigIntegerField(
        null=True, blank=True, verbose_name="融券偿还量"
    )
    short_balance_amount = models.BigIntegerField(
        null=True, blank=True, verbose_name="融券余额"
    )
    total_balance = models.BigIntegerField(
        null=True, blank=True, verbose_name="融资融券余额"
    )
    exchange = models.CharField(max_length=10, verbose_name="交易所")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.trade_date}"


class StockComment(models.Model):
    """千股千评数据"""

    class Meta:
        db_table = "stock_comment"
        unique_together = [["stock_code", "date"]]
        verbose_name = "千股千评"
        verbose_name_plural = verbose_name
        ordering = ["-date", "stock_code"]
        indexes = [
            models.Index(fields=["stock_code", "date"]),
            models.Index(fields=["date"]),
            models.Index(fields=["rise_rank"]),
            models.Index(fields=["current_rank"]),
        ]

    id = models.AutoField(primary_key=True, verbose_name="主键ID")
    stock_code = models.CharField(max_length=10, db_index=True, verbose_name="股票代码")
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    date = models.DateField(db_index=True, verbose_name="交易日期")
    current_price = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="最新价"
    )
    change_ratio = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="涨跌幅"
    )
    turnover_rate = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="换手率"
    )
    pe_ratio = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="市盈率"
    )
    main_cost = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="主力成本"
    )
    institution_participation = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="机构参与度",
    )
    comprehensive_score = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="综合得分"
    )
    rise_rank = models.IntegerField(
        null=True, blank=True, db_index=True, verbose_name="上升排名"
    )
    current_rank = models.IntegerField(
        null=True, blank=True, db_index=True, verbose_name="目前排名"
    )
    attention_index = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="关注指数"
    )
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.date}"


class StockActiveBroker(models.Model):
    """每日活跃营业部数据"""

    class Meta:
        db_table = "stock_active_broker"
        unique_together = [["broker_name", "trade_date"]]
        verbose_name = "活跃营业部"
        verbose_name_plural = verbose_name
        ordering = ["-trade_date", "broker_name"]
        indexes = [
            models.Index(fields=["broker_name", "trade_date"]),
            models.Index(fields=["trade_date"]),
        ]
        managed = False

    id = models.AutoField(primary_key=True, verbose_name="主键ID")
    broker_name = models.CharField(
        max_length=100, db_index=True, verbose_name="营业部名称"
    )
    trade_date = models.DateField(db_index=True, verbose_name="上榜日期")
    buy_stock_count = models.IntegerField(
        null=True, blank=True, verbose_name="买入个股数"
    )
    sell_stock_count = models.IntegerField(
        null=True, blank=True, verbose_name="卖出个股数"
    )
    buy_amount = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="买入总金额",
    )
    sell_amount = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="卖出总金额",
    )
    net_amount = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="总买卖净额",
    )
    buy_stocks = models.TextField(null=True, blank=True, verbose_name="买入股票")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def __str__(self):
        return f"{self.broker_name} - {self.trade_date}"


class StockLimitList(models.Model):
    """涨跌停数据"""

    stock_code = models.CharField("股票代码", max_length=10)
    stock_name = models.CharField("股票名称", max_length=50)
    change_ratio = models.FloatField("涨跌幅", null=True, blank=True)
    latest_price = models.FloatField("最新价", null=True, blank=True)
    amount = models.BigIntegerField("成交额", null=True, blank=True)
    circulation_market_value = models.FloatField("流通市值", null=True, blank=True)
    total_market_value = models.FloatField("总市值", null=True, blank=True)
    turnover_ratio = models.FloatField("换手率", null=True, blank=True)
    fund_amount = models.BigIntegerField("封单资金", null=True, blank=True)
    first_time = models.TimeField("首次封板时间", null=True, blank=True)
    last_time = models.TimeField("最后封板时间")
    break_count = models.IntegerField("炸板次数/开板次数", default=0)
    continuous_limit = models.IntegerField("连板数/连续跌停", default=1)
    industry = models.CharField("所属行业", max_length=50, null=True, blank=True)
    limit_type = models.CharField("涨跌停类型", max_length=10)
    date = models.DateField("交易日期")
    create_time = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "涨跌停数据"
        verbose_name_plural = verbose_name
        db_table = "stock_limit_list"
        unique_together = ["stock_code", "date"]
        managed = False

    def __str__(self):
        return f"{self.stock_name}({self.stock_code}) - {self.date}"


class StockFundFlow(models.Model):
    """资金流向数据"""

    code = models.CharField("代码", max_length=10)
    name = models.CharField("名称", max_length=100)
    date = models.DateField("交易日期")
    type = models.CharField("数据类型", max_length=50, help_text="个股/北向资金")
    main_net_inflow = models.FloatField("主力净流入(元)", null=True, blank=True)
    retail_net_inflow = models.FloatField("散户净流入(元)", null=True, blank=True)
    total_net_inflow = models.FloatField("总净流入(元)", null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "资金流向数据"
        verbose_name_plural = verbose_name
        db_table = "stock_fund_flow"
        unique_together = ["date", "code", "type"]
        indexes = [
            models.Index(fields=["date"]),
            models.Index(fields=["code"]),
        ]
        managed = False

    def __str__(self):
        return f"{self.name}({self.code}) - {self.date}"


class StockMarketFundFlow(models.Model):
    """市场资金周报数据"""

    trade_date = models.DateField("交易日期")
    sh_index_close = models.FloatField("上证-收盘价", null=True, blank=True)
    sh_index_change_pct = models.FloatField("上证-涨跌幅", null=True, blank=True)
    sz_index_close = models.FloatField("深证-收盘价", null=True, blank=True)
    sz_index_change_pct = models.FloatField("深证-涨跌幅", null=True, blank=True)
    main_net_inflow = models.FloatField("主力净流入-净额", null=True, blank=True)
    main_net_inflow_pct = models.FloatField("主力净流入-净占比", null=True, blank=True)
    super_big_net_inflow = models.FloatField("超大单净流入-净额", null=True, blank=True)
    super_big_net_inflow_pct = models.FloatField(
        "超大单净流入-净占比", null=True, blank=True
    )
    big_net_inflow = models.FloatField("大单净流入-净额", null=True, blank=True)
    big_net_inflow_pct = models.FloatField("大单净流入-净占比", null=True, blank=True)
    medium_net_inflow = models.FloatField("中单净流入-净额", null=True, blank=True)
    medium_net_inflow_pct = models.FloatField(
        "中单净流入-净占比", null=True, blank=True
    )
    small_net_inflow = models.FloatField("小单净流入-净额", null=True, blank=True)
    small_net_inflow_pct = models.FloatField("小单净流入-净占比", null=True, blank=True)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "市场资金周报数据"
        verbose_name_plural = verbose_name
        db_table = "stock_market_fund_flow"
        indexes = [models.Index(fields=["trade_date"])]
        managed = False

    def __str__(self):
        return f"市场资金周报 - {self.trade_date}"


class StockSectorFundFlow(models.Model):
    """行业资金轴幅数据"""

    trade_date = models.DateField("交易日期")
    sector_name = models.CharField("板块名称", max_length=50)
    sector_type = models.CharField(
        "板块类型", max_length=20, help_text="行业板块或概念板块"
    )
    rank = models.IntegerField("排名", null=True, blank=True)
    net_inflow_rate = models.FloatField("今日涨跌幅(%)", null=True, blank=True)
    net_inflow_amount = models.FloatField("主力净流入-净额", null=True, blank=True)
    main_net_inflow_pct = models.FloatField("主力净流入-净占比", null=True, blank=True)
    super_big_net_inflow = models.FloatField("超大单净流入-净额", null=True, blank=True)
    super_big_net_inflow_pct = models.FloatField(
        "超大单净流入-净占比", null=True, blank=True
    )
    big_net_inflow = models.FloatField("大单净流入-净额", null=True, blank=True)
    big_net_inflow_pct = models.FloatField("大单净流入-净占比", null=True, blank=True)
    medium_net_inflow = models.FloatField("中单净流入-净额", null=True, blank=True)
    medium_net_inflow_pct = models.FloatField(
        "中单净流入-净占比", null=True, blank=True
    )
    small_net_inflow = models.FloatField("小单净流入-净额", null=True, blank=True)
    small_net_inflow_pct = models.FloatField("小单净流入-净占比", null=True, blank=True)
    max_net_inflow_stock = models.CharField(
        "主力净流入最大股", max_length=50, null=True, blank=True
    )
    create_time = models.DateTimeField("创建时间", auto_now_add=True)
    update_time = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "行业资金轴幅数据"
        verbose_name_plural = verbose_name
        db_table = "stock_sector_fund_flow"
        unique_together = ["trade_date", "sector_name"]
        indexes = [
            models.Index(fields=["trade_date"]),
            models.Index(fields=["sector_name"]),
        ]
        managed = False

    def __str__(self):
        return f"{self.sector_name} - {self.trade_date}"
