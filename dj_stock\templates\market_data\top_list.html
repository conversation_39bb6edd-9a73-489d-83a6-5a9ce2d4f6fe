{% extends 'base.html' %} {% block title %}龙虎榜 - 股票市场数据{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">龙虎榜</h2>
        <div class="text-muted mt-1">A股市场龙虎榜交易数据</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list"></div>
      </div>
    </div>
  </div>
</div>
<div class="page-body">
  <div class="container-xl">
    <div class="card mb-3">
      <div class="card-header">
        <h3 class="card-title">筛选条件</h3>
      </div>
      <div class="card-body">
        <form method="get" id="filter-form">
          <div class="row g-3">
            <div class="col-md-3">
              <div class="form-label">股票代码</div>
              <div class="input-icon">
                <span class="input-icon-addon">
                  <i class="ti ti-code"></i>
                </span>
                <input type="text" class="form-control" name="stock_code" value="{{ stock_code }}" placeholder="输入股票代码" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-label">股票名称</div>
              <div class="input-icon">
                <span class="input-icon-addon">
                  <i class="ti ti-id"></i>
                </span>
                <input type="text" class="form-control" name="stock_name" value="{{ stock_name }}" placeholder="输入股票名称" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-label">上榜日期</div>
              <div class="input-icon">
                <span class="input-icon-addon">
                  <i class="ti ti-calendar"></i>
                </span>
                <input type="date" class="form-control" name="date" value="{{ date }}" />
              </div>
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <div class="btn-list w-100">
                <button type="submit" class="btn btn-primary w-100">
                  <i class="ti ti-search me-1"></i>
                  查询
                </button>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="btn-list">
              <button type="button" class="btn btn-outline-secondary btn-sm" id="btn-clear-filter">
                <i class="ti ti-trash me-1"></i>
                清空筛选
              </button>
              <a href="{% url 'market_data:top_list' %}" class="btn btn-outline-secondary btn-sm">
                <i class="ti ti-refresh me-1"></i>
                重置
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">龙虎榜交易数据</h3>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-vcenter table-hover card-table">
            <thead>
              <tr>
                <th>股票代码</th>
                <th>股票名称</th>
                <th>上榜日期</th>
                <th>收盘价</th>
                <th>涨跌幅</th>
                <th>上榜理由</th>
                <th>龙虎榜净买额(万)</th>
                <th>龙虎榜成交额(万)</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {% for item in page_obj %}
              <tr>
                <td>{{ item.stock_code }}</td>
                <td>{{ item.stock_name }}</td>
                <td>{{ item.date|date:"Y-m-d" }}</td>
                <td>{{ item.close_price|floatformat:2 }}</td>
                <td class="{% if item.change_ratio > 0 %}text-danger fw-bold{% elif item.change_ratio < 0 %}text-success fw-bold{% endif %}">
                  {{ item.change_ratio|floatformat:2 }}%
                </td>
                <td>{{ item.reason|truncatechars:20 }}</td>
                <td class="{% if item.net_buy > 0 %}text-danger fw-bold{% elif item.net_buy < 0 %}text-success fw-bold{% endif %}">
                  {{ item.net_buy|floatformat:2 }}
                </td>
                <td>{{ item.total_turnover|floatformat:2 }}</td>
                <td>
                  <div class="btn-list flex-nowrap">
                    <a href="{% url 'market_data:top_list_detail' item.id %}" class="btn btn-sm btn-outline-primary"> <i class="ti ti-eye"></i> 明细 </a>
                    <a href="{% url 'market_data:stock_detail' item.stock_code %}" class="btn btn-sm btn-outline-secondary">
                      <i class="ti ti-chart-line"></i> 股票
                    </a>
                  </div>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="9" class="text-center py-3">暂无数据</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% include "includes/pagination.html" with page_obj=page_obj %}
      </div>
    </div>
  </div>
</div>

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 清空筛选按钮事件
    document.getElementById('btn-clear-filter').addEventListener('click', function () {
      const inputs = document.querySelectorAll('#filter-form input')
      inputs.forEach((input) => {
        input.value = ''
      })
      document.getElementById('filter-form').submit()
    })
  })
</script>
{% endblock %} {% endblock %}
