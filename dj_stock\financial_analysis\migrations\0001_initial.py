# Generated by Django 5.1.7 on 2025-03-30 15:05

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='StockComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=100, verbose_name='股票名称')),
                ('date', models.DateField(verbose_name='评级日期')),
                ('rating', models.CharField(blank=True, max_length=50, null=True, verbose_name='最新评级')),
                ('target_price', models.FloatField(blank=True, null=True, verbose_name='目标价格(元)')),
                ('current_price', models.FloatField(blank=True, null=True, verbose_name='当前价格(元)')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '千股千评',
                'verbose_name_plural': '千股千评',
                'db_table': 'stock_comment',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockDividend',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('announce_date', models.DateField(verbose_name='公告日期')),
                ('registration_date', models.DateField(verbose_name='登记日期')),
                ('dividend_plan', models.TextField(verbose_name='分红方案')),
                ('cash_dividend', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='现金分红')),
                ('share_dividend', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='送股')),
                ('share_transfer', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='转增')),
                ('implementation_status', models.CharField(max_length=20, verbose_name='实施状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '股票分红',
                'verbose_name_plural': '股票分红',
                'db_table': 'stock_dividend',
                'ordering': ['-registration_date'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockFinancial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('report_date', models.DateField(verbose_name='报告日期')),
                ('report_type', models.CharField(max_length=20, verbose_name='报告类型')),
                ('revenue', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='营业收入')),
                ('net_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='净利润')),
                ('total_assets', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='总资产')),
                ('total_liabilities', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='总负债')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '股票财务',
                'verbose_name_plural': '股票财务',
                'db_table': 'stock_financial',
                'ordering': ['-report_date'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockIndustryChain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('industry_chain', models.CharField(max_length=100, verbose_name='产业链')),
                ('position', models.CharField(max_length=50, verbose_name='产业链位置')),
                ('market_share', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='市场份额')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '股票产业链',
                'verbose_name_plural': '股票产业链',
                'db_table': 'stock_industry_chain',
                'ordering': ['stock_code'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockShareholderStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('statistics_date', models.DateField(verbose_name='统计日期')),
                ('total_shareholders', models.IntegerField(blank=True, null=True, verbose_name='总股东数')),
                ('institutional_shareholders', models.IntegerField(blank=True, null=True, verbose_name='机构股东数')),
                ('retail_shareholders', models.IntegerField(blank=True, null=True, verbose_name='散户股东数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '股票股东统计',
                'verbose_name_plural': '股票股东统计',
                'db_table': 'stock_shareholder_statistics',
                'ordering': ['-statistics_date'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockFinancialIndicator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('report_date', models.DateField(verbose_name='报告期')),
                ('net_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='净利润')),
                ('net_profit_growth', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净利润同比增长率')),
                ('deducted_net_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='扣非净利润')),
                ('deducted_net_profit_growth', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='扣非净利润同比增长率')),
                ('total_revenue', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='营业总收入')),
                ('total_revenue_growth', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='营业总收入同比增长率')),
                ('eps', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='基本每股收益')),
                ('nav', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股净资产')),
                ('capital_reserve', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股资本公积金')),
                ('undistributed_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股未分配利润')),
                ('ocf_per_share', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股经营现金流')),
                ('net_profit_margin', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='销售净利率')),
                ('gross_profit_margin', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='销售毛利率')),
                ('roe', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净资产收益率')),
                ('diluted_roe', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净资产收益率-摊薄')),
                ('operating_cycle', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='营业周期')),
                ('inventory_turnover', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='存货周转率')),
                ('inventory_days', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='存货周转天数')),
                ('receivable_days', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='应收账款周转天数')),
                ('current_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='流动比率')),
                ('quick_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='速动比率')),
                ('conservative_quick_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='保守速动比率')),
                ('equity_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='产权比率')),
                ('debt_asset_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='资产负债率')),
                ('last_update', models.DateTimeField(auto_now=True, verbose_name='最后更新时间')),
                ('data_source', models.CharField(default='同花顺', max_length=50, verbose_name='数据来源')),
                ('collection_date', models.DateTimeField(default=datetime.datetime.now, verbose_name='数据采集时间')),
            ],
            options={
                'verbose_name': '股票财务指标',
                'verbose_name_plural': '股票财务指标',
                'db_table': 'stock_financial_indicator',
                'indexes': [models.Index(fields=['stock_code', 'report_date'], name='stock_finan_stock_c_42c7f8_idx')],
                'unique_together': {('stock_code', 'report_date')},
            },
        ),
    ]
