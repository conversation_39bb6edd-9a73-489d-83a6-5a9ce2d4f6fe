{% extends "base.html" %}
{% load static %}

{% block title %}风险预警 - 股票分析系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">风险预警 ({{ total_risks }})</h1>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">状态</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">全部</option>
                        {% for status_value in all_statuses %}
                        <option value="{{ status_value }}" {% if status == status_value %}selected{% endif %}>{{ status_value }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="risk_type" class="form-label">风险类型</label>
                    <select name="risk_type" id="risk_type" class="form-select">
                        <option value="">全部</option>
                        {% for risk_type_value in all_risk_types %}
                        <option value="{{ risk_type_value }}" {% if risk_type == risk_type_value %}selected{% endif %}>{{ risk_type_value }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="q" class="form-label">搜索</label>
                    <input type="text" class="form-control" id="q" name="q" value="{{ query }}" placeholder="股票代码或名称">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">搜索</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>股票代码</th>
                            <th>股票名称</th>
                            <th>风险类型</th>
                            <th>预警原因</th>
                            <th>预警日期</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for warning in page_obj %}
                        <tr>
                            <td>
                                <a href="{% url 'market_data:stock_detail' warning.stock_code %}">{{ warning.stock_code }}</a>
                            </td>
                            <td>{{ warning.stock_name }}</td>
                            <td>
                                {% if warning.stock_name|slice:":3" == "*ST" %}
                                *ST
                                {% elif warning.stock_name|slice:":2" == "ST" %}
                                ST
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>{{ warning.warning_reason }}</td>
                            <td>{{ warning.warning_date|date:"Y-m-d" }}</td>
                            <td>
                                <span class="badge {% if warning.status == '纳入' %}bg-danger{% else %}bg-success{% endif %}">
                                    {{ warning.status }}
                                </span>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">未找到风险预警股票</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if risk_type %}&risk_type={{ risk_type }}{% endif %}">上一页</a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}{% if query %}&q={{ query }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if risk_type %}&risk_type={{ risk_type }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if risk_type %}&risk_type={{ risk_type }}{% endif %}">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
