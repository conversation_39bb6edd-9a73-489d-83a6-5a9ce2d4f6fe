# 开发工具说明

本目录包含了一些用于开发和维护股票数据系统的工具脚本。

## 工具列表

### 1. check_indicators.py
**财务指标检查工具**

用途：
- 获取akshare API返回的所有财务指标
- 显示当前StockFinancialIndicator模型中的字段
- 帮助开发者了解可用的财务指标和模型字段对应关系

使用方法：
```bash
python check_indicators.py
```

### 2. check_indicators_match.py
**财务指标映射匹配检查工具**

用途：
- 检查akshare API返回的指标与financial_fetcher.py中映射字典的匹配情况
- 找出缺失的指标映射
- 找出多余的指标映射
- 帮助维护指标映射的完整性

使用方法：
```bash
python check_indicators_match.py
```

### 3. check_data_encoding.py
**股票数据编码检查工具**

用途：
- 诊断和调试股票数据中的编码问题
- 检查数据库中的字段编码格式
- 查找可能导致JSON序列化失败的数据
- 分析K线图数据处理过程中的编码问题

使用场景：
- 当股票详情页面出现编码错误时
- 当JSON序列化失败时
- 当图表数据显示异常时

使用方法：
```bash
python check_data_encoding.py
```

### 4. refresh_stock_data.py
**股票数据刷新工具**

用途：
- 批量刷新股票数据
- 通过访问股票详情页面来触发数据更新
- 显示进度条和刷新状态

功能：
- 获取指定范围的股票代码
- 循环访问每个股票的详情页面
- 触发股票历史数据和财务指标的更新

使用方法：
1. 修改脚本底部的 start 和 end 参数
2. 确保Django服务器在 http://127.0.0.1:8000 运行
3. 运行脚本：
```bash
python refresh_stock_data.py
```

注意事项：
- 请求间有随机延迟，避免服务器压力过大
- 建议分批次运行，每次不超过500只股票

## 维护说明

这些工具脚本主要用于开发和调试阶段，在生产环境中请谨慎使用。如果发现工具有问题或需要新的功能，可以根据需要进行修改和扩展。

## 已清理的文件

以下文件已被清理，因为相关功能已经集成到Django模型和迁移中：
- `update_financial_indicator_table.sql` - 字段已在模型中定义
- `add_field_comments.sql` - PostgreSQL注释，已在模型中设置
- `add_field_comments_mysql.sql` - MySQL注释，已在模型中设置
- `add_comments.py` - 执行MySQL注释的脚本，不再需要
- `temp_script.py` - 临时测试脚本
- `check_data.py` - 空文件
