{% extends "base.html" %} {% block title %}技术指标详情 - {{ indicator.stock_name }}{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">财务分析</div>
        <h2 class="page-title">技术指标详情</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'financial_analysis:technical_indicator_list' %}" class="btn btn-outline-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="icon"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M9 13l-4 -4l4 -4m-4 4h11a4 4 0 0 1 0 8h-1" />
            </svg>
            返回列表
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">{{ indicator.stock_name }}({{ indicator.stock_code }}) - {{ indicator.indicator_type }}</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">当前指标数据</h3>
                  </div>
                  <div class="card-body">
                    <div class="datagrid">
                      <div class="datagrid-item">
                        <div class="datagrid-title">日期</div>
                        <div class="datagrid-content">{{ indicator.date|date:"Y-m-d" }}</div>
                      </div>
                      <div class="datagrid-item">
                        <div class="datagrid-title">指标类型</div>
                        <div class="datagrid-content">{{ indicator.indicator_type }}</div>
                      </div>
                      <div class="datagrid-item">
                        <div class="datagrid-title">指标值</div>
                        <div class="datagrid-content">{{ indicator.indicator_value|floatformat:4 }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">历史趋势</h3>
                  </div>
                  <div class="card-body">
                    <div id="indicator-chart" style="height: 300px"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">历史数据</h3>
                  </div>
                  <div class="card-body p-0">
                    <div class="table-responsive">
                      <table class="table table-vcenter card-table">
                        <thead>
                          <tr>
                            <th>日期</th>
                            <th>指标值</th>
                          </tr>
                        </thead>
                        <tbody>
                          {% for item in history_data %}
                          <tr>
                            <td>{{ item.date|date:"Y-m-d" }}</td>
                            <td>{{ item.indicator_value|floatformat:4 }}</td>
                          </tr>
                          {% empty %}
                          <tr>
                            <td colspan="2" class="text-center">暂无历史数据</td>
                          </tr>
                          {% endfor %}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% block extra_js %}
<script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
<script>
  // 准备图表数据
  var dates = [{% for item in history_data %}'{{ item.date|date:"Y-m-d" }}'{% if not forloop.last %}, {% endif %}{% endfor %}];
  var values = [{% for item in history_data %}{{ item.indicator_value }}{% if not forloop.last %}, {% endif %}{% endfor %}];

  // 初始化图表
  var chart = echarts.init(document.getElementById('indicator-chart'));

  // 配置图表选项
  var option = {
    title: {
      text: '{{ indicator.indicator_type }}趋势图'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: values,
      type: 'line',
      smooth: true
    }]
  };

  // 使用配置项显示图表
  chart.setOption(option);

  // 响应式调整
  window.addEventListener('resize', function() {
    chart.resize();
  });
</script>
{% endblock %} {% endblock %}
