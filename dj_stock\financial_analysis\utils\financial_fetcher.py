import akshare as ak
from datetime import datetime, date, timedelta
import pandas as pd
from decimal import Decimal
import logging
import time
from django.utils import timezone
from ..models import StockFinancialIndicator

logger = logging.getLogger(__name__)


class FinancialDataFetcher:
    """财务数据采集器"""

    def __init__(self):
        self.update_interval = timedelta(days=90)  # 更新间隔改为90天
        self.start_year = 2015  # 设置起始年份为2015年

    def _format_stock_code(self, stock_code):
        """格式化股票代码"""
        # 移除可能的前缀
        stock_code = stock_code.replace("sh", "").replace("sz", "").strip()

        # 确保是6位数字
        if len(stock_code) != 6:
            logger.warning(f"股票代码长度不正确: {stock_code}")
            return None

        # 检查是否全为数字
        if not stock_code.isdigit():
            logger.warning(f"股票代码包含非数字字符: {stock_code}")
            return None

        logger.info(f"格式化后的股票代码: {stock_code}")
        return stock_code

    def _convert_to_decimal(self, value, is_percentage=False):
        """转换数值为Decimal类型"""
        try:
            if pd.isna(value) or value == "":
                return Decimal("0")

            # 处理百分比
            if isinstance(value, str):
                value = value.replace("%", "")

            decimal_value = Decimal(str(value))

            # 如果是百分比值，但不是以百分比形式存储的，需要转换
            if is_percentage and abs(decimal_value) > 100:
                decimal_value = decimal_value / 100

            return decimal_value
        except Exception as e:
            logger.error(
                f"转换数值时发生错误: {str(e)}, 值: {value}, 类型: {type(value)}"
            )
            return Decimal("0")

    def _parse_report_date(self, date_str):
        """解析报告期日期"""
        try:
            if isinstance(date_str, (date, datetime)):
                return date_str.strftime("%Y-%m-%d")
            elif isinstance(date_str, str):
                # 尝试不同的日期格式
                formats = ["%Y-%m-%d", "%Y/%m/%d", "%Y%m%d", "%Y年%m月%d日", "%Y.%m.%d"]
                for fmt in formats:
                    try:
                        return datetime.strptime(date_str, fmt).strftime("%Y-%m-%d")
                    except ValueError:
                        continue
                logger.warning(f"未知的日期格式: {date_str}")
                return None
            else:
                logger.warning(f"未知的日期类型: {type(date_str)} - {date_str}")
                return None
        except Exception as e:
            logger.error(f"解析日期时发生错误: {str(e)}")
            return None

    def _needs_update(self, stock_code):
        """检查是否需要更新数据"""
        # 获取最新的财务数据记录
        latest_record = (
            StockFinancialIndicator.objects.filter(stock_code=stock_code)
            .order_by("-report_date")
            .first()
        )

        if not latest_record:
            logger.info(f"股票 {stock_code} 没有历史数据，需要从2015年开始采集")
            return True, self.start_year

        # 获取当前最新的报告期
        latest_report_date = StockFinancialIndicator.get_latest_report_date()

        # 如果最新数据的报告期不是当前最新的报告期，需要更新
        if latest_record.report_date < latest_report_date:
            # 如果最后更新时间在90天以内，不更新
            if (timezone.now() - latest_record.last_update) <= self.update_interval:
                logger.info(
                    f"股票 {stock_code} 的数据已于{latest_record.last_update}更新过，暂不需要更新"
                )
                return False, None

            logger.info(f"股票 {stock_code} 需要更新最新财务数据")
            current_year = datetime.now().year
            return True, current_year

        logger.info(f"股票 {stock_code} 的数据已是最新")
        return False, None

    def stock_financial_abstract(self, stock_code):
        """获取并保存财务摘要数据

        使用新浪财经-财务报表-关键指标接口获取数据
        https://vip.stock.finance.sina.com.cn/corp/go.php/vFD_FinanceSummary/stockid/600004.phtml

        Args:
            stock_code: 股票代码

        Returns:
            tuple: (成功标志, 消息)
        """
        try:
            # 格式化股票代码
            formatted_code = self._format_stock_code(stock_code)
            if not formatted_code:
                return False, f"无效的股票代码: {stock_code}"

            # 检查是否需要更新
            should_update, _ = self._needs_update(stock_code)
            if not should_update:
                return True, "数据已是最新"

            # 获取财务指标数据
            logger.info(f"开始获取股票 {formatted_code} 的财务摘要数据")

            # 添加重试机制
            max_retries = 3
            retry_count = 0
            df = None

            while retry_count < max_retries:
                try:
                    # 使用新浪财经财务摘要接口
                    logger.info(f"尝试获取股票 {formatted_code} 的财务摘要数据")
                    try:
                        # 打印接口参数
                        logger.info(f"调用参数: symbol={formatted_code}")

                        # 尝试直接调用接口并打印返回值
                        result = ak.stock_financial_abstract(symbol=formatted_code)
                        logger.info(f"接口返回值类型: {type(result)}")

                        df = result
                        if df is not None and not df.empty:
                            logger.info(f"数据框不为空，列名: {df.columns.tolist()}")
                            logger.info(f"数据示例:\n{df.head()}")
                            break
                        else:
                            logger.warning("数据框为空")
                    except Exception as e:
                        logger.error(f"调用接口时发生错误: {str(e)}")
                        logger.error(f"错误类型: {type(e)}")
                        import traceback

                        logger.error(f"错误堆栈: {traceback.format_exc()}")

                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(
                            f"获取数据为空，正在重试({retry_count}/{max_retries})"
                        )
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        return False, "获取数据失败，请稍后重试"
                except Exception as e:
                    logger.error(f"获取数据时发生错误: {str(e)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        time.sleep(2)
                    else:
                        return False, f"获取数据失败: {str(e)}"

            if df is None or df.empty:
                return False, "未获取到数据"

            # 保存数据
            saved_count = 0

            # 获取所有日期列（列名为日期格式，如'20241231'）
            date_columns = [col for col in df.columns if col not in ["选项", "指标"]]
            logger.info(f"找到 {len(date_columns)} 个日期列: {date_columns[:5]}...")

            # 创建指标映射字典
            indicator_mapping = {
                # 基本财务数据
                "归母净利润": "net_profit",
                "归属母公司净利润增长率": "net_profit_growth",
                "扣非净利润": "deducted_net_profit",
                "营业总收入": "total_revenue",
                "营业总收入增长率": "total_revenue_growth",
                # 资产负债表数据
                "股东权益合计(净资产)": "net_assets",
                "商誉": "goodwill",
                # 现金流量表数据
                "经营现金流量净额": "operating_cash_flow",
                # 每股指标
                "基本每股收益": "eps",
                "稀释每股收益": "diluted_eps",
                "每股净资产": "nav",
                "每股资本公积金": "capital_reserve",
                "每股未分配利润": "undistributed_profit",
                "每股盈余公积金": "surplus_reserve",
                "每股留存收益": "retained_earnings",
                "每股经营现金流": "ocf_per_share",
                "每股现金流": "cash_flow_per_share",
                "每股企业自由现金流量": "free_cash_flow_per_share",
                "每股股东自由现金流量": "shareholder_free_cash_flow_per_share",
                "每股营业收入": "revenue_per_share",
                "每股息税前利润": "ebit_per_share",
                # 盈利能力指标
                "销售净利率": "net_profit_margin",
                "毛利率": "gross_profit_margin",
                "营业利润率": "operating_profit_margin",
                "息税前利润率": "ebit_margin",
                "期间费用率": "expense_ratio",
                "成本费用率": "cost_expense_ratio",
                # 收益率指标
                "净资产收益率(ROE)": "roe",
                "净资产收益率": "roe",
                "净资产收益率_平均": "roe",
                "摄薄净资产收益率": "diluted_roe",
                "总资产报酬率(ROA)": "roa",
                "总资产报酬率": "roa",
                "总资产净利率_平均": "roa",
                "总资本回报率": "total_capital_return",
                "投入资本回报率": "invested_capital_return",
                # 现金流指标
                "经营活动净现金/销售收入": "cash_to_sales_ratio",
                "经营性现金净流量/营业总收入": "cash_to_revenue_ratio",
                "经营活动净现金/归属母公司的净利润": "cash_to_profit_ratio",
                "所得税/利润总额": "tax_to_profit_ratio",
                # 营运能力指标
                "存货周转率": "inventory_turnover",
                "存货周转天数": "inventory_days",
                "应收账款周转天数": "receivable_days",
                "应付账款周转率": "accounts_payable_turnover",
                "总资产周转率": "total_assets_turnover",
                "总资产周转天数": "total_assets_turnover_days",
                "流动资产周转率": "current_assets_turnover",
                "流动资产周转天数": "current_assets_turnover_days",
                # 偿债能力指标
                "流动比率": "current_ratio",
                "速动比率": "quick_ratio",
                "保守速动比率": "conservative_quick_ratio",
                "现金比率": "cash_ratio",
                "产权比率": "equity_ratio",
                "权益乘数": "equity_multiplier",
                "资产负债率": "debt_asset_ratio",
            }

            # 获取指标行名称
            indicator_names = df["指标"].tolist()
            logger.info(f"找到指标: {indicator_names[:10]}...")

            # 处理每个日期列
            for date_col in date_columns:
                try:
                    # 解析报告期
                    # 日期列格式为'YYYYMMDD'，需要转换为'YYYY-MM-DD'
                    if len(date_col) != 8 or not date_col.isdigit():
                        logger.warning(f"跳过无效的日期列: {date_col}")
                        continue

                    year = date_col[:4]
                    month = date_col[4:6]
                    day = date_col[6:8]
                    report_date_str = f"{year}-{month}-{day}"

                    # 将日期字符串转换为datetime对象
                    report_date = datetime.strptime(report_date_str, "%Y-%m-%d")

                    # 检查是否已存在相同报告期的数据
                    existing_record = StockFinancialIndicator.objects.filter(
                        stock_code=stock_code, report_date=report_date
                    ).first()

                    if existing_record:
                        # 如果数据已存在，更新它
                        logger.info(
                            f"更新股票 {stock_code} 的报告期 {report_date_str} 数据"
                        )
                        indicator = existing_record
                    else:
                        # 创建新记录
                        indicator = StockFinancialIndicator(
                            stock_code=stock_code, report_date=report_date
                        )

                    # 打印所有指标名称，便于调试
                    if stock_code == "600000" and date_col == "20241231":
                        logger.info(f"\n所有指标名称:")
                        for idx, row in df.iterrows():
                            indicator_name = row["指标"]
                            logger.info(f"  - {indicator_name}")

                    # 遍历所有指标行，获取当前日期列的值
                    for idx, row in df.iterrows():
                        indicator_name = row["指标"]
                        if indicator_name in indicator_mapping:
                            field_name = indicator_mapping[indicator_name]
                            value = row[date_col]

                            # 判断是否为百分比指标
                            is_percentage = any(
                                keyword in indicator_name for keyword in ["率", "增长"]
                            )

                            # 转换并设置值
                            # 如果值为nan，则设置为0
                            if pd.isna(value):
                                decimal_value = Decimal("0")
                            else:
                                decimal_value = self._convert_to_decimal(
                                    value, is_percentage=is_percentage
                                )
                            setattr(indicator, field_name, decimal_value)

                            # 打印调试信息
                            if (
                                stock_code == "600000"
                                and date_col == "20241231"
                                and field_name in ["roa", "expense_ratio"]
                            ):
                                logger.info(
                                    f"  指标: {indicator_name} -> {field_name} = {value} -> {decimal_value}"
                                )
                        elif (
                            stock_code == "600000"
                            and date_col == "20241231"
                            and (
                                "总资产报酬率" in indicator_name
                                or "期间费用率" in indicator_name
                            )
                        ):
                            logger.info(f"  未匹配指标: {indicator_name}")

                    # 更新元数据
                    indicator.last_update = timezone.now()
                    indicator.data_source = "新浪财经-财务摘要"
                    indicator.collection_date = timezone.now()

                    indicator.save()
                    saved_count += 1
                    logger.info(
                        f"成功保存股票 {stock_code} 的报告期 {report_date_str} 数据"
                    )
                except Exception as e:
                    logger.error(f"保存数据时发生错误: {str(e)}")
                    continue

            return True, f"成功保存 {saved_count} 条数据"
        except Exception as e:
            error_msg = str(e)
            logger.error(f"处理股票 {stock_code} 的财务摘要数据时发生错误: {error_msg}")
            return False, f"处理数据时发生错误：{error_msg}"

    def fetch_and_save(self, stock_code):
        """获取并保存财务指标数据（旧方法，保留兼容性）"""
        return self.stock_financial_abstract(stock_code)
