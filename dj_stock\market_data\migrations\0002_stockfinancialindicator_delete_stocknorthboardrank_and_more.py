# Generated by Django 5.1.7 on 2025-03-30 14:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('market_data', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockFinancialIndicator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('report_date', models.DateField(verbose_name='报告期')),
                ('net_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='净利润')),
                ('net_profit_growth', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净利润同比增长率')),
                ('deducted_net_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='扣非净利润')),
                ('deducted_net_profit_growth', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='扣非净利润同比增长率')),
                ('total_revenue', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='营业总收入')),
                ('total_revenue_growth', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='营业总收入同比增长率')),
                ('eps', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='基本每股收益')),
                ('nav', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股净资产')),
                ('capital_reserve', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股资本公积金')),
                ('undistributed_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股未分配利润')),
                ('ocf_per_share', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='每股经营现金流')),
                ('net_profit_margin', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='销售净利率')),
                ('gross_profit_margin', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='销售毛利率')),
                ('roe', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净资产收益率')),
                ('diluted_roe', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净资产收益率-摊薄')),
                ('operating_cycle', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='营业周期')),
                ('inventory_turnover', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='存货周转率')),
                ('inventory_days', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='存货周转天数')),
                ('receivable_days', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='应收账款周转天数')),
                ('current_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='流动比率')),
                ('quick_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='速动比率')),
                ('conservative_quick_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='保守速动比率')),
                ('equity_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='产权比率')),
                ('debt_asset_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='资产负债率')),
                ('last_update', models.DateTimeField(auto_now=True, verbose_name='最后更新时间')),
            ],
            options={
                'verbose_name': '股票财务指标',
                'verbose_name_plural': '股票财务指标',
            },
        ),
        migrations.DeleteModel(
            name='StockNorthBoardRank',
        ),
        migrations.DeleteModel(
            name='StockNorthHolding',
        ),
        migrations.AddIndex(
            model_name='stockfinancialindicator',
            index=models.Index(fields=['stock_code', 'report_date'], name='market_data_stock_c_70375d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockfinancialindicator',
            unique_together={('stock_code', 'report_date')},
        ),
    ]
