"""
URL configuration for stock_analysis project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from django.shortcuts import redirect
from django.http import FileResponse
import os


def home_redirect(request):
    return redirect("market_data:index")


def test_api_view(request):
    file_path = os.path.join(settings.BASE_DIR, "test_api.html")
    return FileResponse(open(file_path, "rb"), content_type="text/html")


urlpatterns = [
    path("admin/", admin.site.urls),
    path("", home_redirect, name="home"),  # 根路径重定向到市场数据首页
    path("test_api.html", test_api_view, name="test_api"),  # 测试页面
    path("market_data/", include("market_data.urls")),  # 市场数据应用
    path("financial-analysis/", include("financial_analysis.urls")),  # 财务分析应用
    path(
        "login/", auth_views.LoginView.as_view(template_name="login.html"), name="login"
    ),
    path("logout/", auth_views.LogoutView.as_view(), name="logout"),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
