{"version": 3, "sources": ["tabler-vendors.css"], "names": [], "mappings": "AAAA;;;;;EAKE;AACF;;;;;;EAME;AACF;EACE,SAAS;EACT,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,0BAA0B;AAC5B;;AAEA;EACE,eAAe;EACf,iBAAiB;AACnB;;AAEA;EACE,oCAAoC;EACpC,mBAAmB;AACrB;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,4CAA4C;EAC5C,+CAA+C;EAC/C,mBAAmB;EACnB,wBAAwB;EACxB,UAAU;AACZ;AACA;EACE,aAAa;AACf;AACA;EACE,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,aAAc;EACd,mBAAmB;AACrB;AACA;EACE,gFAAgF;AAClF;;AAEA;EACE,wBAAwB;AAC1B;;AAEA;EACE,uDAAuD;EACvD,2DAA2D;EAC3D,2DAA2D;EAC3D,+DAA+D;EAC/D,+DAA+D;EAC/D,8CAA8C;EAC9C,iDAAiD;EACjD,mDAAmD;EACnD,iDAAiD;EACjD,gDAAgD;EAChD,gDAAgD;EAChD,uDAAuD;EACvD,8DAA8D;EAC9D,aAAa;EACb,yBAAiB;EAAjB,sBAAiB;EAAjB,iBAAiB;AACnB;AACA;EACE,yCAAyC;AAC3C;AACA;EACE,qBAAqB;AACvB;AACA;EACE,kFAAkF;EAClF,wCAAwC;EACxC,gBAAgB;AAClB;AACA;;EAEE,sDAAsD;AACxD;AACA;;EAEE,0BAA0B;AAC5B;AACA;EACE,4BAA4B;EAC5B,kBAAkB;AACpB;AACA;EACE,0BAA0B;EAC1B,4BAA4B;EAC5B,gEAAgE;AAClE;AACA;EACE;IACE,gBAAgB;EAClB;AACF;AACA;EACE,wCAAwC;AAC1C;AACA;EACE,gBAAgB;EAChB,sCAAsC;AACxC;;AAEA;EACE,0BAA0B;EAC1B,mBAAmB;AACrB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,cAAc;AAChB;AACA;EACE,WAAW;EACX,YAAY;AACd;;AAEA;;EAEE,wCAAwC;AAC1C;AACA;EACE,4BAA4B;AAC9B;;AAEA;EACE,kCAAkC;EAClC,6BAA6B;EAC7B,2CAA2C;EAC3C,aAAa;AACf;AACA;EACE,uBAAuB;AACzB;;AAEA;;EAEE,6BAA6B;AAC/B;;AAEA;EACE,cAAc;AAChB;;AAFA;EACE,cAAc;AAChB;;AAEA;;EAEE,4CAA4C;EAC5C,0CAA0C;EAC1C,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;AACA;EACE,2BAA2B;AAC7B;;AAEA;EACE,mCAAmC;EACnC,kDAAkD;EAClD,iCAAiC;EACjC,2BAA2B;EAC3B,2BAA2B;AAC7B;;AAEA;EACE,kCAAkC;EAClC,oBAAoB;EACpB,oBAAoB;EACpB,yCAAyC;EACzC,kCAAkC;AACpC;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,0BAA0B;AAC5B;;AAEA;EACE,uCAAuC;AACzC;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;;EAEE,sBAAsB;AACxB;;AAEA;EACE,mBAAmB;EACnB,oBAAoB;EACpB,kBAAkB;EAClB,uCAAuC;AACzC;;AAEA;EACE,gBAAgB;EAChB,2CAA2C;AAC7C;;AAEA;EACE,6DAA6D;EAC7D,kFAAkF;EAClF,eAAe;AACjB;AACA;EACE,aAAa;EACb,mBAAmB;AACrB;AACA;EACE,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,kFAAkF;EAClF,uDAAuD;EACvD,6BAA6B;EAC7B,UAAU;EACV,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,cAAc;EACd,aAAa;EACb,cAAc;EACd,eAAe;EACf,uCAAuC;AACzC;AACA;EACE,WAAW;AACb;;AAEA;EACE,2EAA2E;EAC3E,uCAAuC;EACvC,wBAAwB;AAC1B;AACA;EACE,2DAA2D;EAC3D,+CAA+C;EAC/C,0BAA0B;AAC5B;AACA;EACE,UAAU;AACZ;AACA;EACE,cAAc;AAChB;AACA;EACE,wCAAwC;AAC1C;AACA;EACE,YAAY;AACd;;AAEA;EACE,+BAA+B;EAC/B,6CAA6C;EAC7C,6CAAqC;EAArC,qCAAqC;AACvC;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,6FAA6F;EAC7F,6BAA6B;EAC7B,mDAAmD;AACrD;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,oGAAoG;EACpG,2BAA2B;EAC3B,qBAAqB;AACvB;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,iGAAiG;AACnG;;AAEA;;EAEE,kCAAkC;AACpC;;AAEA;EACE,oBAAoB;EACpB,mCAAmC;EACnC,kDAAkD;AACpD;;AAEA;EACE,4CAA4C;EAC5C,6CAA6C;AAC/C;AACA;EACE,+CAA+C;AACjD;AACA;EACE,2BAA2B;EAC3B,kBAAkB;EAClB,oBAAoB;AACtB;;AAEA;EACE,2CAA2C;EAC3C,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;EACxC,6BAA6B;EAC7B,sCAAsC;EACtC,uBAAuB;AACzB;AACA;EACE,UAAU;EACV,6DAA6D;EAC7D,iDAAiD;AACnD;;AAEA;EACE,wCAAwC;EACxC,sBAAsB;AACxB;AACA;EACE,UAAU;EACV,6DAA6D;AAC/D;;AAEA;EACE,wCAAwC;EACxC,iBAAiB;AACnB;AACA;EACE,wCAAwC;AAC1C;AACA;EACE,UAAU;EACV,6DAA6D;AAC/D;;AAEA;EACE,cAAc;AAChB;AACA;EACE,aAAa;EACb,cAAc;EACd,UAAS;EACT,UAAW;EACX,wCAAwC;AAC1C;AACA;EACE,gEAAgE;AAClE;AACA;EACE,UAAU;EACV,6DAA6D;AAC/D;AACA;EACE,qBAAoB;AACtB;;AAEA;EACE,gBAAgB;EAChB,cAAc;AAChB", "file": "tabler-vendors.rtl.css", "sourcesContent": ["/**\n * Converts a given value to a percentage string.\n *\n * @param {Number} $value - The value to be converted to a percentage.\n * @return {String} - The percentage representation of the value.\n */\n/**\n * Generates a transparent version of the given color.\n *\n * @param {Color} $color - The base color to be made transparent.\n * @param {Number} $alpha - The level of transparency, ranging from 0 (fully transparent) to 1 (fully opaque). Default is 1.\n * @return {Color} - The resulting color with the specified transparency.\n */\n.noUi-target {\n  border: 0;\n  box-shadow: none;\n  background: none;\n  border-radius: 0;\n  color: var(--tblr-primary);\n}\n\n.noUi-horizontal {\n  height: 1.25rem;\n  padding: 0.5rem 0;\n}\n\n.noUi-base {\n  background: var(--tblr-border-color);\n  border-radius: 1rem;\n}\n\n.noUi-handle {\n  width: 1rem;\n  height: 1rem;\n  border: 2px var(--tblr-border-style) #ffffff;\n  box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);\n  border-radius: 1rem;\n  background: currentColor;\n  outline: 0;\n}\n.noUi-handle:before, .noUi-handle:after {\n  content: none;\n}\n.noUi-horizontal .noUi-handle {\n  width: 1rem;\n  height: 1rem;\n  top: -0.5rem;\n  right: -0.5rem;\n  margin: 1px 1px 0 0;\n}\n.noUi-handle.noUi-active, .noUi-handle:focus {\n  box-shadow: 0 0 0 1px #f6f8fb, 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.noUi-connect {\n  background: currentColor;\n}\n\n.litepicker {\n  --litepicker-month-weekday-color: var(--tblr-secondary);\n  --litepicker-button-prev-month-color: var(--tblr-secondary);\n  --litepicker-button-next-month-color: var(--tblr-secondary);\n  --litepicker-button-prev-month-color-hover: var(--tblr-primary);\n  --litepicker-button-next-month-color-hover: var(--tblr-primary);\n  --litepicker-day-color: var(--tblr-body-color);\n  --litepicker-day-color-hover: var(--tblr-primary);\n  --litepicker-is-start-color-bg: var(--tblr-primary);\n  --litepicker-is-end-color-bg: var(--tblr-primary);\n  --litepicker-is-in-range-color: var(--tblr-info);\n  --litepicker-is-today-color: var(--tblr-primary);\n  --litepicker-month-header-color: var(--tblr-body-color);\n  --litepicker-container-months-color-bg: var(--tblr-bg-surface);\n  font: inherit;\n  user-select: none;\n}\n.litepicker .day-item.is-in-range {\n  --litepicker-day-color: var(--tblr-light);\n}\n.litepicker svg {\n  fill: none !important;\n}\n.litepicker .container__main {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  border-radius: var(--tblr-border-radius);\n  box-shadow: none;\n}\n.litepicker .container__months .month-item-name,\n.litepicker .container__months .month-item-year {\n  font-weight: var(--tblr-font-weight-medium) !important;\n}\n.litepicker .container__months .button-next-month,\n.litepicker .container__months .button-previous-month {\n  cursor: pointer !important;\n}\n.litepicker .container__months .month-item-weekdays-row > div {\n  padding: 0.5rem 0 !important;\n  font-size: 0.75rem;\n}\n.litepicker .container__days .day-item {\n  cursor: pointer !important;\n  padding: 0.5rem 0 !important;\n  transition: color 0.3s, background-color 0.3s, border-color 0.3s;\n}\n@media (prefers-reduced-motion: reduce) {\n  .litepicker .container__days .day-item {\n    transition: none;\n  }\n}\n.datepicker-inline .litepicker {\n  box-shadow: var(--tblr-box-shadow-input);\n}\n.datepicker-inline .litepicker .container__months {\n  box-shadow: none;\n  background-color: var(--tblr-bg-forms);\n}\n\n:root {\n  --ts-pr-clear-button: 0rem;\n  --ts-pr-caret: 0rem;\n}\n\n.ts-input {\n  color: inherit;\n}\n\n.ts-control {\n  color: inherit;\n}\n.ts-control .dropdown-menu {\n  width: 100%;\n  height: auto;\n}\n\n.ts-wrapper .form-control,\n.ts-wrapper .form-select, .ts-wrapper.form-control, .ts-wrapper.form-select {\n  box-shadow: var(--tblr-box-shadow-input);\n}\n.ts-wrapper.is-invalid .ts-control, .ts-wrapper.is-valid .ts-control {\n  --ts-pr-clear-button: 1.5rem;\n}\n\n.ts-dropdown {\n  background: var(--tblr-bg-surface);\n  color: var(--tblr-body-color);\n  box-shadow: var(--tblr-box-shadow-dropdown);\n  z-index: 1000;\n}\n.ts-dropdown .option {\n  padding: 0.5rem 0.75rem;\n}\n\n.ts-control,\n.ts-control input {\n  color: var(--tblr-body-color);\n}\n\n.ts-control input::placeholder {\n  color: #8a97ab;\n}\n\n.ts-wrapper.multi .ts-control > div,\n.ts-wrapper.multi.disabled .ts-control > div {\n  background: var(--tblr-bg-surface-secondary);\n  border: 1px solid var(--tblr-border-color);\n  color: var(--tblr-body-color);\n}\n\n.ts-wrapper.disabled .ts-control {\n  opacity: 1;\n}\n.ts-wrapper.disabled .ts-control > div.item {\n  color: var(--tblr-gray-500);\n}\n\n.apexcharts-tooltip {\n  color: var(--tblr-light) !important;\n  background: var(--tblr-bg-surface-dark) !important;\n  font-size: 0.765625rem !important;\n  padding: 0.25rem !important;\n  box-shadow: none !important;\n}\n\n.apexcharts-tooltip-title {\n  background: transparent !important;\n  border: 0 !important;\n  margin: 0 !important;\n  font-weight: var(--tblr-font-weight-bold);\n  padding: 0.25rem 0.5rem !important;\n}\n\n.apexcharts-tooltip-y-group {\n  padding: 2px 0 !important;\n}\n\n.apexcharts-tooltip-series-group {\n  padding: 0 0.5rem 0 !important;\n}\n\n.apexcharts-tooltip-marker:before {\n  font-size: 16px !important;\n}\n\n.apexcharts-text {\n  fill: var(--tblr-body-color) !important;\n}\n\n.apexcharts-gridline {\n  stroke: var(--tblr-border-color) !important;\n}\n\n.apexcharts-legend-text {\n  color: inherit !important;\n}\n\n.apexcharts-radialbar-track .apexcharts-radialbar-area {\n  stroke: var(--tblr-border-color) !important;\n}\n\n.apexcharts-svg,\n.apexcharts-canvas {\n  border-radius: inherit;\n}\n\n.jvm-tooltip {\n  background: #182433;\n  font-family: inherit;\n  font-size: 0.75rem;\n  box-shadow: var(--tblr-box-shadow-card);\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-title {\n  border-bottom: 0;\n  font-weight: var(--tblr-font-weight-medium);\n}\n\n.jvm-series-container .jvm-legend {\n  background-color: var(--tblr-card-bg, var(--tblr-bg-surface));\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  min-width: 8rem;\n}\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {\n  display: flex;\n  align-items: center;\n}\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-sample {\n  width: 0.75rem;\n  height: 0.75rem;\n}\n\n.jvm-zoom-btn {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  background: var(--tblr-card-bg, var(--tblr-bg-surface));\n  color: var(--tblr-body-color);\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n  width: 1.5rem;\n  height: 1.5rem;\n  font-size: 1rem;\n  box-shadow: var(--tblr-box-shadow-card);\n}\n.jvm-zoom-btn.jvm-zoomout {\n  top: 2.5rem;\n}\n\n.dropzone {\n  border: var(--tblr-border-width) dashed var(--tblr-border-color) !important;\n  color: var(--tblr-secondary) !important;\n  padding: 1rem !important;\n}\n.dropzone.dz-drag-hover {\n  border: var(--tblr-border-width) dashed var(--tblr-primary);\n  background: rgba(var(--tblr-primary-rgb), 0.01);\n  color: var(--tblr-primary);\n}\n.dropzone.dz-drag-hover .dz-message {\n  opacity: 1;\n}\n.dropzone .dz-preview {\n  margin: 0.5rem;\n}\n.dropzone .dz-preview .dz-image {\n  border-radius: var(--tblr-border-radius);\n}\n.dropzone .dz-preview .dz-success-mark {\n  height: 54px;\n}\n\n.fslightbox-container {\n  font-family: inherit !important;\n  background: rgba(24, 36, 51, 0.24) !important;\n  backdrop-filter: blur(4px) !important;\n}\n\n.fslightbox-slide-number-container {\n  color: inherit !important;\n}\n\n.fslightbox-slash {\n  background: currentColor !important;\n}\n\nbody {\n  --plyr-color-main: var(--tblr-primary);\n}\n\n.tox-tinymce {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n  border-radius: 6px !important;\n  font-family: var(--tblr-font-sans-serif) !important;\n}\n\n.tox-toolbar__group {\n  padding: 0 0.5rem 0;\n}\n\n.tox .tox-toolbar__primary {\n  background: transparent !important;\n}\n\n.tox:not(.tox-tinymce-inline) .tox-editor-header {\n  border-bottom: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n  box-shadow: none !important;\n  padding: 0 !important;\n}\n\n.tox-tbtn {\n  margin: 0 !important;\n}\n\n.tox-statusbar {\n  border-top: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n}\n\n.tox .tox-toolbar-overlord,\n.tox:not(.tox-tinymce-inline) .tox-editor-header {\n  background: transparent !important;\n}\n\n:root {\n  --gl-star-size: auto;\n  --gl-star-color: var(--tblr-yellow);\n  --gl-star-color-inactive: var(--tblr-border-color);\n}\n\n[data-star-rating] svg {\n  width: var(--tblr-icon-size, --gl-star-size);\n  height: var(--tblr-icon-size, --gl-star-size);\n}\n[data-star-rating] :not(.gl-active) > .gl-star-full {\n  color: var(--gl-star-color-inactive) !important;\n}\n[data-star-rating] .gl-active > .gl-star-full {\n  color: var(--gl-star-color);\n  fill: currentColor;\n  stroke: currentColor;\n}\n\n.clr-picker {\n  box-shadow: var(--tblr-box-shadow-dropdown);\n  background-color: var(--tblr-bg-surface);\n}\n\ninput.clr-color {\n  border-radius: var(--tblr-border-radius);\n  color: var(--tblr-body-color);\n  border-color: var(--tblr-border-color);\n  background: transparent;\n}\ninput.clr-color:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n  border-color: rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-swatches button {\n  border-radius: var(--tblr-border-radius);\n  padding: 0 2px 4px 2px;\n}\n.clr-swatches button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-preview {\n  border-radius: var(--tblr-border-radius);\n  overflow: visible;\n}\n.clr-preview button, .clr-preview:before, .clr-preview:after {\n  border-radius: var(--tblr-border-radius);\n}\n.clr-preview button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-field {\n  display: block;\n}\n.clr-field button {\n  width: 1.5rem;\n  height: 1.5rem;\n  left: 6px;\n  right: auto;\n  border-radius: var(--tblr-border-radius);\n}\n.clr-field button:after {\n  box-shadow: inset 0 0 0 1px var(--tblr-border-color-translucent);\n}\n.clr-field button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n.clr-field input {\n  padding-left: 2.5rem;\n}\n\n.typed-cursor {\n  font-weight: 500;\n  color: #6c7a91;\n}\n"]}