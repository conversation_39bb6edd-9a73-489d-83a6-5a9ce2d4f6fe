{% extends "base.html" %} {% block title %}新股上市 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">新股上市</h2>
      </div>
    </div>
  </div>
</div>
<div class="page-body">
  <div class="container-xl">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">新股上市列表</h3>
        <div class="card-actions">
          <form method="get" class="d-flex gap-2">
            <div class="input-icon">
              <span class="input-icon-addon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="icon"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                  <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" />
                  <line x1="21" y1="21" x2="15" y2="15" />
                </svg>
              </span>
              <input type="text" name="search" class="form-control" placeholder="搜索股票..." value="{{ search_query }}" />
            </div>
            <button type="submit" class="btn btn-primary">搜索</button>
          </form>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table table-vcenter card-table">
          <thead>
            <tr>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>上市日期</th>
              <th>发行价格</th>
              <th>首日收盘价</th>
              <th>首日涨跌幅</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for ipo in page_obj %}
            <tr>
              <td>{{ ipo.stock_code }}</td>
              <td>{{ ipo.stock_name }}</td>
              <td>{{ ipo.listing_date }}</td>
              <td>{{ ipo.issue_price }}</td>
              <td>{{ ipo.first_day_close }}</td>
              <td>{{ ipo.first_day_change_pct|floatformat:2 }}%</td>
              <td>
                <a href="{% url 'market_data:stock_detail' ipo.stock_code %}" class="btn btn-primary btn-sm">详情</a>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% include "includes/pagination.html" with page_obj=page_obj total_items=total_ipos %}
    </div>
  </div>
</div>
{% endblock %}
