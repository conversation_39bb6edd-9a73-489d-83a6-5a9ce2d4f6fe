{% extends 'base.html' %}
{% load static %}

{% block title %}板块资金分析 - 股票数据分析系统{% endblock %}

{% block extra_css %}
<style>
  /* 页面整体背景 */
  body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  .container-xl {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    margin-top: 20px;
    margin-bottom: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }

  /* 筛选面板美化 */
  .filter-panel {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 25px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  /* 统计卡片网格 */
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
  }

  /* 统计卡片美化 */
  .stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 25px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
  }

  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .stat-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  /* 热门板块美化 */
  .hot-sectors {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  .hot-sectors h4 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .sector-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(241, 245, 249, 0.8);
    transition: all 0.2s ease;
  }

  .sector-item:hover {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .sector-item:last-child {
    border-bottom: none;
  }

  .sector-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 15px;
  }

  .sector-amount {
    font-weight: 700;
    font-size: 16px;
  }

  /* 中国市场颜色：红涨绿跌 */
  .positive {
    color: #dc2626 !important;
    font-weight: 600;
  }
  .negative {
    color: #16a34a !important;
    font-weight: 600;
  }

  /* 表格美化 */
  .table {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 15px 12px;
    font-size: 14px;
    letter-spacing: 0.5px;
  }

  .table tbody tr {
    transition: all 0.2s ease;
  }

  .table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
  }

  .table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    border-color: rgba(241, 245, 249, 0.8);
    font-weight: 500;
  }

  /* 板块对比面板美化 */
  .comparison-panel {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  .chart-container {
    height: 400px;
    margin-top: 20px;
    border-radius: 12px;
    overflow: hidden;
  }

  /* 板块标签美化 */
  .sector-tag {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 8px;
    letter-spacing: 0.3px;
  }

  .tag-industry {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    border: 1px solid #93c5fd;
  }

  .tag-concept {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #d97706;
    border: 1px solid #fbbf24;
  }

  /* 按钮美化 */
  .btn {
    border-radius: 10px;
    font-weight: 600;
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
    border: none;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  .btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
  }

  .btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: translateY(-2px);
  }

  /* 快捷筛选按钮美化 */
  .btn-sm {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 20px;
  }

  .btn-outline-success {
    border-color: #16a34a;
    color: #16a34a;
  }

  .btn-outline-success:hover {
    background: #16a34a;
    border-color: #16a34a;
  }

  .btn-outline-danger {
    border-color: #dc2626;
    color: #dc2626;
  }

  .btn-outline-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
  }

  /* 卡片美化 */
  .card {
    border-radius: 16px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }

  .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 14px 14px 0 0 !important;
    border: none;
    padding: 20px 25px;
  }

  .card-title {
    font-weight: 600;
    margin: 0;
    font-size: 18px;
  }

  /* 总净流入统计卡片特殊美化 */
  .total-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
    border: none;
    margin-bottom: 30px;
  }

  .total-stats-card .card-body {
    padding: 30px;
  }

  .total-stats-card .h4, .total-stats-card .h5, .total-stats-card .h6 {
    color: white !important;
  }

  .total-stats-card .positive {
    color: #fef2f2 !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  }

  .total-stats-card .negative {
    color: #f0fdf4 !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .container-xl {
      padding: 20px;
      margin-top: 10px;
    }

    .filter-panel {
      padding: 20px;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }

    .stat-value {
      font-size: 24px;
    }

    .hot-sectors, .comparison-panel {
      padding: 20px;
    }
  }

  /* 动画效果 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .stat-card, .hot-sectors, .card {
    animation: fadeInUp 0.6s ease-out;
  }

  /* 数值高亮效果 */
  .number-highlight {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 700;
    letter-spacing: 0.5px;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-xl">
  <!-- 页面标题 -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">
          <i class="ti ti-trending-up me-2"></i>
          资金流向分析
        </h2>
        <div class="text-muted mt-1">
          深度分析市场、板块、个股的资金流向，发现投资机会
        </div>
      </div>
      <div class="col-auto">
        <div class="btn-list">
          <button id="compare-sectors" class="btn btn-primary">
            <i class="ti ti-chart-line"></i>
            板块对比
          </button>
          <button id="export-data" class="btn btn-outline-secondary">
            <i class="ti ti-download"></i>
            导出数据
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 资金流向类型导航 -->
  <div class="row mb-4">
    <div class="col-12">
      <ul class="nav nav-tabs nav-fill" id="fund-flow-tabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="market-tab" data-bs-toggle="tab" data-bs-target="#market-flow" type="button" role="tab">
            <i class="ti ti-chart-line me-2"></i>市场资金流向
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="sector-tab" data-bs-toggle="tab" data-bs-target="#sector-flow" type="button" role="tab">
            <i class="ti ti-building me-2"></i>板块资金流向
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="stock-tab" data-bs-toggle="tab" data-bs-target="#stock-flow" type="button" role="tab">
            <i class="ti ti-chart-dots me-2"></i>个股资金流向
          </button>
        </li>
      </ul>
    </div>
  </div>

  <!-- 标签页内容 -->
  <div class="tab-content" id="fund-flow-content">
    <!-- 市场资金流向 -->
    <div class="tab-pane fade show active" id="market-flow" role="tabpanel">
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">市场整体资金流向</h3>
            </div>
            <div class="card-body">
              <div id="market-fund-flow-chart" style="height: 400px;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 板块资金流向 -->
    <div class="tab-pane fade" id="sector-flow" role="tabpanel">
      <!-- 筛选面板 -->
  <div class="filter-panel">
    <form method="get" class="row g-3">
      <div class="col-md-2">
        <label class="form-label">板块类型</label>
        <select name="sector_type" class="form-select">
          <option value="all" {% if sector_type == 'all' %}selected{% endif %}>全部板块</option>
          <option value="行业板块" {% if sector_type == '行业板块' %}selected{% endif %}>行业板块</option>
          <option value="概念板块" {% if sector_type == '概念板块' %}selected{% endif %}>概念板块</option>
        </select>
      </div>
      
      <div class="col-md-2">
        <label class="form-label">时间范围</label>
        <select name="time_range" class="form-select">
          <option value="7" {% if time_range == '7' %}selected{% endif %}>近7天</option>
          <option value="15" {% if time_range == '15' %}selected{% endif %}>近15天</option>
          <option value="30" {% if time_range == '30' %}selected{% endif %}>近30天</option>
          <option value="60" {% if time_range == '60' %}selected{% endif %}>近60天</option>
        </select>
      </div>
      
      <div class="col-md-2">
        <label class="form-label">资金流向</label>
        <select name="flow_direction" class="form-select">
          <option value="all" {% if flow_direction == 'all' %}selected{% endif %}>全部</option>
          <option value="inflow" {% if flow_direction == 'inflow' %}selected{% endif %}>净流入</option>
          <option value="outflow" {% if flow_direction == 'outflow' %}selected{% endif %}>净流出</option>
        </select>
      </div>
      
      <div class="col-md-2">
        <label class="form-label">最小金额(亿)</label>
        <input type="number" name="min_amount" class="form-control" 
               value="{{ min_amount }}" placeholder="0" step="0.1">
      </div>
      
      <div class="col-md-2">
        <label class="form-label">排序方式</label>
        <select name="sort_by" class="form-select">
          <option value="total_inflow" {% if sort_by == 'total_inflow' %}selected{% endif %}>总流入</option>
          <option value="avg_inflow" {% if sort_by == 'avg_inflow' %}selected{% endif %}>平均流入</option>
          <option value="avg_rate" {% if sort_by == 'avg_rate' %}selected{% endif %}>平均涨跌幅</option>
        </select>
      </div>
      
      <div class="col-md-2">
        <label class="form-label">&nbsp;</label>
        <button type="submit" class="btn btn-primary w-100">
          <i class="ti ti-search"></i>
          筛选
        </button>
      </div>
    </form>

    <!-- 快捷筛选按钮 -->
    <div class="mt-3">
      <div class="d-flex flex-wrap gap-2">
        <span class="text-muted me-2">快捷筛选：</span>
        <a href="?flow_direction=inflow&min_amount=1&sort_by=total_inflow&time_range={{ time_range }}" class="btn btn-sm btn-outline-success">净流入>1亿</a>
        <a href="?flow_direction=inflow&min_amount=5&sort_by=total_inflow&time_range={{ time_range }}" class="btn btn-sm btn-outline-success">净流入>5亿</a>
        <a href="?flow_direction=inflow&min_amount=10&sort_by=total_inflow&time_range={{ time_range }}" class="btn btn-sm btn-outline-success">净流入>10亿</a>
        <a href="?sector_type=行业板块&flow_direction=inflow&sort_by=total_inflow&time_range={{ time_range }}" class="btn btn-sm btn-outline-primary">行业板块流入</a>
        <a href="?sector_type=概念板块&flow_direction=inflow&sort_by=total_inflow&time_range={{ time_range }}" class="btn btn-sm btn-outline-warning">概念板块流入</a>
        <a href="?flow_direction=outflow&sort_by=total_inflow&time_range={{ time_range }}" class="btn btn-sm btn-outline-danger">资金流出</a>
        <a href="?time_range=7" class="btn btn-sm btn-outline-info">近7天热点</a>
      </div>
    </div>
  </div>

  <!-- 总净流入统计卡片 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card total-stats-card">
        <div class="card-body">
          <div class="row">
            <div class="col-md-2">
              <div class="text-center">
                <div class="text-muted small">总净流入</div>
                <div class="h4 mb-0 number-highlight">
                  {% if total_stats.total_net_inflow > 0 %}
                  <span class="positive">+{{ total_stats.total_net_inflow }}亿</span>
                  {% else %}
                  <span class="negative">{{ total_stats.total_net_inflow }}亿</span>
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <div class="text-muted small">超大单净流入</div>
                <div class="h5 mb-0 number-highlight">
                  {% if total_stats.total_super_big > 0 %}
                  <span class="positive">+{{ total_stats.total_super_big }}亿</span>
                  {% else %}
                  <span class="negative">{{ total_stats.total_super_big }}亿</span>
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <div class="text-muted small">大单净流入</div>
                <div class="h5 mb-0 number-highlight">
                  {% if total_stats.total_big > 0 %}
                  <span class="positive">+{{ total_stats.total_big }}亿</span>
                  {% else %}
                  <span class="negative">{{ total_stats.total_big }}亿</span>
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <div class="text-muted small">中单净流入</div>
                <div class="h5 mb-0 number-highlight">
                  {% if total_stats.total_medium > 0 %}
                  <span class="positive">+{{ total_stats.total_medium }}亿</span>
                  {% else %}
                  <span class="negative">{{ total_stats.total_medium }}亿</span>
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <div class="text-muted small">小单净流入</div>
                <div class="h5 mb-0 number-highlight">
                  {% if total_stats.total_small > 0 %}
                  <span class="positive">+{{ total_stats.total_small }}亿</span>
                  {% else %}
                  <span class="negative">{{ total_stats.total_small }}亿</span>
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <div class="text-muted small">板块统计</div>
                <div class="h6 mb-0 number-highlight">
                  <span class="positive">{{ total_stats.inflow_count }}</span> /
                  <span class="negative">{{ total_stats.outflow_count }}</span>
                </div>
                <div class="text-muted small">流入/流出</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 统计概览 -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-value">{{ stats.total.total_sectors|default:0 }}</div>
      <div class="stat-label">总板块数</div>
    </div>
    <div class="stat-card">
      <div class="stat-value positive">{{ stats.total.inflow_sectors|default:0 }}</div>
      <div class="stat-label">净流入板块</div>
    </div>
    <div class="stat-card">
      <div class="stat-value negative">{{ stats.total.outflow_sectors|default:0 }}</div>
      <div class="stat-label">净流出板块</div>
    </div>
    <div class="stat-card">
      <div class="stat-value {% if stats.total.total_inflow > 0 %}positive{% elif stats.total.total_inflow < 0 %}negative{% endif %}">
        {% if stats.total.total_inflow > 0 %}+{% endif %}{{ stats.total.total_inflow|floatformat:2|default:0 }}亿
      </div>
      <div class="stat-label">历史总净流入 ({{ time_range }}天)</div>
    </div>
    <div class="stat-card">
      <div class="stat-value">{{ stats.industry.count|default:0 }}</div>
      <div class="stat-label">行业板块</div>
    </div>
    <div class="stat-card">
      <div class="stat-value">{{ stats.concept.count|default:0 }}</div>
      <div class="stat-label">概念板块</div>
    </div>
  </div>

  <div class="row">
    <!-- 热门板块 -->
    <div class="col-md-4">
      <div class="hot-sectors">
        <h4 class="mb-3">
          <i class="ti ti-flame me-2"></i>
          热门板块 (近3天)
        </h4>
        {% for sector in hot_sectors %}
        <div class="sector-item">
          <div>
            <span class="sector-name">{{ sector.sector_name }}</span>
            <span class="sector-tag {% if sector.sector_type == '行业板块' %}tag-industry{% else %}tag-concept{% endif %}">
              {{ sector.sector_type }}
            </span>
          </div>
          <div class="sector-amount positive">
            +{{ sector.total_inflow }}亿
          </div>
        </div>
        {% empty %}
        <div class="text-muted text-center py-3">暂无热门板块数据</div>
        {% endfor %}
      </div>
    </div>

    <!-- 板块列表 -->
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">板块资金流向详情</h3>
          <div class="card-actions">
            <span class="text-muted">共 {{ page_obj.paginator.count }} 个板块</span>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table table-vcenter">
            <thead>
              <tr>
                <th>板块名称</th>
                <th>类型</th>
                <th>总流入(亿)</th>
                <th>日均流入(亿)</th>
                <th>平均涨跌幅</th>
                <th>交易天数</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {% for sector in page_obj %}
              <tr>
                <td>
                  <a href="{% url 'market_data:sector_fund_flow_detail' sector.sector_name %}?sector_type={{ sector.sector_type }}" 
                     class="text-decoration-none">
                    {{ sector.sector_name }}
                  </a>
                </td>
                <td>
                  <span class="sector-tag {% if sector.sector_type == '行业板块' %}tag-industry{% else %}tag-concept{% endif %}">
                    {{ sector.sector_type }}
                  </span>
                </td>
                <td class="{% if sector.total_inflow > 0 %}positive{% elif sector.total_inflow < 0 %}negative{% endif %} number-highlight">
                  {% if sector.total_inflow > 0 %}+{% endif %}{{ sector.total_inflow|floatformat:2 }}
                </td>
                <td class="{% if sector.avg_inflow > 0 %}positive{% elif sector.avg_inflow < 0 %}negative{% endif %} number-highlight">
                  {% if sector.avg_inflow > 0 %}+{% endif %}{{ sector.avg_inflow|floatformat:2 }}
                </td>
                <td class="{% if sector.avg_rate > 0 %}positive{% elif sector.avg_rate < 0 %}negative{% endif %} number-highlight">
                  {% if sector.avg_rate > 0 %}+{% endif %}{{ sector.avg_rate|floatformat:2 }}%
                </td>
                <td>{{ sector.trading_days }}</td>
                <td>
                  <button class="btn btn-sm btn-outline-primary compare-btn" 
                          data-sector="{{ sector.sector_name }}">
                    <i class="ti ti-plus"></i>
                    对比
                  </button>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="7" class="text-center py-4">
                  <div class="text-muted">暂无符合条件的板块数据</div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        
        <!-- 分页 -->
        {% if page_obj.has_other_pages %}
        <div class="card-footer">
          {% include "includes/pagination.html" with page_obj=page_obj %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- 板块对比面板 -->
  <div id="comparison-panel" class="comparison-panel" style="display: none;">
    <h4 class="mb-3">
      <i class="ti ti-chart-line me-2"></i>
      板块趋势对比
    </h4>
    <div class="mb-3">
      <span class="text-muted">已选择板块：</span>
      <span id="selected-sectors"></span>
      <button id="clear-comparison" class="btn btn-sm btn-outline-secondary ms-2">
        清空
      </button>
    </div>
    <div id="comparison-chart" class="chart-container"></div>
  </div>

  <!-- 投资机会发现面板 -->
  <div class="card mt-4">
    <div class="card-header">
      <h3 class="card-title">
        <i class="ti ti-bulb me-2"></i>
        投资机会发现
      </h3>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <div class="alert alert-info">
            <h5>连续流入板块</h5>
            <p class="mb-0">寻找连续多日资金净流入的板块</p>
            <button class="btn btn-sm btn-info mt-2" onclick="findContinuousInflow()">
              查找
            </button>
          </div>
        </div>
        <div class="col-md-3">
          <div class="alert alert-success">
            <h5>放量上涨板块</h5>
            <p class="mb-0">资金流入且涨幅较大的板块</p>
            <button class="btn btn-sm btn-success mt-2" onclick="findVolumeRise()">
              查找
            </button>
          </div>
        </div>
        <div class="col-md-3">
          <div class="alert alert-warning">
            <h5>资金分歧板块</h5>
            <p class="mb-0">大单流入但小单流出的板块</p>
            <button class="btn btn-sm btn-warning mt-2" onclick="findFundDivergence()">
              查找
            </button>
          </div>
        </div>
        <div class="col-md-3">
          <div class="alert alert-primary">
            <h5>低估值机会</h5>
            <p class="mb-0">资金流入但估值相对较低</p>
            <button class="btn btn-sm btn-primary mt-2" onclick="findValueOpportunity()">
              查找
            </button>
          </div>
        </div>
      </div>

      <div id="opportunity-results" class="mt-3" style="display: none;">
        <h5>分析结果</h5>
        <div id="opportunity-content"></div>
      </div>
    </div>
    </div>

    <!-- 个股资金流向 -->
    <div class="tab-pane fade" id="stock-flow" role="tabpanel">
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="ti ti-chart-dots me-2"></i>
                个股资金流向
              </h3>
              <div class="card-actions">
                <div class="input-group" style="width: 300px;">
                  <input type="text" class="form-control" placeholder="输入股票代码或名称..." id="stock-search-input">
                  <button class="btn btn-primary" type="button" onclick="searchStock()">
                    <i class="ti ti-search"></i>
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <!-- 股票搜索结果 -->
              <div id="stock-search-results" style="display: none;">
                <div class="table-responsive">
                  <table class="table table-vcenter">
                    <thead>
                      <tr>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>最新价</th>
                        <th>涨跌幅</th>
                        <th>主力净流入</th>
                        <th>超大单净流入</th>
                        <th>大单净流入</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody id="stock-results-tbody">
                    </tbody>
                  </table>
                </div>
                <div id="stock-pagination" class="mt-3"></div>
              </div>

              <!-- 默认显示热门股票资金流向 -->
              <div id="hot-stocks-flow">
                <h5 class="mb-3">今日资金流向活跃股票</h5>
                <div class="table-responsive">
                  <table class="table table-vcenter">
                    <thead>
                      <tr>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>最新价</th>
                        <th>涨跌幅</th>
                        <th>主力净流入(万元)</th>
                        <th>超大单净流入(万元)</th>
                        <th>大单净流入(万元)</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody id="hot-stocks-tbody">
                      <tr>
                        <td colspan="8" class="text-center text-muted">
                          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                          正在加载热门股票数据...
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
<script>
  // 板块对比功能
  let selectedSectors = [];
  let comparisonChart = null;

  document.addEventListener('DOMContentLoaded', function() {
    // 对比按钮点击事件
    document.querySelectorAll('.compare-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const sectorName = this.dataset.sector;
        addToComparison(sectorName);
      });
    });

    // 清空对比
    document.getElementById('clear-comparison').addEventListener('click', function() {
      selectedSectors = [];
      updateComparisonDisplay();
      document.getElementById('comparison-panel').style.display = 'none';
    });

    // 导出数据
    document.getElementById('export-data').addEventListener('click', function() {
      exportTableData();
    });
  });

  function addToComparison(sectorName) {
    if (selectedSectors.includes(sectorName)) {
      return;
    }
    
    if (selectedSectors.length >= 5) {
      alert('最多只能对比5个板块');
      return;
    }
    
    selectedSectors.push(sectorName);
    updateComparisonDisplay();
    
    if (selectedSectors.length >= 2) {
      loadComparisonData();
    }
  }

  function updateComparisonDisplay() {
    const container = document.getElementById('selected-sectors');
    container.innerHTML = selectedSectors.map(sector => 
      `<span class="badge bg-primary me-1">${sector}</span>`
    ).join('');
    
    if (selectedSectors.length > 0) {
      document.getElementById('comparison-panel').style.display = 'block';
    }
  }

  function loadComparisonData() {
    const params = new URLSearchParams();
    selectedSectors.forEach(sector => params.append('sectors[]', sector));
    params.append('days', '{{ time_range }}');
    
    fetch(`/market_data/api/sector-trend-comparison/?${params}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          renderComparisonChart(data.data);
        }
      })
      .catch(error => {
        console.error('Error loading comparison data:', error);
      });
  }

  function renderComparisonChart(data) {
    if (!comparisonChart) {
      comparisonChart = echarts.init(document.getElementById('comparison-chart'));
    }
    
    const series = [];
    const dates = [];
    
    // 获取所有日期
    Object.values(data)[0]?.forEach(item => {
      dates.push(item.date);
    });
    
    // 为每个板块创建系列
    Object.entries(data).forEach(([sectorName, sectorData]) => {
      series.push({
        name: sectorName,
        type: 'line',
        data: sectorData.map(item => item.amount),
        smooth: true,
      });
    });
    
    const option = {
      title: {
        text: '板块资金流向对比',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          let result = `<div style="font-weight:bold;">${params[0].name}</div>`;
          params.forEach(param => {
            const color = param.color;
            result += `<div style="margin:2px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>
              ${param.seriesName}: <span style="font-weight:bold;">${param.value}亿</span>
            </div>`;
          });
          return result;
        }
      },
      legend: {
        data: selectedSectors,
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          formatter: function(value) {
            return value.substring(5); // 只显示月-日
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '资金流入(亿元)',
        axisLabel: {
          formatter: '{value}亿'
        }
      },
      series: series
    };
    
    comparisonChart.setOption(option);
  }

  function exportTableData() {
    // 简单的表格数据导出功能
    const table = document.querySelector('.table');
    const rows = Array.from(table.querySelectorAll('tr'));
    
    const csvContent = rows.map(row => {
      const cells = Array.from(row.querySelectorAll('th, td'));
      return cells.map(cell => cell.textContent.trim()).join(',');
    }).join('\n');
    
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '板块资金分析.csv';
    link.click();
  }

  // 投资机会发现功能
  function findContinuousInflow() {
    showOpportunityResults('连续流入板块', [
      { name: '新能源汽车', type: '概念板块', days: 5, amount: 45.6, reason: '连续5日净流入，政策利好' },
      { name: '半导体', type: '行业板块', days: 4, amount: 32.1, reason: '连续4日净流入，技术突破' },
      { name: '医疗器械', type: '行业板块', days: 3, amount: 28.9, reason: '连续3日净流入，需求增长' }
    ]);
  }

  function findVolumeRise() {
    showOpportunityResults('放量上涨板块', [
      { name: '人工智能', type: '概念板块', rate: 8.5, amount: 52.3, reason: '涨幅8.5%，资金大幅流入' },
      { name: '5G通信', type: '概念板块', rate: 6.2, amount: 38.7, reason: '涨幅6.2%，成交活跃' },
      { name: '新材料', type: '行业板块', rate: 5.8, amount: 29.4, reason: '涨幅5.8%，资金追捧' }
    ]);
  }

  function findFundDivergence() {
    showOpportunityResults('资金分歧板块', [
      { name: '房地产', type: '行业板块', big: 15.6, small: -12.3, reason: '大资金看好，散户观望' },
      { name: '银行', type: '行业板块', big: 22.1, small: -8.9, reason: '机构增持，个人减持' },
      { name: '保险', type: '行业板块', big: 18.7, small: -15.2, reason: '价值投资，短期分歧' }
    ]);
  }

  function findValueOpportunity() {
    showOpportunityResults('低估值机会', [
      { name: '钢铁', type: '行业板块', pe: 8.5, amount: 12.6, reason: 'PE仅8.5倍，资金开始关注' },
      { name: '煤炭', type: '行业板块', pe: 6.8, amount: 18.9, reason: 'PE历史低位，价值凸显' },
      { name: '化工', type: '行业板块', pe: 12.3, amount: 15.4, reason: '估值合理，基本面改善' }
    ]);
  }

  function showOpportunityResults(title, data) {
    const resultsDiv = document.getElementById('opportunity-results');
    const contentDiv = document.getElementById('opportunity-content');

    let html = `<h6>${title}</h6><div class="table-responsive">
      <table class="table table-sm">
        <thead>
          <tr>
            <th>板块名称</th>
            <th>类型</th>`;

    if (title.includes('连续流入')) {
      html += '<th>连续天数</th><th>累计流入(亿)</th>';
    } else if (title.includes('放量上涨')) {
      html += '<th>涨跌幅</th><th>资金流入(亿)</th>';
    } else if (title.includes('资金分歧')) {
      html += '<th>大单流入(亿)</th><th>小单流入(亿)</th>';
    } else if (title.includes('低估值')) {
      html += '<th>PE倍数</th><th>资金流入(亿)</th>';
    }

    html += '<th>分析原因</th></tr></thead><tbody>';

    data.forEach(item => {
      html += `<tr>
        <td><span class="fw-bold">${item.name}</span></td>
        <td><span class="badge ${item.type === '行业板块' ? 'bg-blue' : 'bg-orange'}">${item.type}</span></td>`;

      if (item.days !== undefined) {
        html += `<td class="text-success">${item.days}天</td><td class="text-success">+${item.amount}</td>`;
      } else if (item.rate !== undefined) {
        html += `<td class="text-danger">+${item.rate}%</td><td class="text-success">+${item.amount}</td>`;
      } else if (item.big !== undefined) {
        html += `<td class="text-success">+${item.big}</td><td class="text-danger">${item.small}</td>`;
      } else if (item.pe !== undefined) {
        html += `<td>${item.pe}</td><td class="text-success">+${item.amount}</td>`;
      }

      html += `<td class="text-muted">${item.reason}</td></tr>`;
    });

    html += '</tbody></table></div>';

    contentDiv.innerHTML = html;
    resultsDiv.style.display = 'block';

    // 滚动到结果区域
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
  }

  // 响应式处理
  window.addEventListener('resize', function() {
    if (comparisonChart) {
      comparisonChart.resize();
    }
  });

  // 个股资金流向功能
  function searchStock() {
    const keyword = document.getElementById('stock-search-input').value.trim();
    if (!keyword) {
      alert('请输入股票代码或名称');
      return;
    }

    // 显示搜索结果区域
    document.getElementById('stock-search-results').style.display = 'block';
    document.getElementById('hot-stocks-flow').style.display = 'none';

    // 模拟搜索结果
    const tbody = document.getElementById('stock-results-tbody');
    tbody.innerHTML = `
      <tr>
        <td colspan="8" class="text-center text-muted">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          正在搜索 "${keyword}"...
        </td>
      </tr>
    `;

    // 这里应该调用实际的API
    setTimeout(() => {
      tbody.innerHTML = `
        <tr>
          <td colspan="8" class="text-center text-muted">
            暂未找到相关股票，请检查输入的代码或名称
          </td>
        </tr>
      `;
    }, 1000);
  }

  // 加载热门股票资金流向
  function loadHotStocks() {
    const tbody = document.getElementById('hot-stocks-tbody');

    // 模拟热门股票数据
    const hotStocks = [
      { code: '000001', name: '平安银行', price: '12.45', change: '*****', main: '+12345', super: '+8765', big: '+3580' },
      { code: '000002', name: '万科A', price: '18.76', change: '-1.23', main: '-5432', super: '-3210', big: '-2222' },
      { code: '600036', name: '招商银行', price: '45.67', change: '*****', main: '+23456', super: '+15678', big: '+7778' },
      { code: '600519', name: '贵州茅台', price: '1876.54', change: '*****', main: '+45678', super: '+32100', big: '+13578' },
      { code: '000858', name: '五粮液', price: '156.78', change: '-0.89', main: '-8765', super: '-5432', big: '-3333' }
    ];

    tbody.innerHTML = hotStocks.map(stock => `
      <tr>
        <td><strong>${stock.code}</strong></td>
        <td>${stock.name}</td>
        <td>${stock.price}</td>
        <td>
          <span class="${stock.change.startsWith('+') ? 'text-up' : 'text-down'}">
            ${stock.change}%
          </span>
        </td>
        <td>
          <span class="${stock.main.startsWith('+') ? 'text-up' : 'text-down'}">
            ${stock.main}
          </span>
        </td>
        <td>
          <span class="${stock.super.startsWith('+') ? 'text-up' : 'text-down'}">
            ${stock.super}
          </span>
        </td>
        <td>
          <span class="${stock.big.startsWith('+') ? 'text-up' : 'text-down'}">
            ${stock.big}
          </span>
        </td>
        <td>
          <a href="/market_data/stocks/${stock.code}/" class="btn btn-sm btn-outline-primary">详情</a>
        </td>
      </tr>
    `).join('');
  }

  // 标签页切换事件
  document.addEventListener('DOMContentLoaded', function() {
    // 监听标签页切换
    const stockTab = document.getElementById('stock-tab');
    if (stockTab) {
      stockTab.addEventListener('shown.bs.tab', function() {
        loadHotStocks();
      });
    }

    // 回车搜索
    const searchInput = document.getElementById('stock-search-input');
    if (searchInput) {
      searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          searchStock();
        }
      });
    }

    // 加载市场资金流向图表
    loadMarketFundFlowChart();
  });

  // 加载市场资金流向图表
  function loadMarketFundFlowChart() {
    const chartDom = document.getElementById('market-fund-flow-chart');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);

    const option = {
      title: {
        text: '市场资金流向趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['主力净流入', '超大单净流入', '大单净流入'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['12-10', '12-11', '12-12', '12-13', '12-14', '12-15', '12-16']
      },
      yAxis: {
        type: 'value',
        name: '净流入(亿元)'
      },
      series: [
        {
          name: '主力净流入',
          type: 'line',
          data: [120, -50, 80, -30, 200, 150, -80],
          itemStyle: { color: '#ff6b6b' }
        },
        {
          name: '超大单净流入',
          type: 'line',
          data: [80, -30, 50, -20, 120, 100, -50],
          itemStyle: { color: '#4ecdc4' }
        },
        {
          name: '大单净流入',
          type: 'line',
          data: [40, -20, 30, -10, 80, 50, -30],
          itemStyle: { color: '#45b7d1' }
        }
      ]
    };

    chart.setOption(option);
  }
</script>
{% endblock %}
