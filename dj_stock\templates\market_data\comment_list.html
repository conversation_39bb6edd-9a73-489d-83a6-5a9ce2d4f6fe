{% extends "base.html" %}
{% block title %}千股千评 - 股票市场数据{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          千股千评
        </h2>
        <div class="text-muted mt-1">股票综合评分数据</div>
      </div>
    </div>
  </div>
</div>
<div class="page-body">
  <div class="container-xl">
    <div class="card">
      <div class="card-body">
        <div class="d-flex">
          <form method="get" class="d-flex flex-wrap gap-2 me-auto">
            <div class="input-group">
              <input type="text" class="form-control" name="stock_code" value="{{ stock_code }}" placeholder="股票代码">
            </div>
            <div class="input-group">
              <input type="text" class="form-control" name="stock_name" value="{{ stock_name }}" placeholder="股票名称">
            </div>
            <div class="input-group">
              <select class="form-select" name="date">
                <option value="">选择日期</option>
                {% for date_obj in available_dates %}
                <option value="{{ date_obj|date:'Y-m-d' }}" {% if date == date_obj|date:'Y-m-d' %}selected{% endif %}>{{ date_obj|date:'Y-m-d' }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="input-group">
              <select class="form-select" name="sort">
                <option value="-comprehensive_score" {% if sort_by == '-comprehensive_score' %}selected{% endif %}>综合评分↓</option>
                <option value="comprehensive_score" {% if sort_by == 'comprehensive_score' %}selected{% endif %}>综合评分↑</option>
                <option value="-institution_participation" {% if sort_by == '-institution_participation' %}selected{% endif %}>机构参与度↓</option>
                <option value="-attention_index" {% if sort_by == '-attention_index' %}selected{% endif %}>关注指数↓</option>
                <option value="-rise_rank" {% if sort_by == '-rise_rank' %}selected{% endif %}>上升排名↓</option>
                <option value="current_rank" {% if sort_by == 'current_rank' %}selected{% endif %}>当前排名↑</option>
              </select>
            </div>
            <button type="submit" class="btn btn-primary">
              <i class="ti ti-search"></i>
              搜索
            </button>
          </form>
        </div>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-vcenter table-hover card-table">
            <thead>
              <tr>
                <th>股票代码</th>
                <th>股票名称</th>
                <th>日期</th>
                <th>最新价</th>
                <th>涨跌幅</th>
                <th>换手率</th>
                <th>机构参与度</th>
                <th>关注指数</th>
                <th>综合评分</th>
                <th>排名</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {% for comment in page_obj %}
              <tr>
                <td>{{ comment.stock_code }}</td>
                <td>{{ comment.stock_name }}</td>
                <td>{{ comment.date }}</td>
                <td>{{ comment.current_price }}</td>
                <td class="{% if comment.change_ratio > 0 %}text-success{% elif comment.change_ratio < 0 %}text-danger{% endif %}">
                  {{ comment.change_ratio }}%
                </td>
                <td>{{ comment.turnover_rate }}%</td>
                <td>{{ comment.institution_participation }}</td>
                <td>{{ comment.attention_index }}</td>
                <td class="fw-bold">{{ comment.comprehensive_score }}</td>
                <td>{{ comment.current_rank }}</td>
                <td>
                  <a href="{% url 'market_data:comment_detail' comment.stock_code %}?date={{ comment.date|date:'Y-m-d' }}" class="btn btn-sm btn-outline-primary">
                    <i class="ti ti-eye"></i> 详情
                  </a>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="11" class="text-center py-3">暂无数据</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% include "includes/pagination.html" with page_obj=page_obj %}
      </div>
    </div>
  </div>
</div>
{% endblock %} 