{% extends "base.html" %}
{% block title %}财务报告 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">财务分析</div>
        <h2 class="page-title">财务报告</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:index' %}" class="btn btn-outline-primary">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
              <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
              <path d="M5 12l-2 0l9 -9l9 9l-2 0" />
              <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
              <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />
            </svg>
            返回首页
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">财务报告列表</h3>
            <div class="card-actions">
              <form method="get" class="d-flex gap-2">
                <input type="text" class="form-control" name="q" value="{{ query }}" placeholder="搜索股票代码或名称">
                <select class="form-select" name="report_type">
                  <option value="">所有报告类型</option>
                  <option value="年报" {% if report_type == "年报" %}selected{% endif %}>年报</option>
                  <option value="中报" {% if report_type == "中报" %}selected{% endif %}>中报</option>
                  <option value="一季报" {% if report_type == "一季报" %}selected{% endif %}>一季报</option>
                  <option value="三季报" {% if report_type == "三季报" %}selected{% endif %}>三季报</option>
                </select>
                <select class="form-select" name="year">
                  <option value="">所有年份</option>
                  {% for y in all_years %}
                  <option value="{{ y }}" {% if year == y|stringformat:"s" %}selected{% endif %}>{{ y }}年</option>
                  {% endfor %}
                </select>
                <button type="submit" class="btn btn-primary">搜索</button>
              </form>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter card-table table-hover">
                <thead>
                  <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>报告期</th>
                    <th class="text-end">营业收入(亿)</th>
                    <th class="text-end">每股收益</th>
                    <th class="text-end">净资产收益率</th>
                    <th class="text-end">资产负债率</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {% for financial in page_obj %}
                  <tr class="cursor-pointer" onclick="window.location='{% url 'financial_analysis:financial_indicator' financial.stock_code %}'">
                    <td>{{ financial.stock_code }}</td>
                    <td>{{ financial.stock_name }}</td>
                    <td>{{ financial.report_date|date:"Y-m-d" }}</td>
                    <td class="text-end">{{ financial.total_revenue|floatformat:2|default:"-" }}</td>
                    <td class="text-end {% if financial.eps > 0 %}text-danger{% elif financial.eps < 0 %}text-success{% endif %}">
                      {{ financial.eps|floatformat:4|default:"-" }}
                    </td>
                    <td class="text-end {% if financial.roe > 0 %}text-danger{% elif financial.roe < 0 %}text-success{% endif %}">
                      {{ financial.roe|floatformat:2|default:"-" }}%
                    </td>
                    <td class="text-end {% if financial.debt_asset_ratio > 70 %}text-danger{% elif financial.debt_asset_ratio < 40 %}text-success{% endif %}">
                      {{ financial.debt_asset_ratio|floatformat:2|default:"-" }}%
                    </td>
                    <td>
                      <div class="btn-list">
                        <a href="{% url 'financial_analysis:financial_indicator' financial.stock_code %}" class="btn btn-sm btn-primary">
                          详细指标
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modal-report-{{ financial.id }}">
                          更多数据
                        </button>
                      </div>
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="8" class="text-center">暂无财务数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% include "includes/pagination.html" with page_obj=page_obj %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% for financial in page_obj %}
<!-- 详细数据弹窗 -->
<div class="modal modal-blur fade" id="modal-report-{{ financial.id }}" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ financial.stock_name }} ({{ financial.stock_code }}) - {{ financial.report_date|date:"Y年m月d日" }}财务数据</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">营业收入(亿)</label>
              <div class="form-control-plaintext">{{ financial.total_revenue|floatformat:2|default:"-" }}</div>
            </div>
            <div class="mb-3">
              <label class="form-label">每股收益(EPS)</label>
              <div class="form-control-plaintext {% if financial.eps > 0 %}text-danger{% elif financial.eps < 0 %}text-success{% endif %}">
                {{ financial.eps|floatformat:4|default:"-" }}
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">净资产收益率(ROE)</label>
              <div class="form-control-plaintext {% if financial.roe > 0 %}text-danger{% elif financial.roe < 0 %}text-success{% endif %}">
                {{ financial.roe|floatformat:2|default:"-" }}%
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">总资产收益率(ROA)</label>
              <div class="form-control-plaintext {% if financial.roa > 0 %}text-danger{% elif financial.roa < 0 %}text-success{% endif %}">
                {{ financial.roa|floatformat:2|default:"-" }}%
              </div>
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">毛利率</label>
              <div class="form-control-plaintext {% if financial.gross_profit_margin > 0 %}text-danger{% elif financial.gross_profit_margin < 0 %}text-success{% endif %}">
                {{ financial.gross_profit_margin|floatformat:2|default:"-" }}%
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">营业利润率</label>
              <div class="form-control-plaintext {% if financial.operating_margin > 0 %}text-danger{% elif financial.operating_margin < 0 %}text-success{% endif %}">
                {{ financial.operating_margin|floatformat:2|default:"-" }}%
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">净利率</label>
              <div class="form-control-plaintext {% if financial.net_profit_margin > 0 %}text-danger{% elif financial.net_profit_margin < 0 %}text-success{% endif %}">
                {{ financial.net_profit_margin|floatformat:2|default:"-" }}%
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">资产负债率</label>
              <div class="form-control-plaintext {% if financial.debt_asset_ratio > 70 %}text-danger{% elif financial.debt_asset_ratio < 40 %}text-success{% endif %}">
                {{ financial.debt_asset_ratio|floatformat:2|default:"-" }}%
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <a href="{% url 'financial_analysis:financial_indicator' financial.stock_code %}" class="btn btn-primary">查看完整财务指标</a>
        <button type="button" class="btn btn-link link-secondary" data-bs-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>
{% endfor %}

{% endblock %}

{% block extra_css %}
<style>
  .cursor-pointer {
    cursor: pointer;
  }
  .cursor-pointer:hover {
    background-color: rgba(32, 107, 196, 0.03);
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 阻止点击"更多数据"按钮时触发行点击事件
    document.querySelectorAll('.btn-outline-primary').forEach(function(btn) {
      btn.addEventListener('click', function(e) {
        e.stopPropagation();
      });
    });
  });
</script>
{% endblock %}
