# Generated by Django 5.1.7 on 2025-04-23 13:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('market_data', '0003_delete_stockfinancialindicator'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockFundFlow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=10, verbose_name='代码')),
                ('name', models.CharField(max_length=100, verbose_name='名称')),
                ('date', models.DateField(verbose_name='交易日期')),
                ('type', models.CharField(help_text='个股/北向资金', max_length=50, verbose_name='数据类型')),
                ('main_net_inflow', models.FloatField(blank=True, null=True, verbose_name='主力净流入(元)')),
                ('retail_net_inflow', models.FloatField(blank=True, null=True, verbose_name='散户净流入(元)')),
                ('total_net_inflow', models.FloatField(blank=True, null=True, verbose_name='总净流入(元)')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '资金流向数据',
                'verbose_name_plural': '资金流向数据',
                'db_table': 'stock_fund_flow',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockLimitList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('change_ratio', models.FloatField(blank=True, null=True, verbose_name='涨跌幅')),
                ('latest_price', models.FloatField(blank=True, null=True, verbose_name='最新价')),
                ('amount', models.BigIntegerField(blank=True, null=True, verbose_name='成交额')),
                ('circulation_market_value', models.FloatField(blank=True, null=True, verbose_name='流通市值')),
                ('total_market_value', models.FloatField(blank=True, null=True, verbose_name='总市值')),
                ('turnover_ratio', models.FloatField(blank=True, null=True, verbose_name='换手率')),
                ('fund_amount', models.BigIntegerField(blank=True, null=True, verbose_name='封单资金')),
                ('first_time', models.TimeField(blank=True, null=True, verbose_name='首次封板时间')),
                ('last_time', models.TimeField(verbose_name='最后封板时间')),
                ('break_count', models.IntegerField(default=0, verbose_name='炸板次数/开板次数')),
                ('continuous_limit', models.IntegerField(default=1, verbose_name='连板数/连续跌停')),
                ('industry', models.CharField(blank=True, max_length=50, null=True, verbose_name='所属行业')),
                ('limit_type', models.CharField(max_length=10, verbose_name='涨跌停类型')),
                ('date', models.DateField(verbose_name='交易日期')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '涨跌停数据',
                'verbose_name_plural': '涨跌停数据',
                'db_table': 'stock_limit_list',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockMarketFundFlow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trade_date', models.DateField(verbose_name='交易日期')),
                ('sh_index_close', models.FloatField(blank=True, null=True, verbose_name='上证-收盘价')),
                ('sh_index_change_pct', models.FloatField(blank=True, null=True, verbose_name='上证-涨跌幅')),
                ('sz_index_close', models.FloatField(blank=True, null=True, verbose_name='深证-收盘价')),
                ('sz_index_change_pct', models.FloatField(blank=True, null=True, verbose_name='深证-涨跌幅')),
                ('main_net_inflow', models.FloatField(blank=True, null=True, verbose_name='主力净流入-净额')),
                ('main_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='主力净流入-净占比')),
                ('super_big_net_inflow', models.FloatField(blank=True, null=True, verbose_name='超大单净流入-净额')),
                ('super_big_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='超大单净流入-净占比')),
                ('big_net_inflow', models.FloatField(blank=True, null=True, verbose_name='大单净流入-净额')),
                ('big_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='大单净流入-净占比')),
                ('medium_net_inflow', models.FloatField(blank=True, null=True, verbose_name='中单净流入-净额')),
                ('medium_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='中单净流入-净占比')),
                ('small_net_inflow', models.FloatField(blank=True, null=True, verbose_name='小单净流入-净额')),
                ('small_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='小单净流入-净占比')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '市场资金周报数据',
                'verbose_name_plural': '市场资金周报数据',
                'db_table': 'stock_market_fund_flow',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockSectorFundFlow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trade_date', models.DateField(verbose_name='交易日期')),
                ('sector_name', models.CharField(max_length=50, verbose_name='板块名称')),
                ('sector_type', models.CharField(help_text='行业板块或概念板块', max_length=20, verbose_name='板块类型')),
                ('rank', models.IntegerField(blank=True, null=True, verbose_name='排名')),
                ('net_inflow_rate', models.FloatField(blank=True, null=True, verbose_name='今日涨跌幅(%)')),
                ('net_inflow_amount', models.FloatField(blank=True, null=True, verbose_name='主力净流入-净额')),
                ('main_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='主力净流入-净占比')),
                ('super_big_net_inflow', models.FloatField(blank=True, null=True, verbose_name='超大单净流入-净额')),
                ('super_big_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='超大单净流入-净占比')),
                ('big_net_inflow', models.FloatField(blank=True, null=True, verbose_name='大单净流入-净额')),
                ('big_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='大单净流入-净占比')),
                ('medium_net_inflow', models.FloatField(blank=True, null=True, verbose_name='中单净流入-净额')),
                ('medium_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='中单净流入-净占比')),
                ('small_net_inflow', models.FloatField(blank=True, null=True, verbose_name='小单净流入-净额')),
                ('small_net_inflow_pct', models.FloatField(blank=True, null=True, verbose_name='小单净流入-净占比')),
                ('max_net_inflow_stock', models.CharField(blank=True, max_length=50, null=True, verbose_name='主力净流入最大股')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '行业资金轴幅数据',
                'verbose_name_plural': '行业资金轴幅数据',
                'db_table': 'stock_sector_fund_flow',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockActiveBroker',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('broker_name', models.CharField(db_index=True, max_length=100, verbose_name='营业部名称')),
                ('trade_date', models.DateField(db_index=True, verbose_name='上榜日期')),
                ('buy_stock_count', models.IntegerField(blank=True, null=True, verbose_name='买入个股数')),
                ('sell_stock_count', models.IntegerField(blank=True, null=True, verbose_name='卖出个股数')),
                ('buy_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='买入总金额')),
                ('sell_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='卖出总金额')),
                ('net_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='总买卖净额')),
                ('buy_stocks', models.TextField(blank=True, null=True, verbose_name='买入股票')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '活跃营业部',
                'verbose_name_plural': '活跃营业部',
                'db_table': 'stock_active_broker',
                'ordering': ['-trade_date', 'broker_name'],
            },
        ),
        migrations.CreateModel(
            name='StockComment',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('date', models.DateField(db_index=True, verbose_name='交易日期')),
                ('current_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='最新价')),
                ('change_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='涨跌幅')),
                ('turnover_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='换手率')),
                ('pe_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='市盈率')),
                ('main_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='主力成本')),
                ('institution_participation', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='机构参与度')),
                ('comprehensive_score', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='综合得分')),
                ('rise_rank', models.IntegerField(blank=True, db_index=True, null=True, verbose_name='上升排名')),
                ('current_rank', models.IntegerField(blank=True, db_index=True, null=True, verbose_name='目前排名')),
                ('attention_index', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='关注指数')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '千股千评',
                'verbose_name_plural': '千股千评',
                'db_table': 'stock_comment',
                'ordering': ['-date', 'stock_code'],
            },
        ),
        migrations.CreateModel(
            name='StockMargin',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('trade_date', models.DateField(verbose_name='信用交易日期')),
                ('stock_code', models.CharField(max_length=10, verbose_name='标的证券代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='标的证券简称')),
                ('margin_balance', models.BigIntegerField(blank=True, null=True, verbose_name='融资余额')),
                ('margin_buy', models.BigIntegerField(blank=True, null=True, verbose_name='融资买入额')),
                ('margin_repay', models.BigIntegerField(blank=True, null=True, verbose_name='融资偿还额')),
                ('short_balance', models.BigIntegerField(blank=True, null=True, verbose_name='融券余量')),
                ('short_sell', models.BigIntegerField(blank=True, null=True, verbose_name='融券卖出量')),
                ('short_repay', models.BigIntegerField(blank=True, null=True, verbose_name='融券偿还量')),
                ('short_balance_amount', models.BigIntegerField(blank=True, null=True, verbose_name='融券余额')),
                ('total_balance', models.BigIntegerField(blank=True, null=True, verbose_name='融资融券余额')),
                ('exchange', models.CharField(max_length=10, verbose_name='交易所')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '融资融券明细',
                'verbose_name_plural': '融资融券明细',
                'db_table': 'stock_margin',
                'ordering': ['-trade_date', 'stock_code'],
            },
        ),
        migrations.CreateModel(
            name='StockTopList',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=100, verbose_name='股票名称')),
                ('date', models.DateField(db_index=True, verbose_name='上榜日期')),
                ('reason', models.TextField(blank=True, null=True, verbose_name='上榜理由')),
                ('detail_reason', models.TextField(blank=True, null=True, verbose_name='上榜明细')),
                ('net_buy', models.FloatField(blank=True, null=True, verbose_name='龙虎榜净买额')),
                ('close_price', models.FloatField(blank=True, null=True, verbose_name='收盘价')),
                ('change_ratio', models.FloatField(blank=True, null=True, verbose_name='涨跌幅')),
                ('buy_amount', models.FloatField(blank=True, null=True, verbose_name='龙虎榜买入额')),
                ('sell_amount', models.FloatField(blank=True, null=True, verbose_name='龙虎榜卖出额')),
                ('total_turnover', models.FloatField(blank=True, null=True, verbose_name='龙虎榜成交额')),
                ('market_total_turnover', models.FloatField(blank=True, null=True, verbose_name='市场总成交额')),
                ('net_buy_ratio', models.FloatField(blank=True, null=True, verbose_name='净买额占总成交比')),
                ('turnover_ratio', models.FloatField(blank=True, null=True, verbose_name='成交额占总成交比')),
                ('turnover_rate', models.FloatField(blank=True, null=True, verbose_name='换手率')),
                ('circulation_market_value', models.FloatField(blank=True, null=True, verbose_name='流通市值')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '龙虎榜',
                'verbose_name_plural': '龙虎榜',
                'db_table': 'stock_top_list',
                'ordering': ['-date', 'stock_code'],
            },
        ),
        migrations.DeleteModel(
            name='StockHistory',
        ),
        migrations.DeleteModel(
            name='TechnicalIndicator',
        ),
        migrations.AlterModelTable(
            name='stockboardconceptrelation',
            table='stock_board_concept_stock',
        ),
        migrations.AddIndex(
            model_name='stockactivebroker',
            index=models.Index(fields=['broker_name', 'trade_date'], name='stock_activ_broker__235972_idx'),
        ),
        migrations.AddIndex(
            model_name='stockactivebroker',
            index=models.Index(fields=['trade_date'], name='stock_activ_trade_d_1d63f7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockactivebroker',
            unique_together={('broker_name', 'trade_date')},
        ),
        migrations.AddIndex(
            model_name='stockcomment',
            index=models.Index(fields=['stock_code', 'date'], name='stock_comme_stock_c_51bb72_idx'),
        ),
        migrations.AddIndex(
            model_name='stockcomment',
            index=models.Index(fields=['date'], name='stock_comme_date_4e3d61_idx'),
        ),
        migrations.AddIndex(
            model_name='stockcomment',
            index=models.Index(fields=['rise_rank'], name='stock_comme_rise_ra_1b53a0_idx'),
        ),
        migrations.AddIndex(
            model_name='stockcomment',
            index=models.Index(fields=['current_rank'], name='stock_comme_current_a913a4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockcomment',
            unique_together={('stock_code', 'date')},
        ),
        migrations.AlterUniqueTogether(
            name='stockmargin',
            unique_together={('trade_date', 'stock_code', 'exchange')},
        ),
        migrations.AlterUniqueTogether(
            name='stocktoplist',
            unique_together={('stock_code', 'date')},
        ),
    ]
