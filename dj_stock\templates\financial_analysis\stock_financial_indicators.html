{% extends "base.html" %} {% load humanize %} {% block title %}{{ stock_code }} 财务指标 | 股票数据分析系统{% endblock %} {% block content %}
<div class="container-xl">
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">{{ stock_code }} 财务指标</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        {% if show_all %}
        <a href="{% url 'financial_analysis:stock_financial_indicators' stock_code %}" class="btn btn-primary"> 显示最近4个季度 </a>
        {% else %}
        <a href="{% url 'financial_analysis:stock_financial_indicators' stock_code %}?show_all=1" class="btn btn-primary"> 显示全部 </a>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- 数据状态提示 -->
  {% if data_status_message %}
  <div class="alert alert-{{ data_status }} alert-dismissible" role="alert">
    <div class="d-flex">
      <div>
        <!-- 根据数据状态显示不同图标 -->
        {% if data_status == 'complete' %}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="icon icon-tabler icon-tabler-check"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
          <path d="M5 12l5 5l10 -10"></path>
        </svg>
        {% elif data_status == 'fetching' %}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="icon icon-tabler icon-tabler-loader"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
          <path d="M12 6l0 -3"></path>
          <path d="M16.25 7.75l2.15 -2.15"></path>
          <path d="M18 12l3 0"></path>
          <path d="M16.25 16.25l2.15 2.15"></path>
          <path d="M12 18l0 3"></path>
          <path d="M7.75 16.25l-2.15 2.15"></path>
          <path d="M6 12l-3 0"></path>
          <path d="M7.75 7.75l-2.15 -2.15"></path>
        </svg>
        {% elif data_status == 'error' %}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="icon icon-tabler icon-tabler-alert-triangle"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
          <path d="M12 9v2m0 4v.01"></path>
          <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
        </svg>
        {% else %}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="icon icon-tabler icon-tabler-info-circle"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
          <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
          <path d="M12 9h.01"></path>
          <path d="M11 12h1v4h1"></path>
        </svg>
        {% endif %}
      </div>
      <div class="ms-2">{{ data_status_message }}</div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
  </div>
  {% endif %}

  <!-- 关键指标卡片 -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <div>
        <h3 class="card-title mb-0">关键财务指标</h3>
        {% if latest_update %}
        <small class="text-muted">最后更新时间：{{ latest_update|date:"Y-m-d H:i" }}</small>
        {% endif %}
      </div>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-vcenter card-table table-striped">
          <thead>
            <tr>
              <th>报告期</th>
              <th class="text-end">营业收入(亿)</th>
              <th class="text-end">收入同比(%)</th>
              <th class="text-end">每股收益</th>
              <th class="text-end">每股净资产</th>
              <th class="text-end">ROE(%)</th>
              <th class="text-end">资产负债率(%)</th>
              <th class="text-end">每股经营现金流</th>
              <th class="text-center">操作</th>
            </tr>
          </thead>
          <tbody>
            {% for item in indicators %}
            <tr>
              <td>{{ item.report_date|date:"Y-m-d" }}</td>
              <td class="text-end">{{ item.total_revenue_display|floatformat:2|intcomma }}</td>
              <td class="text-end {% if item.total_revenue_growth > 0 %}text-danger{% elif item.total_revenue_growth < 0 %}text-success{% endif %}">
                {{ item.total_revenue_growth|floatformat:2 }}%
              </td>
              <td class="text-end">{{ item.eps|floatformat:3 }}</td>
              <td class="text-end">{{ item.nav|floatformat:2 }}</td>
              <td class="text-end {% if item.roe > 0 %}text-danger{% elif item.roe < 0 %}text-success{% endif %}">{{ item.roe|floatformat:2 }}%</td>
              <td class="text-end">{{ item.debt_asset_ratio|floatformat:2 }}%</td>
              <td class="text-end">{{ item.ocf_per_share|floatformat:2 }}</td>
              <td class="text-center">
                <button class="btn btn-sm btn-outline-primary" onclick="toggleDetails('details-{{ forloop.counter }}')">详情</button>
              </td>
            </tr>
            <!-- 详细信息（默认隐藏） -->
            <tr id="details-{{ forloop.counter }}" style="display: none">
              <td colspan="9">
                <div class="p-3">
                  <div class="row g-3">
                    <div class="col-md-6">
                      <h4>盈利能力指标</h4>
                      <table class="table table-sm">
                        <tr>
                          <td>扣非净利润(亿)：</td>
                          <td class="text-end">{{ item.deducted_net_profit_display|floatformat:2|intcomma }}</td>
                          <td>扣非净利润同比(%)：</td>
                          <td
                            class="text-end {% if item.deducted_net_profit_growth > 0 %}text-danger{% elif item.deducted_net_profit_growth < 0 %}text-success{% endif %}"
                          >
                            {{ item.deducted_net_profit_growth|floatformat:2 }}%
                          </td>
                        </tr>
                        <tr>
                          <td>净利率(%)：</td>
                          <td class="text-end">{{ item.net_profit_margin|floatformat:2 }}%</td>
                          <td>每股净资产：</td>
                          <td class="text-end">{{ item.nav|floatformat:2 }}</td>
                        </tr>
                        <tr>
                          <td>每股资本公积：</td>
                          <td class="text-end">{{ item.capital_reserve|floatformat:2 }}</td>
                          <td>每股未分配利润：</td>
                          <td class="text-end">{{ item.undistributed_profit|floatformat:2 }}</td>
                        </tr>
                        <tr>
                          <td>每股经营现金流：</td>
                          <td class="text-end">{{ item.ocf_per_share|floatformat:2 }}</td>
                          <td>稀释净资产收益率(%)：</td>
                          <td class="text-end {% if item.diluted_roe > 0 %}text-danger{% elif item.diluted_roe < 0 %}text-success{% endif %}">
                            {{ item.diluted_roe|floatformat:2 }}%
                          </td>
                        </tr>
                      </table>
                    </div>
                    <div class="col-md-6">
                      <h4>运营效率指标</h4>
                      <table class="table table-sm">
                        <tr>
                          <td>营业周期(天)：</td>
                          <td class="text-end">{{ item.operating_cycle|floatformat:1 }}</td>
                          <td>存货周转天数(天)：</td>
                          <td class="text-end">{{ item.inventory_days|floatformat:1 }}</td>
                        </tr>
                        <tr>
                          <td>应收账款周转天数(天)：</td>
                          <td class="text-end">{{ item.receivable_days|floatformat:1 }}</td>
                          <td>存货周转率(次)：</td>
                          <td class="text-end">{{ item.inventory_turnover|floatformat:2 }}</td>
                        </tr>
                      </table>
                    </div>
                    <div class="col-md-6">
                      <h4>偿债能力指标</h4>
                      <table class="table table-sm">
                        <tr>
                          <td>流动比率(倍)：</td>
                          <td class="text-end">{{ item.current_ratio|floatformat:2 }}</td>
                          <td>速动比率(倍)：</td>
                          <td class="text-end">{{ item.quick_ratio|floatformat:2 }}</td>
                        </tr>
                        <tr>
                          <td>现金比率(%)：</td>
                          <td class="text-end">{{ item.conservative_quick_ratio|floatformat:2 }}%</td>
                          <td>资产负债率(%)：</td>
                          <td class="text-end">{{ item.debt_asset_ratio|floatformat:2 }}%</td>
                        </tr>
                        <tr>
                          <td>产权比率(%)：</td>
                          <td class="text-end">{{ item.equity_ratio|floatformat:2 }}%</td>
                          <td></td>
                          <td></td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="9" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon icon-tabler icon-tabler-chart-line"
                      width="40"
                      height="40"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M4 19l16 0"></path>
                      <path d="M4 15l4 -6l4 2l4 -5l4 4"></path>
                    </svg>
                  </div>
                  <p class="empty-title">暂无财务指标数据</p>
                  <p class="empty-subtitle text-muted">该股票暂时没有财务指标数据</p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  function toggleDetails(detailsId) {
    const detailsRow = document.getElementById(detailsId)
    if (detailsRow.style.display === 'none') {
      detailsRow.style.display = 'table-row'
    } else {
      detailsRow.style.display = 'none'
    }
  }
</script>

<style>
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }
  .empty {
    padding: 2rem 0;
    text-align: center;
  }
  .empty-title {
    font-size: 1rem;
    line-height: 1.4285714286;
    font-weight: 600;
  }
  .empty-subtitle {
    font-size: 0.875rem;
    line-height: 1.4285714286;
    color: #626976;
  }
</style>
{% endblock %}
