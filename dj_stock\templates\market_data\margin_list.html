{% extends "base.html" %} {% block title %}融资融券数据 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">市场数据</div>
        <h2 class="page-title">融资融券数据</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:index' %}" class="btn btn-outline-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="icon"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M5 12l-2 0l9 -9l9 9l-2 0" />
              <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
              <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />
            </svg>
            返回首页
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">融资融券数据列表</h3>
            <div class="card-actions">
              <form method="get" action="{% url 'market_data:margin_list' %}" class="d-flex gap-2">
                <input type="date" class="form-control" name="date" value="{{ date }}" placeholder="选择日期" />
                <input type="text" class="form-control" name="stock_code" value="{{ stock_code }}" placeholder="股票代码" />
                <input type="text" class="form-control" name="stock_name" value="{{ stock_name }}" placeholder="股票名称" />
                <button type="submit" class="btn btn-primary">搜索</button>
              </form>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter card-table">
                <thead>
                  <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>日期</th>
                    <th>融资余额</th>
                    <th>融券余额</th>
                    <th>融资融券余额</th>
                    <th>融资买入额</th>
                    <th>融券卖出量</th>
                    <th>融资融券余额/流通市值</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in page_obj %}
                  <tr>
                    <td>{{ item.stock_code }}</td>
                    <td>{{ item.stock_name }}</td>
                    <td>{{ item.date|date:"Y-m-d" }}</td>
                    <td>{{ item.margin_balance|floatformat:2 }}</td>
                    <td>{{ item.short_balance|floatformat:2 }}</td>
                    <td>{{ item.total_balance|floatformat:2 }}</td>
                    <td>{{ item.margin_buy|floatformat:2 }}</td>
                    <td>{{ item.short_sell|floatformat:2 }}</td>
                    <td>{{ item.balance_ratio|floatformat:2 }}%</td>
                    <td>
                      <a href="{% url 'market_data:margin_detail' item.pk %}" class="btn btn-info btn-sm">详情</a>
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="10" class="text-center">暂无融资融券数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% include "includes/pagination.html" with page_obj=page_obj %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
