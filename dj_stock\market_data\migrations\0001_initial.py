# Generated by Django 5.1.7 on 2025-03-29 14:03

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='HKStockConnect',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=100, verbose_name='股票名称')),
                ('date', models.DateField(verbose_name='统计日期')),
                ('direction', models.CharField(help_text='沪股通/深股通', max_length=20, verbose_name='交易方向')),
                ('status', models.Char<PERSON>ield(help_text='纳入/剔除', max_length=20, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '港股通成份股',
                'verbose_name_plural': '港股通成份股',
                'db_table': 'hk_stock_connect',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockBasic',
            fields=[
                ('stock_code', models.CharField(max_length=10, primary_key=True, serialize=False, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('industry', models.CharField(blank=True, max_length=50, null=True, verbose_name='所属行业')),
                ('market', models.CharField(blank=True, max_length=20, null=True, verbose_name='市场类型')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '股票基本信息',
                'verbose_name_plural': '股票基本信息',
                'db_table': 'stock_basic',
                'ordering': ['stock_code'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockBoardConcept',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('board_code', models.CharField(db_index=True, max_length=20, verbose_name='板块代码')),
                ('board_name', models.CharField(max_length=100, verbose_name='板块名称')),
                ('date', models.DateField(db_index=True, verbose_name='交易日期')),
                ('rank', models.IntegerField(blank=True, null=True, verbose_name='排名')),
                ('latest_price', models.FloatField(blank=True, null=True, verbose_name='最新价')),
                ('change_amount', models.FloatField(blank=True, null=True, verbose_name='涨跌额')),
                ('change_percent', models.FloatField(blank=True, null=True, verbose_name='涨跌幅')),
                ('total_market_value', models.FloatField(blank=True, null=True, verbose_name='总市值(亿)')),
                ('turnover_rate', models.FloatField(blank=True, null=True, verbose_name='换手率')),
                ('up_count', models.IntegerField(blank=True, null=True, verbose_name='上涨家数')),
                ('down_count', models.IntegerField(blank=True, null=True, verbose_name='下跌家数')),
                ('leading_stock', models.CharField(blank=True, max_length=50, null=True, verbose_name='领涨股票')),
                ('leading_stock_change_percent', models.FloatField(blank=True, null=True, verbose_name='领涨股票涨跌幅')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '概念板块',
                'verbose_name_plural': '概念板块',
                'db_table': 'stock_board_concept',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockBoardConceptRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('board_code', models.CharField(max_length=10, verbose_name='板块代码')),
                ('board_name', models.CharField(max_length=50, verbose_name='板块名称')),
                ('date', models.DateField(verbose_name='日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '概念板块成分股关系',
                'verbose_name_plural': '概念板块成分股关系',
                'db_table': 'stock_board_concept_relation',
                'ordering': ['board_code', 'stock_code'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockDailyQuote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('trade_date', models.DateField(verbose_name='交易日期')),
                ('open_price', models.FloatField(blank=True, null=True, verbose_name='开盘价')),
                ('close_price', models.FloatField(blank=True, null=True, verbose_name='收盘价')),
                ('high_price', models.FloatField(blank=True, null=True, verbose_name='最高价')),
                ('low_price', models.FloatField(blank=True, null=True, verbose_name='最低价')),
                ('prev_close', models.FloatField(blank=True, null=True, verbose_name='昨日收盘价')),
                ('volume', models.FloatField(blank=True, null=True, verbose_name='成交量')),
                ('amount', models.FloatField(blank=True, null=True, verbose_name='成交额')),
                ('turnover_rate', models.FloatField(blank=True, null=True, verbose_name='换手率')),
                ('volume_ratio', models.FloatField(blank=True, null=True, verbose_name='量比')),
                ('change_amount', models.FloatField(blank=True, null=True, verbose_name='涨跌额')),
                ('change_percent', models.FloatField(blank=True, null=True, verbose_name='涨跌幅')),
                ('amplitude', models.FloatField(blank=True, null=True, verbose_name='振幅')),
                ('pe_ratio', models.FloatField(blank=True, null=True, verbose_name='市盈率(动态)')),
                ('pb_ratio', models.FloatField(blank=True, null=True, verbose_name='市净率')),
                ('total_value', models.FloatField(blank=True, null=True, verbose_name='总市值')),
                ('float_value', models.FloatField(blank=True, null=True, verbose_name='流通市值')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '股票每日行情',
                'verbose_name_plural': '股票每日行情',
                'db_table': 'stock_daily_quote',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('trade_date', models.DateField(verbose_name='日期')),
                ('open_price', models.FloatField(blank=True, null=True, verbose_name='开盘价')),
                ('close_price', models.FloatField(blank=True, null=True, verbose_name='收盘价')),
                ('high_price', models.FloatField(blank=True, null=True, verbose_name='最高价')),
                ('low_price', models.FloatField(blank=True, null=True, verbose_name='最低价')),
                ('volume', models.FloatField(blank=True, null=True, verbose_name='成交量')),
                ('amount', models.FloatField(blank=True, null=True, verbose_name='成交额')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '股票历史数据',
                'verbose_name_plural': '股票历史数据',
                'db_table': 'stock_daily_quote',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockIndustryBoard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('board_code', models.CharField(db_index=True, max_length=20, verbose_name='板块代码')),
                ('board_name', models.CharField(max_length=100, verbose_name='板块名称')),
                ('date', models.DateField(db_index=True, verbose_name='交易日期')),
                ('rank', models.IntegerField(blank=True, null=True, verbose_name='排名')),
                ('latest_price', models.FloatField(blank=True, null=True, verbose_name='最新价')),
                ('change_amount', models.FloatField(blank=True, null=True, verbose_name='涨跌额')),
                ('change_percent', models.FloatField(blank=True, null=True, verbose_name='涨跌幅')),
                ('total_market_value', models.FloatField(blank=True, null=True, verbose_name='总市值(亿)')),
                ('turnover_rate', models.FloatField(blank=True, null=True, verbose_name='换手率')),
                ('up_count', models.IntegerField(blank=True, null=True, verbose_name='上涨家数')),
                ('down_count', models.IntegerField(blank=True, null=True, verbose_name='下跌家数')),
                ('leading_stock', models.CharField(blank=True, max_length=50, null=True, verbose_name='领涨股票')),
                ('leading_stock_change_percent', models.FloatField(blank=True, null=True, verbose_name='领涨股票涨跌幅')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '行业板块',
                'verbose_name_plural': '行业板块',
                'db_table': 'stock_board_industry',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockIndustryBoardRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('board_code', models.CharField(db_index=True, max_length=20, verbose_name='板块代码')),
                ('board_name', models.CharField(max_length=100, verbose_name='板块名称')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=100, verbose_name='股票名称')),
                ('date', models.DateField(blank=True, null=True, verbose_name='交易日期')),
                ('rank', models.IntegerField(blank=True, null=True, verbose_name='序号')),
                ('latest_price', models.FloatField(blank=True, null=True, verbose_name='最新价')),
                ('change_percent', models.FloatField(blank=True, null=True, verbose_name='涨跌幅')),
                ('change_amount', models.FloatField(blank=True, null=True, verbose_name='涨跌额')),
                ('volume', models.FloatField(blank=True, null=True, verbose_name='成交量')),
                ('amount', models.FloatField(blank=True, null=True, verbose_name='成交额')),
                ('amplitude', models.FloatField(blank=True, null=True, verbose_name='振幅')),
                ('high', models.FloatField(blank=True, null=True, verbose_name='最高')),
                ('low', models.FloatField(blank=True, null=True, verbose_name='最低')),
                ('open', models.FloatField(blank=True, null=True, verbose_name='今开')),
                ('pre_close', models.FloatField(blank=True, null=True, verbose_name='昨收')),
                ('turnover_rate', models.FloatField(blank=True, null=True, verbose_name='换手率')),
                ('pe_ratio', models.FloatField(blank=True, null=True, verbose_name='市盈率-动态')),
                ('pb_ratio', models.FloatField(blank=True, null=True, verbose_name='市净率')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '行业板块成分股',
                'verbose_name_plural': '行业板块成分股',
                'db_table': 'stock_board_industry_stock',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockIPO',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, unique=True, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('listing_date', models.DateField(db_index=True, verbose_name='上市日期')),
                ('issue_price', models.FloatField(verbose_name='发行价')),
                ('latest_price', models.FloatField(blank=True, null=True, verbose_name='最新价')),
                ('first_day_open', models.FloatField(blank=True, null=True, verbose_name='首日开盘价')),
                ('first_day_close', models.FloatField(blank=True, null=True, verbose_name='首日收盘价')),
                ('first_day_high', models.FloatField(blank=True, null=True, verbose_name='首日最高价')),
                ('first_day_low', models.FloatField(blank=True, null=True, verbose_name='首日最低价')),
                ('first_day_change_pct', models.FloatField(blank=True, null=True, verbose_name='首日涨跌幅')),
                ('break_issue', models.CharField(blank=True, max_length=10, null=True, verbose_name='是否破发')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '新股上市',
                'verbose_name_plural': '新股上市',
                'db_table': 'stock_ipo',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockMarketIndex',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('index_code', models.CharField(max_length=10, verbose_name='指数代码')),
                ('index_name', models.CharField(max_length=50, verbose_name='指数名称')),
                ('trade_date', models.DateField(verbose_name='交易日期')),
                ('open_price', models.FloatField(blank=True, null=True, verbose_name='开盘价')),
                ('high_price', models.FloatField(blank=True, null=True, verbose_name='最高价')),
                ('low_price', models.FloatField(blank=True, null=True, verbose_name='最低价')),
                ('close_price', models.FloatField(blank=True, null=True, verbose_name='收盘价')),
                ('pre_close', models.FloatField(blank=True, null=True, verbose_name='昨收价')),
                ('volume', models.FloatField(blank=True, null=True, verbose_name='成交量')),
                ('amount', models.FloatField(blank=True, null=True, verbose_name='成交额')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '主板指数',
                'verbose_name_plural': '主板指数',
                'db_table': 'stock_market_index',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockNorthBoardRank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('board_name', models.CharField(max_length=50, verbose_name='板块名称')),
                ('board_type', models.CharField(max_length=20, verbose_name='板块类型')),
                ('date', models.DateField(verbose_name='交易日期')),
                ('holding_market_value', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='持股市值')),
                ('increase_market_value', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='增持市值')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '北向资金板块排名',
                'verbose_name_plural': '北向资金板块排名',
                'db_table': 'stock_north_board_rank',
                'ordering': ['-date', '-holding_market_value'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockNorthHolding',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('date', models.DateField(verbose_name='交易日期')),
                ('holding_shares', models.IntegerField(blank=True, null=True, verbose_name='持股数量')),
                ('holding_value', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='持股市值')),
                ('holding_ratio', models.DecimalField(blank=True, decimal_places=4, max_digits=10, null=True, verbose_name='持股占比')),
                ('value_change_1d', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='1日市值变化')),
                ('value_change_5d', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='5日市值变化')),
                ('value_change_10d', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='10日市值变化')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '北向资金持股',
                'verbose_name_plural': '北向资金持股',
                'db_table': 'stock_north_holding',
                'ordering': ['-date', '-holding_value'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockRiskWarning',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=100, verbose_name='股票名称')),
                ('warning_reason', models.TextField(verbose_name='风险警示原因')),
                ('warning_date', models.DateField(verbose_name='风险警示日期')),
                ('status', models.CharField(choices=[('纳入', '纳入'), ('剔除', '剔除')], default='纳入', max_length=20, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '风险警示股票',
                'verbose_name_plural': '风险警示股票',
                'db_table': 'stock_risk_warning',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TechnicalIndicator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=100, verbose_name='股票名称')),
                ('date', models.DateField(verbose_name='统计日期')),
                ('indicator_type', models.CharField(max_length=50, verbose_name='指标类型')),
                ('indicator_value', models.FloatField(blank=True, null=True, verbose_name='指标值')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '技术指标',
                'verbose_name_plural': '技术指标',
                'db_table': 'technical_indicator',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockDataStatus',
            fields=[
                ('stock_code', models.CharField(max_length=10, primary_key=True, serialize=False, verbose_name='股票代码')),
                ('last_update', models.DateTimeField(auto_now=True, verbose_name='最后更新时间')),
                ('is_complete', models.BooleanField(default=False, verbose_name='数据是否完整')),
                ('date_from', models.DateField(blank=True, null=True, verbose_name='数据起始日期')),
                ('date_to', models.DateField(blank=True, null=True, verbose_name='数据结束日期')),
                ('record_count', models.IntegerField(default=0, verbose_name='记录数量')),
                ('status', models.CharField(choices=[('never_fetched', '从未获取'), ('fetching', '获取中'), ('partial', '部分数据'), ('complete', '完整数据'), ('error', '获取错误')], default='never_fetched', max_length=20, verbose_name='数据状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
            ],
            options={
                'verbose_name': '股票数据状态',
                'verbose_name_plural': '股票数据状态',
                'db_table': 'stock_data_status',
            },
        ),
    ]
