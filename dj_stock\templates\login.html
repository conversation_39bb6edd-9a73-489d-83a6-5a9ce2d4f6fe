{% extends "login_base.html" %} {% block content %}
<div class="login-card">
  <div class="login-header">
    <a href="/" class="login-brand">
      <i class="ti ti-chart-line"></i>
      <span>股票数据分析</span>
    </a>
  </div>
  <div class="login-form">
    {% if form.errors %}
    <div class="alert alert-danger">
      <i class="ti ti-alert-circle"></i>
      用户名或密码不正确，请重新输入
    </div>
    {% endif %}

    <form method="post" action="{% url 'login' %}">
      {% csrf_token %}
      <div class="mb-3">
        <label class="form-label">
          <i class="ti ti-user me-1"></i>
          用户名
        </label>
        <input type="text" name="username" class="form-control" placeholder="请输入用户名" required autofocus />
      </div>

      <div class="mb-3">
        <label class="form-label">
          <i class="ti ti-lock me-1"></i>
          密码
        </label>
        <input type="password" name="password" class="form-control" placeholder="请输入密码" required />
      </div>

      <div class="mb-4">
        <label class="form-check">
          <input type="checkbox" class="form-check-input" name="remember" />
          <span class="form-check-label">记住我</span>
        </label>
      </div>

      <button type="submit" class="btn-login">
        <i class="ti ti-login me-1"></i>
        登录
      </button>

      {% if next %}
      <input type="hidden" name="next" value="{{ next }}" />
      {% endif %}
    </form>
  </div>
</div>
{% endblock %}
