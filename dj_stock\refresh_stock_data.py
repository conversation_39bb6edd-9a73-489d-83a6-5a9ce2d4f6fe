#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票数据刷新工具

该脚本用于批量刷新股票数据，通过访问股票详情页面来触发数据更新。

功能：
1. 获取指定范围的股票代码
2. 循环访问每个股票的详情页面
3. 触发股票历史数据和财务指标的更新
4. 显示进度条和刷新状态

使用方法：
1. 修改脚本底部的 start 和 end 参数
2. 确保Django服务器在 http://127.0.0.1:8000 运行
3. 运行脚本：python refresh_stock_data.py

注意：
- 请求间有随机延迟，避免服务器压力过大
- 建议分批次运行，每次不超过500只股票
"""

import os
import sys
import time
import random
import requests
import django
from tqdm import tqdm

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "dj_stock.settings")
django.setup()

# 导入Django模型
from market_data.models import StockBasic


def refresh_stock_data(start=0, end=100):
    """
    刷新股票数据

    Args:
        start (int): 开始的股票索引（从0开始）
        end (int): 结束的股票索引
    """
    # 获取前N个股票代码
    stocks = StockBasic.objects.all().order_by("stock_code")[start:end]

    # 显示进度条
    with tqdm(total=len(stocks), desc="刷新股票数据") as pbar:
        for stock in stocks:
            stock_code = stock.stock_code
            stock_name = stock.stock_name

            # 构建URL
            url = f"http://127.0.0.1:8000/stocks/{stock_code}/"

            try:
                # 发送请求
                response = requests.get(url, timeout=30)

                # 检查响应状态
                if response.status_code == 200:
                    pbar.write(f"成功刷新 {stock_name}({stock_code}) 的数据")
                else:
                    pbar.write(
                        f"刷新 {stock_name}({stock_code}) 失败，状态码: {response.status_code}"
                    )

            except requests.RequestException as e:
                pbar.write(f"请求 {stock_name}({stock_code}) 时出错: {str(e)}")

            # 更新进度条
            pbar.update(1)

            # 随机延迟，避免请求过于频繁
            time.sleep(random.uniform(8, 15))


if __name__ == "__main__":
    # 解析命令行参数
    start = 5001  # 开始的股票索引，从0开始计数，表示从第一个股票开始刷新。
    end = 5500  # 结束的股票索引，表示刷新到第100个股票。
    print(f"开始刷新第 {start} -- {end} 个股票的数据...")
    refresh_stock_data(start, end)
    print("刷新完成！")
