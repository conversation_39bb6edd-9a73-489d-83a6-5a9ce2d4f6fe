from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage

def get_pagination(request, queryset, per_page=20):
    """通用分页处理函数"""
    paginator = Paginator(queryset, per_page)
    page = request.GET.get('page')
    try:
        paginated_data = paginator.page(page)
    except PageNotAnInteger:
        paginated_data = paginator.page(1)
    except EmptyPage:
        paginated_data = paginator.page(paginator.num_pages)
    return paginated_data 