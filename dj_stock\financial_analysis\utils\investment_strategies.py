# -*- coding: utf-8 -*-
"""
投资策略模板
提供预设的投资选股策略，帮助用户快速筛选股票
"""

class InvestmentStrategies:
    """投资策略类"""
    
    @staticmethod
    def get_value_investment_strategy():
        """价值投资策略 - 巴菲特式选股"""
        return {
            'name': '价值投资策略',
            'description': '寻找低估值、高ROE、稳定盈利的优质公司',
            'filters': {
                'pe_min': 0,
                'pe_max': 15,
                'pb_min': 0,
                'pb_max': 2,
                'roe_min': 15,
                'debt_ratio_max': 50,
                'revenue_growth_min': 5,
                'net_profit_growth_min': 5,
                'continuous_roe_years': 3,
            },
            'sort_by': 'roe',
            'sort_order': 'desc',
            'color': 'success',
            'icon': 'ti-shield-check'
        }
    
    @staticmethod
    def get_growth_investment_strategy():
        """成长投资策略 - 高成长股筛选"""
        return {
            'name': '成长投资策略',
            'description': '寻找高成长、高ROE、业绩快速增长的公司',
            'filters': {
                'roe_min': 20,
                'revenue_growth_min': 25,
                'net_profit_growth_min': 30,
                'pe_min': 0,
                'pe_max': 50,
                'debt_ratio_max': 60,
            },
            'sort_by': 'net_profit_growth',
            'sort_order': 'desc',
            'color': 'primary',
            'icon': 'ti-trending-up'
        }
    
    @staticmethod
    def get_quality_investment_strategy():
        """质量投资策略 - 高质量公司筛选"""
        return {
            'name': '质量投资策略',
            'description': '寻找财务健康、盈利稳定、护城河深厚的公司',
            'filters': {
                'roe_min': 15,
                'gross_margin_min': 40,
                'debt_ratio_max': 30,
                'revenue_growth_min': 10,
                'net_profit_growth_min': 10,
                'continuous_roe_years': 5,
            },
            'sort_by': 'gross_margin',
            'sort_order': 'desc',
            'color': 'warning',
            'icon': 'ti-award'
        }
    
    @staticmethod
    def get_dividend_investment_strategy():
        """股息投资策略 - 高股息率股票筛选"""
        return {
            'name': '股息投资策略',
            'description': '寻找高股息率、分红稳定的价值股',
            'filters': {
                'pe_min': 0,
                'pe_max': 20,
                'roe_min': 10,
                'debt_ratio_max': 60,
                'revenue_growth_min': 0,
                'net_profit_growth_min': 0,
            },
            'sort_by': 'roe',
            'sort_order': 'desc',
            'color': 'info',
            'icon': 'ti-coins'
        }
    
    @staticmethod
    def get_turnaround_investment_strategy():
        """困境反转策略 - 寻找反转机会"""
        return {
            'name': '困境反转策略',
            'description': '寻找基本面改善、估值较低的反转机会',
            'filters': {
                'pe_min': 0,
                'pe_max': 25,
                'pb_min': 0,
                'pb_max': 3,
                'debt_ratio_max': 70,
                'revenue_growth_min': -20,
                'net_profit_growth_min': -50,
            },
            'sort_by': 'pb',
            'sort_order': 'asc',
            'color': 'danger',
            'icon': 'ti-refresh'
        }
    
    @staticmethod
    def get_small_cap_growth_strategy():
        """小盘成长策略 - 小市值高成长股"""
        return {
            'name': '小盘成长策略',
            'description': '寻找市值较小但成长性突出的公司',
            'filters': {
                'market_cap_min': 10,
                'market_cap_max': 100,
                'roe_min': 20,
                'revenue_growth_min': 30,
                'net_profit_growth_min': 35,
                'debt_ratio_max': 50,
            },
            'sort_by': 'revenue_growth',
            'sort_order': 'desc',
            'color': 'secondary',
            'icon': 'ti-rocket'
        }
    
    @staticmethod
    def get_all_strategies():
        """获取所有策略"""
        return [
            InvestmentStrategies.get_value_investment_strategy(),
            InvestmentStrategies.get_growth_investment_strategy(),
            InvestmentStrategies.get_quality_investment_strategy(),
            InvestmentStrategies.get_dividend_investment_strategy(),
            InvestmentStrategies.get_turnaround_investment_strategy(),
            InvestmentStrategies.get_small_cap_growth_strategy(),
        ]
    
    @staticmethod
    def get_strategy_by_name(strategy_name):
        """根据策略名称获取策略"""
        strategies = InvestmentStrategies.get_all_strategies()
        for strategy in strategies:
            if strategy['name'] == strategy_name:
                return strategy
        return None


class MarketConditionStrategies:
    """市场环境策略"""
    
    @staticmethod
    def get_bull_market_strategy():
        """牛市策略 - 成长股为主"""
        return {
            'name': '牛市成长策略',
            'description': '牛市环境下重点关注高成长、高弹性股票',
            'filters': {
                'roe_min': 15,
                'revenue_growth_min': 20,
                'net_profit_growth_min': 25,
                'pe_max': 60,
            },
            'sort_by': 'net_profit_growth',
            'sort_order': 'desc',
        }
    
    @staticmethod
    def get_bear_market_strategy():
        """熊市策略 - 防御性价值股"""
        return {
            'name': '熊市防御策略',
            'description': '熊市环境下重点关注低估值、高股息的防御性股票',
            'filters': {
                'pe_max': 12,
                'pb_max': 1.5,
                'roe_min': 12,
                'debt_ratio_max': 40,
            },
            'sort_by': 'pe',
            'sort_order': 'asc',
        }
    
    @staticmethod
    def get_sideways_market_strategy():
        """震荡市策略 - 均衡配置"""
        return {
            'name': '震荡市均衡策略',
            'description': '震荡市环境下均衡配置价值股和成长股',
            'filters': {
                'pe_max': 25,
                'roe_min': 12,
                'revenue_growth_min': 10,
                'debt_ratio_max': 60,
            },
            'sort_by': 'roe',
            'sort_order': 'desc',
        }


class SectorRotationStrategies:
    """板块轮动策略"""
    
    @staticmethod
    def get_cyclical_strategy():
        """周期性行业策略"""
        return {
            'name': '周期性行业策略',
            'description': '关注周期性行业的投资机会',
            'target_industries': ['钢铁', '有色金属', '煤炭', '化工', '建筑材料'],
            'filters': {
                'pe_max': 15,
                'pb_max': 2,
                'roe_min': 10,
            }
        }
    
    @staticmethod
    def get_defensive_strategy():
        """防御性行业策略"""
        return {
            'name': '防御性行业策略',
            'description': '关注防御性行业的稳健投资机会',
            'target_industries': ['食品饮料', '医药生物', '公用事业', '银行'],
            'filters': {
                'roe_min': 12,
                'debt_ratio_max': 50,
                'revenue_growth_min': 5,
            }
        }
    
    @staticmethod
    def get_technology_strategy():
        """科技成长策略"""
        return {
            'name': '科技成长策略',
            'description': '关注科技行业的成长投资机会',
            'target_industries': ['计算机', '电子', '通信', '传媒'],
            'filters': {
                'roe_min': 15,
                'revenue_growth_min': 20,
                'net_profit_growth_min': 25,
            }
        }
