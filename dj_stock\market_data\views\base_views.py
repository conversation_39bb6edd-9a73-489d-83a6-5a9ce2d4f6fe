# -*- coding: utf-8 -*-
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Count, Sum, Avg
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.db.models.functions import TruncDate, Abs, TruncDay, Cast
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.http import require_GET
from django.urls import reverse
from django.core.serializers.json import DjangoJSONEncoder

from urllib.parse import urlencode
import re
import json
import math
import logging
from datetime import datetime, timedelta
from decimal import Decimal

# 导入所有模型，以便其他视图文件可以从 base_views 导入
from market_data.models import (
    StockBasic,
    StockRiskWarning,
    StockIPO,
    StockBoardConcept,
    StockIndustryBoard,
    StockIndustryBoardRelation,
    StockBoardConceptRelation,
    StockDailyQuote,
    StockMarketIndex,
    HKStockConnect,
    StockTopList,
    StockMargin,
    StockComment,
    StockActiveBroker,
    StockDataStatus,
    StockLimitList,
    StockFundFlow,
    StockMarketFundFlow,
    StockSectorFundFlow,
)
from market_data.utils.stock_fetcher import StockDataFetcher
from financial_analysis.models import StockFinancialIndicator, StockDividend
from financial_analysis.utils.financial_fetcher import FinancialDataFetcher

logger = logging.getLogger(__name__)


# 通用函数可以放在这里
def get_pagination(queryset, page, per_page=20):
    """通用分页处理函数"""
    paginator = Paginator(queryset, per_page)
    try:
        paginated_data = paginator.page(page)
    except PageNotAnInteger:
        paginated_data = paginator.page(1)
    except EmptyPage:
        paginated_data = paginator.page(paginator.num_pages)
    return paginated_data


def get_latest_trade_date():
    """
    获取最新交易日期
    """
    return (
        StockDailyQuote.objects.order_by("-trade_date")
        .values_list("trade_date", flat=True)
        .first()
    ) 