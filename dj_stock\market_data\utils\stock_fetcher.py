# -*- coding: utf-8 -*-
"""
股票历史数据采集工具模块
使用akshare库获取股票历史行情数据
"""
import datetime
import logging
import time
from typing import Tuple, Dict, Any, List, Optional

import akshare as ak
import pandas as pd
from django.db import transaction
from django.utils import timezone

from market_data.models import StockDataStatus, StockDailyQuote

logger = logging.getLogger(__name__)


class StockDataFetcher:
    """股票历史数据采集器"""

    def __init__(self, retries: int = 3, sleep_time: int = 1):
        """
        初始化股票数据采集器
        
        Args:
            retries: 重试次数
            sleep_time: 重试间隔时间(秒)
        """
        self.retries = retries
        self.sleep_time = sleep_time
    
    def _get_stock_history(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        从akshare获取股票历史数据
        
        Args:
            stock_code: 股票代码，如 600000
            start_date: 开始日期，如 20200101
            end_date: 结束日期，如 20201231
        
        Returns:
            包含股票历史数据的DataFrame
        """
        # 尝试不同格式的股票代码
        code_formats = []
        
        # 确定股票所属市场，并添加不同格式的代码到尝试列表
        if stock_code.startswith(('0', '3')):
            code_formats.append(f"sz{stock_code}")  # 深市格式
            code_formats.append(stock_code)  # 直接使用代码
        elif stock_code.startswith(('6', '9')):
            code_formats.append(f"sh{stock_code}")  # 沪市格式
            code_formats.append(stock_code)  # 直接使用代码
        elif stock_code.startswith('4'):
            code_formats.append(f"bj{stock_code}")  # 北交所格式
            code_formats.append(stock_code)  # 直接使用代码
        else:
            # 如果不符合已知格式，仍然尝试直接使用
            code_formats.append(stock_code)
            # 也尝试添加市场前缀
            code_formats.extend([f"sz{stock_code}", f"sh{stock_code}", f"bj{stock_code}"])
        
        # 为每个代码格式应用重试机制
        last_error = None
        
        for code_format in code_formats:
            for attempt in range(self.retries):
                try:
                    logger.info(f"尝试获取股票 {code_format} 的历史数据 (格式 {code_formats.index(code_format)+1}/{len(code_formats)}, 尝试 {attempt+1}/{self.retries})")
                    # 使用akshare获取股票历史行情数据
                    df = ak.stock_zh_a_hist(symbol=code_format, period="daily", 
                                           start_date=start_date, end_date=end_date, 
                                           adjust="qfq")
                    
                    if not df.empty:
                        logger.info(f"成功获取股票 {code_format} 的历史数据，共 {len(df)} 条记录")
                        return df
                    
                    # 如果数据为空，尝试下一种格式
                    logger.warning(f"获取股票 {code_format} 数据返回空结果")
                    break
                    
                except Exception as e:
                    logger.warning(f"获取股票 {code_format} 数据失败 (尝试 {attempt+1}/{self.retries}): {str(e)}")
                    last_error = e
                    if attempt < self.retries - 1:
                        time.sleep(self.sleep_time)
        
        # 如果所有尝试都失败，抛出最后一个错误
        if last_error:
            logger.error(f"所有格式的股票代码 {stock_code} 数据获取均失败: {str(last_error)}")
            raise last_error
        else:
            # 如果没有错误但也没有数据，返回空DataFrame
            logger.error(f"所有格式的股票代码 {stock_code} 数据获取均返回空结果")
            return pd.DataFrame()
    
    def _process_dataframe(self, df: pd.DataFrame, stock_code: str) -> List[Dict[str, Any]]:
        """
        处理DataFrame数据为可存储格式
        
        Args:
            df: 从akshare获取的股票数据DataFrame
            stock_code: 股票代码
        
        Returns:
            处理后的数据列表，可用于批量创建
        """
        records = []
        
        # 重命名列以匹配模型字段
        column_mapping = {
            '日期': 'trade_date',
            '开盘': 'open_price',
            '收盘': 'close_price',
            '最高': 'high_price',
            '最低': 'low_price',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'change_percent',
            '涨跌额': 'change_amount',
            '换手率': 'turnover_rate',
        }
        
        # 确保DataFrame包含所需列
        required_columns = ['日期', '开盘', '收盘', '最高', '最低', '成交量', '成交额']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"缺少必要列: {col}")
        
        # 处理数据
        for _, row in df.iterrows():
            record = {'stock_code': stock_code}
            
            for src_col, dst_col in column_mapping.items():
                if src_col in df.columns:
                    # 日期需要特殊处理
                    if src_col == '日期':
                        if isinstance(row[src_col], str):
                            record[dst_col] = datetime.datetime.strptime(row[src_col], '%Y-%m-%d').date()
                        else:
                            record[dst_col] = row[src_col]
                    else:
                        record[dst_col] = row[src_col]
            
            records.append(record)
        
        return records
    
    @transaction.atomic
    def fetch_and_save(self, stock_code: str, start_date: Optional[str] = None, 
                       end_date: Optional[str] = None, force_update: bool = False) -> Tuple[bool, str]:
        """
        获取并保存股票历史数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期，默认为上市以来的所有数据
            end_date: 结束日期，默认为今天
            force_update: 是否强制更新现有数据
        
        Returns:
            (成功状态, 消息)
        """
        # 获取或创建数据状态记录
        status, created = StockDataStatus.objects.get_or_create(
            stock_code=stock_code,
            defaults={'status': 'fetching'}
        )
        
        # 如果不是强制更新且数据已完整，则跳过
        if not force_update and status.status == 'complete' and status.is_complete:
            # 检查数据是否足够新（不超过3天）
            if status.date_to and (timezone.now().date() - status.date_to).days <= 3:
                return True, f"股票 {stock_code} 数据已是最新"
        
        # 设置状态为获取中
        status.status = 'fetching'
        status.save()
        
        try:
            # 设置日期范围
            if not end_date:
                end_date = timezone.now().date().strftime('%Y%m%d')
            
            if not start_date:
                # 默认获取全部历史数据（使用较早日期）
                start_date = "19900101"  # 从1990年开始，覆盖中国股市所有历史
            
            # 获取数据
            all_data = pd.DataFrame()
            
            # 当数据量大时，可能需要分段获取
            current_end_date = end_date
            years_per_query = 5  # 每次查询5年的数据
            
            while True:
                current_start_date = (datetime.datetime.strptime(current_end_date, '%Y%m%d') - 
                                     datetime.timedelta(days=years_per_query*365)).strftime('%Y%m%d')
                
                # 如果当前开始日期早于指定的起始日期，则使用指定的起始日期
                if current_start_date < start_date:
                    current_start_date = start_date
                
                logger.info(f"获取股票 {stock_code} 从 {current_start_date} 到 {current_end_date} 的历史数据")
                
                df = self._get_stock_history(stock_code, current_start_date, current_end_date)
                
                if not df.empty:
                    all_data = pd.concat([df, all_data], ignore_index=True)
                
                # 如果已经获取到指定的起始日期或者获取的数据为空，则结束循环
                if current_start_date == start_date or df.empty:
                    break
                
                # 移动时间窗口
                current_end_date = (datetime.datetime.strptime(current_start_date, '%Y%m%d') - 
                                   datetime.timedelta(days=1)).strftime('%Y%m%d')
            
            # 删除可能的重复记录
            if not all_data.empty and '日期' in all_data.columns:
                all_data = all_data.drop_duplicates(subset=['日期'])
            
            if all_data.empty:
                status.status = 'error'
                status.error_message = f"未找到股票 {stock_code} 的历史数据"
                status.save()
                return False, status.error_message
            
            # 处理数据
            records = self._process_dataframe(all_data, stock_code)
            
            # 批量创建或更新记录
            with transaction.atomic():
                # 如果是强制更新，先删除指定日期范围内的旧数据
                start_date_obj = datetime.datetime.strptime(start_date, '%Y%m%d').date()
                end_date_obj = datetime.datetime.strptime(end_date, '%Y%m%d').date()
                
                if force_update:
                    StockDailyQuote.objects.filter(
                        stock_code=stock_code,
                        trade_date__range=(start_date_obj, end_date_obj)
                    ).delete()
                
                # 批量插入新记录
                batch_size = 100  # 每批处理的记录数
                for i in range(0, len(records), batch_size):
                    batch = records[i:i+batch_size]
                    batch_objects = [StockDailyQuote(**record) for record in batch]
                    
                    # 使用批量创建提高性能
                    StockDailyQuote.objects.bulk_create(
                        batch_objects,
                        ignore_conflicts=True  # 忽略唯一键冲突
                    )
            
            # 更新状态信息
            record_count = StockDailyQuote.objects.filter(stock_code=stock_code).count()
            min_date = StockDailyQuote.objects.filter(stock_code=stock_code).order_by('trade_date').values_list('trade_date', flat=True).first()
            max_date = StockDailyQuote.objects.filter(stock_code=stock_code).order_by('-trade_date').values_list('trade_date', flat=True).first()
            
            status.status = 'complete'
            status.is_complete = True
            status.date_from = min_date
            status.date_to = max_date
            status.record_count = record_count
            status.error_message = None
            status.save()
            
            return True, f"成功获取股票 {stock_code} 的历史数据，共 {record_count} 条记录"
            
        except Exception as e:
            logger.exception(f"获取股票 {stock_code} 数据时发生错误: {str(e)}")
            
            # 更新状态为错误
            status.status = 'error'
            status.error_message = str(e)
            status.save()
            
            return False, f"获取股票 {stock_code} 数据失败: {str(e)}"
    
    def check_stock_data_status(self, stock_code: str) -> Tuple[bool, StockDataStatus]:
        """
        检查股票数据状态
        
        Args:
            stock_code: 股票代码
        
        Returns:
            (数据是否可用, 状态对象)
        """
        # 获取或创建状态记录
        status, created = StockDataStatus.objects.get_or_create(
            stock_code=stock_code,
            defaults={'status': 'never_fetched'}
        )
        
        # 如果数据完整且是最新的，返回True
        if status.status == 'complete' and status.is_complete:
            # 检查数据是否足够新（不超过3天）
            if status.date_to and (timezone.now().date() - status.date_to).days <= 3:
                return True, status
        
        return False, status 