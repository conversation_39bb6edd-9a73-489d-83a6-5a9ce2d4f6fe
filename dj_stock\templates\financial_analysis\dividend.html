{% extends "base.html" %}
{% load static %}

{% block title %}分红记录 - 股票分析系统{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">分红记录</h1>
    <div class="text-muted">共 {{ total_dividends }} 条记录</div>
  </div>

  <div class="card mb-3">
    <div class="card-body">
      <form method="get" action="{% url 'financial_analysis:dividend_list' %}" id="filter-form">
        <div class="row g-3 mb-3">
          <div class="col-md-3">
            <div class="form-label">年份筛选</div>
            <select name="year" class="form-select" id="year-select">
              <option value="">全部年份</option>
              {% for y in all_years %}
                <option value="{{ y }}" {% if y|stringformat:"i" == year %}selected{% endif %}>{{ y }}年</option>
              {% endfor %}
            </select>
          </div>
          <div class="col-md-3">
            <div class="form-label">实施状态</div>
            <select name="implementation_status" class="form-select" id="status-select">
              <option value="">全部状态</option>
              {% for status in implementation_statuses %}
                <option value="{{ status }}" {% if status == implementation_status %}selected{% endif %}>{{ status }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="col-md-6">
            <div class="form-label">股票搜索</div>
            <div class="input-icon">
              <input type="text" name="q" value="{{ query }}" class="form-control" placeholder="输入股票代码或名称..." id="search-input">
              <span class="input-icon-addon">
                <i class="ti ti-search"></i>
              </span>
            </div>
          </div>
        </div>

        <div class="row g-3 mb-3">
          <div class="col-md-4">
            <div class="form-label">现金分红范围 (元/10股)</div>
            <div class="d-flex">
              <input type="number" step="0.01" min="0" name="min_cash_dividend" value="{{ min_cash_dividend }}" class="form-control me-2" placeholder="最小值">
              <span class="align-self-center">-</span>
              <input type="number" step="0.01" min="0" name="max_cash_dividend" value="{{ max_cash_dividend }}" class="form-control ms-2" placeholder="最大值">
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-label">送股范围 (股/10股)</div>
            <div class="d-flex">
              <input type="number" step="0.01" min="0" name="min_share_dividend" value="{{ min_share_dividend }}" class="form-control me-2" placeholder="最小值">
              <span class="align-self-center">-</span>
              <input type="number" step="0.01" min="0" name="max_share_dividend" value="{{ max_share_dividend }}" class="form-control ms-2" placeholder="最大值">
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-label">转增范围 (股/10股)</div>
            <div class="d-flex">
              <input type="number" step="0.01" min="0" name="min_share_transfer" value="{{ min_share_transfer }}" class="form-control me-2" placeholder="最小值">
              <span class="align-self-center">-</span>
              <input type="number" step="0.01" min="0" name="max_share_transfer" value="{{ max_share_transfer }}" class="form-control ms-2" placeholder="最大值">
            </div>
          </div>
        </div>

        <!-- 隐藏排序参数 -->
        <input type="hidden" name="sort" value="{{ sort }}" id="sort-field">
        <input type="hidden" name="secondary_sort" value="{{ secondary_sort }}" id="secondary-sort-field">
        <input type="hidden" name="rows_per_page" value="{{ rows_per_page }}" id="rows-per-page-field">

        <div class="d-flex justify-content-end">
          <button type="reset" class="btn btn-outline-secondary me-2">
            <i class="ti ti-refresh me-1"></i>
            重置
          </button>
          <button type="submit" class="btn btn-primary" id="search-btn">
            <i class="ti ti-search me-1"></i>
            查询
          </button>
        </div>
      </form>
    </div>
  </div>

  <div class="card">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>分红年度</th>
              <th>登记日期</th>
              <th class="sortable-header" data-sort="cash_dividend">
                现金分红
                {% if sort == 'cash_dividend' %}<i class="ti ti-arrow-up"></i>
                {% elif sort == '-cash_dividend' %}<i class="ti ti-arrow-down"></i>{% endif %}
              </th>
              <th class="sortable-header" data-sort="share_dividend">
                送股
                {% if sort == 'share_dividend' %}<i class="ti ti-arrow-up"></i>
                {% elif sort == '-share_dividend' %}<i class="ti ti-arrow-down"></i>{% endif %}
              </th>
              <th class="sortable-header" data-sort="share_transfer">
                转增
                {% if sort == 'share_transfer' %}<i class="ti ti-arrow-up"></i>
                {% elif sort == '-share_transfer' %}<i class="ti ti-arrow-down"></i>{% endif %}
              </th>
              <th>实施状态</th>
            </tr>
          </thead>
          <tbody>
            {% for dividend in page_obj %}
            <tr>
              <td>
                <a href="{% url 'market_data:stock_detail' dividend.stock_code %}">{{ dividend.stock_code }}</a>
              </td>
              <td>{{ dividend.stock_name }}</td>
              <td>{{ dividend.registration_date|date:"Y" }}</td>
              <td>{{ dividend.registration_date|date:"Y-m-d" }}</td>
              <td class="text-danger fw-bold">{{ dividend.cash_dividend|default:"-" }}</td>
              <td class="text-danger fw-bold">{{ dividend.share_dividend|default:"-" }}</td>
              <td class="text-danger fw-bold">{{ dividend.share_transfer|default:"-" }}</td>
              <td>{{ dividend.implementation_status }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="8" class="text-center">未找到分红记录</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% include "includes/pagination.html" with page_obj=page_obj rows_per_page=rows_per_page %}
    </div>
  </div>
</div>
{% endblock content %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 为可排序的表头添加点击事件
    const sortableHeaders = document.querySelectorAll('.sortable-header');
    sortableHeaders.forEach(header => {
      header.style.cursor = 'pointer';
      header.addEventListener('click', function() {
        const sortField = this.dataset.sort;
        const currentSort = document.getElementById('sort-field').value;
        const currentSecondarySort = document.getElementById('secondary-sort-field').value;

        // 判断点击的是否是当前主排序字段
        if (currentSort === sortField) {
          // 切换升序/降序
          document.getElementById('sort-field').value = '-' + sortField;
        } else if (currentSort === '-' + sortField) {
          // 已经是降序，切换回升序
          document.getElementById('sort-field').value = sortField;
        } else {
          // 点击的是新字段，设置为新的主排序，原来的主排序变为次排序
          document.getElementById('secondary-sort-field').value = currentSort;
          document.getElementById('sort-field').value = sortField;
        }

        // 提交表单，保留所有参数
        submitSearch();
      });
    });

    // 处理查询按钮点击事件
    document.getElementById('search-btn').addEventListener('click', function() {
      submitSearch();
    });

    // 自定义搜索提交函数，确保保留所有参数
    function submitSearch() {
      const year = document.getElementById('year-select').value;
      const query = document.getElementById('search-input').value;
      const sort = document.getElementById('sort-field').value;
      const secondarySort = document.getElementById('secondary-sort-field').value;

      // 构建URL
      let url = "{% url 'financial_analysis:dividend_list' %}?";
      if (query) url += `q=${encodeURIComponent(query)}&`;
      if (year) url += `year=${encodeURIComponent(year)}&`;
      if (sort) url += `sort=${encodeURIComponent(sort)}&`;
      if (secondarySort) url += `secondary_sort=${encodeURIComponent(secondarySort)}&`;

      // 移除末尾的 & 如果存在
      if (url.endsWith('&')) {
        url = url.slice(0, -1);
      }

      // 跳转到构建的URL
      window.location.href = url;
    }

    // 处理回车键搜索
    document.getElementById('search-input').addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        submitSearch();
      }
    });

    // 处理年份选择变化
    document.getElementById('year-select').addEventListener('change', function() {
      submitSearch();
    });
  });
</script>
{% endblock extra_js %}