{% extends "base.html" %} {% load static %} {% block title %}首页 - 股票分析系统{% endblock %} {% block extra_css %}
<link rel="stylesheet" href="{% static 'css/dashboard.css' %}" />
<link rel="stylesheet" href="{% static 'css/market-widgets.css' %}" />
<link rel="stylesheet" href="{% static 'css/market-widgets-enhanced.css' %}" />
{% endblock %} {% block content %}
<div class="container-fluid">
  <!-- 顶部日期选择器和市场概况 -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0"><i class="ti ti-dashboard me-2"></i> 市场概览</h2>
        <div class="date-selector">
          <span class="date-label"><i class="ti ti-calendar me-1"></i> 交易日期:</span>
          <input type="date" id="tradeDatePicker" class="date-input" value="{{ trade_date|date:'Y-m-d' }}" max="{{ latest_trade_date|date:'Y-m-d' }}" />
          <span class="ms-2 fw-bold">{{ trade_date|date:"Y年m月d日" }}</span>
        </div>
      </div>

      <!-- 市场概况统计卡片 -->
      <div class="row g-3 mb-4">
        <!-- 市场情绪 -->
        <div class="col-md-6">
          <div class="card market-overview-card h-100">
            <div class="card-header">
              <div class="d-flex align-items-center">
                <div class="avatar bg-red-lt me-3">
                  <i class="ti ti-mood-happy"></i>
                </div>
                <h3 class="card-title">市场情绪</h3>
              </div>
            </div>
            <div class="card-body">
              <div class="market-sentiment-indicator">
                <div class="progress" style="height: 8px">
                  <div class="progress-bar bg-danger" style="width: {{ market_sentiment.up_percent|default:0 }}%" role="progressbar" aria-label="涨"></div>
                  <div class="progress-bar bg-success" style="width: {{ market_sentiment.down_percent|default:0 }}%" role="progressbar" aria-label="跌"></div>
                  <div class="progress-bar bg-secondary" style="width: {{ market_sentiment.flat_percent|default:0 }}%" role="progressbar" aria-label="平"></div>
                </div>
              </div>
              <div class="row g-3 text-center">
                <div class="col-4">
                  <div class="p-3 rounded bg-danger-subtle">
                    <h3 class="mb-1 text-danger">{{ market_sentiment.up_count|default:0 }}</h3>
                    <div class="text-muted">上涨</div>
                  </div>
                </div>
                <div class="col-4">
                  <div class="p-3 rounded bg-success-subtle">
                    <h3 class="mb-1 text-success">{{ market_sentiment.down_count|default:0 }}</h3>
                    <div class="text-muted">下跌</div>
                  </div>
                </div>
                <div class="col-4">
                  <div class="p-3 rounded bg-secondary-subtle">
                    <h3 class="mb-1 text-secondary">{{ market_sentiment.flat_count|default:0 }}</h3>
                    <div class="text-muted">平盈</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 涨跌停统计 -->
        <div class="col-md-6">
          <div class="card market-overview-card h-100">
            <div class="card-header">
              <div class="d-flex align-items-center">
                <div class="avatar bg-blue-lt me-3">
                  <i class="ti ti-chart-bar"></i>
                </div>
                <h3 class="card-title">涨跌停统计</h3>
              </div>
            </div>
            <div class="card-body">
              <div class="limit-stats">
                <div class="limit-stat-item">
                  <div class="stat-value text-danger">{{ limit_up_count|default:0 }}</div>
                  <div class="stat-label">涨停</div>
                </div>
                <div class="limit-stat-item">
                  <div class="stat-value text-success">{{ limit_down_count|default:0 }}</div>
                  <div class="stat-label">跌停</div>
                </div>
              </div>
              <div class="row g-3 text-center mt-2">
                <div class="col-6">
                  <div class="p-3 rounded bg-danger-subtle">
                    <h3 class="mb-1 text-danger">{{ up_5_count|default:0 }}</h3>
                    <div class="text-muted">涨5%以上</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="p-3 rounded bg-success-subtle">
                    <h3 class="mb-1 text-success">{{ down_5_count|default:0 }}</h3>
                    <div class="text-muted">跌5%以上</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 主要指数卡片式布局 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="mb-0">
              <div class="d-flex align-items-center">
                <div class="avatar bg-blue-lt me-3">
                  <i class="ti ti-chart-line"></i>
                </div>
                主要指数
              </div>
            </h3>
            <a href="{% url 'market_data:market_index_list' %}" class="btn btn-outline-primary btn-sm">
              <span class="d-flex align-items-center">
                <i class="ti ti-arrow-right me-1"></i>
                查看全部指数
              </span>
            </a>
          </div>

          <div class="row g-3">
            {% for index in market_indices|slice:":6" %}
            <div class="col-md-4 col-sm-6">
              <div
                class="card index-card {% if index.change_percent > 0 %}bg-danger-subtle{% elif index.change_percent < 0 %}bg-success-subtle{% else %}bg-secondary-subtle{% endif %}"
              >
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="index-name">
                      <a href="{% url 'market_data:market_index_detail' index.index_code %}" class="text-reset"> {{ index.index_name }} </a>
                    </h5>
                    <span
                      class="badge {% if index.change_percent > 0 %}bg-danger{% elif index.change_percent < 0 %}bg-success{% else %}bg-secondary{% endif %} text-white"
                    >
                      {% if index.change_percent > 0 %}+{% endif %}{{ index.change_percent|floatformat:2 }}%
                    </span>
                  </div>
                  <div class="d-flex justify-content-between align-items-end">
                    <div>
                      <div
                        class="index-price {% if index.change_percent > 0 %}text-danger{% elif index.change_percent < 0 %}text-success{% else %}text-secondary{% endif %}"
                      >
                        {{ index.close_price|floatformat:2 }}
                      </div>
                      <div class="text-muted small">
                        涨跌额:
                        <span class="{% if index.change_amount > 0 %}text-danger{% elif index.change_amount < 0 %}text-success{% endif %}">
                          {% if index.change_amount > 0 %}+{% endif %}{{ index.change_amount|floatformat:2 }}
                        </span>
                      </div>
                    </div>
                    <div class="text-end">
                      <div class="text-muted small">振幅: {{ index.amplitude|floatformat:2 }}%</div>
                      <div class="text-muted small">成交额: {{ index.amount|floatformat:2 }}亿</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {% empty %}
            <div class="col-12">
              <div class="card">
                <div class="card-body text-center py-5">
                  <div class="empty">
                    <div class="empty-img">
                      <i class="ti ti-chart-area-line" style="font-size: 3rem"></i>
                    </div>
                    <p class="empty-title">暂无指数数据</p>
                    <p class="empty-subtitle text-muted">指数数据暂未更新，请稍后再试</p>
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>

      <!-- 市场热点和市场统计 -->
      <div class="row mb-4">
        <!-- 市场热点板块 -->
        <div class="col-md-6">
          <div class="card h-100">
            <div class="card-header bg-light">
              <div class="d-flex align-items-center">
                <div class="avatar bg-red-lt me-2">
                  <i class="ti ti-flame"></i>
                </div>
                <h3 class="card-title m-0">市场热点板块</h3>
              </div>
            </div>
            <div class="card-body p-3">
              <!-- 热点板块卡片式布局 -->
              <div class="row g-2 mb-3">
                {% for industry in industry_changes|slice:":3" %}
                <div class="col-md-4">
                  <div
                    class="card {% if industry.avg_change > 0 %}bg-danger-subtle{% elif industry.avg_change < 0 %}bg-success-subtle{% else %}bg-secondary-subtle{% endif %} border-0"
                  >
                    <div class="card-body p-2 text-center">
                      <h4
                        class="{% if industry.avg_change > 0 %}text-danger{% elif industry.avg_change < 0 %}text-success{% else %}text-secondary{% endif %} mb-1"
                      >
                        {% if industry.avg_change > 0 %}+{% endif %}{{ industry.avg_change|floatformat:2 }}%
                      </h4>
                      <div class="text-truncate small fw-bold mb-1">
                        <a href="{% url 'market_data:industry_stocks' industry.industry %}" class="text-reset"> {{ industry.industry }} </a>
                      </div>
                      <span
                        class="badge {% if industry.avg_change > 0 %}bg-danger{% elif industry.avg_change < 0 %}bg-success{% else %}bg-secondary{% endif %} text-white"
                        >行业板块</span
                      >
                    </div>
                  </div>
                </div>
                {% endfor %}
              </div>

              <!-- 热点板块列表 -->
              <div class="list-group list-group-flush">
                {% for industry in industry_changes|slice:"3:7" %}
                <div class="list-group-item px-0 py-2 border-0">
                  <div class="d-flex justify-content-between align-items-center mb-1">
                    <div class="d-flex align-items-center">
                      <span
                        class="badge {% if industry.avg_change > 0 %}bg-danger{% elif industry.avg_change < 0 %}bg-success{% else %}bg-secondary{% endif %} me-2"
                      >
                        {{ forloop.counter|add:3 }}
                      </span>
                      <span class="text-truncate" style="max-width: 120px">
                        <a href="{% url 'market_data:industry_stocks' industry.industry %}" class="text-reset fw-medium"> {{ industry.industry }} </a>
                      </span>
                    </div>
                    <span
                      class="fw-bold {% if industry.avg_change > 0 %}text-danger{% elif industry.avg_change < 0 %}text-success{% else %}text-secondary{% endif %}"
                    >
                      {% if industry.avg_change > 0 %}+{% endif %}{{ industry.avg_change|floatformat:2 }}%
                    </span>
                  </div>
                  <div class="progress" style="height: 3px; border-radius: 1.5px">
                    <div
                      class="progress-bar {% if industry.avg_change > 0 %}bg-danger{% elif industry.avg_change < 0 %}bg-success{% else %}bg-secondary{% endif %}"
                      style="width: {% if industry.avg_change > 0 %}{{ industry.avg_change }}{% elif industry.avg_change < 0 %}{{ industry.avg_change|cut:'-' }}{% else %}3{% endif %}%"
                      role="progressbar"
                    ></div>
                  </div>
                </div>
                {% endfor %}
              </div>

              <div class="text-end mt-3">
                <a href="{% url 'market_data:industry_board_list' %}" class="btn btn-sm btn-outline-primary">
                  <i class="ti ti-arrow-right me-1"></i> 查看全部行业
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 市场统计 -->
        <div class="col-md-6">
          <div class="card h-100">
            <div class="card-header bg-light">
              <div class="d-flex align-items-center">
                <div class="avatar bg-purple-lt me-2">
                  <i class="ti ti-chart-pie"></i>
                </div>
                <h3 class="card-title m-0">市场统计</h3>
              </div>
            </div>
            <div class="card-body p-3">
              <!-- 市场情绪分布 -->
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-muted">市场情绪分布</span>
                <span class="badge bg-light text-dark"
                  >总计: {{ market_sentiment.up_count|add:market_sentiment.down_count|add:market_sentiment.flat_count }}</span
                >
              </div>

              <div class="progress mb-3" style="height: 10px; border-radius: 5px">
                <div class="progress-bar bg-danger" style="width: {{ market_sentiment.up_percent|default:0 }}%" role="progressbar"></div>
                <div class="progress-bar bg-success" style="width: {{ market_sentiment.down_percent|default:0 }}%" role="progressbar"></div>
                <div class="progress-bar bg-secondary" style="width: {{ market_sentiment.flat_percent|default:0 }}%" role="progressbar"></div>
              </div>

              <div class="row g-2 text-center mb-4">
                <div class="col-4">
                  <div class="card bg-danger-subtle border-0">
                    <div class="card-body p-2">
                      <h4 class="text-danger mb-0">{{ market_sentiment.up_count|default:0 }}</h4>
                      <div class="small text-muted">上涨 ({{ market_sentiment.up_percent|default:0|floatformat:1 }}%)</div>
                    </div>
                  </div>
                </div>
                <div class="col-4">
                  <div class="card bg-success-subtle border-0">
                    <div class="card-body p-2">
                      <h4 class="text-success mb-0">{{ market_sentiment.down_count|default:0 }}</h4>
                      <div class="small text-muted">下跌 ({{ market_sentiment.down_percent|default:0|floatformat:1 }}%)</div>
                    </div>
                  </div>
                </div>
                <div class="col-4">
                  <div class="card bg-secondary-subtle border-0">
                    <div class="card-body p-2">
                      <h4 class="text-secondary mb-0">{{ market_sentiment.flat_count|default:0 }}</h4>
                      <div class="small text-muted">平盘 ({{ market_sentiment.flat_percent|default:0|floatformat:1 }}%)</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 涨跌停统计 -->
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-muted">涨跌停统计</span>
              </div>

              <div class="row g-2 text-center">
                <div class="col-4">
                  <div class="card bg-danger border-0">
                    <div class="card-body p-2">
                      <h4 class="text-white mb-0">{{ limit_up_stocks|default:0 }}</h4>
                      <div class="small text-white">涨停</div>
                    </div>
                  </div>
                </div>
                <div class="col-4">
                  <div class="card bg-success border-0">
                    <div class="card-body p-2">
                      <h4 class="text-white mb-0">{{ limit_down_stocks|default:0 }}</h4>
                      <div class="small text-white">跌停</div>
                    </div>
                  </div>
                </div>
                <div class="col-4">
                  <div class="card bg-info border-0">
                    <div class="card-body p-2">
                      <h4 class="text-white mb-0">{{ up_5_count|default:0 }}</h4>
                      <div class="small text-white">涨幅>5%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资金流向和市场估值 -->
      <div class="row mb-4">
        {% include "market_data/fund_flow_fixed.html" %}

        <!-- 市场估值指标 -->
        <div class="col-md-6">
          <div class="card h-100">
            <div class="card-header bg-light">
              <div class="d-flex align-items-center">
                <div class="avatar bg-purple-lt me-2">
                  <i class="ti ti-chart-pie"></i>
                </div>
                <h3 class="card-title m-0">市场估值指标</h3>
              </div>
            </div>
            <div class="card-body p-3">
              <!-- PE和PB指标 -->
              <div class="row g-3 mb-4">
                <div class="col-6">
                  <div class="card bg-blue-lt border-0">
                    <div class="card-body p-3 text-center">
                      <div class="d-flex justify-content-center mb-2">
                        <div class="avatar bg-blue">
                          <i class="ti ti-chart-bar text-white"></i>
                        </div>
                      </div>
                      <h3 class="text-blue mb-0">{{ avg_pe|default:"18.5"|floatformat:1 }}</h3>
                      <div class="text-muted mb-1">市场PE</div>
                      <span class="badge {% if pe_change > 0 %}bg-danger{% else %}bg-success{% endif %}">
                        {% if pe_change > 0 %}+{% endif %}{{ pe_change|default:"0.2"|floatformat:1 }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="card bg-green-lt border-0">
                    <div class="card-body p-3 text-center">
                      <div class="d-flex justify-content-center mb-2">
                        <div class="avatar bg-green">
                          <i class="ti ti-chart-area text-white"></i>
                        </div>
                      </div>
                      <h3 class="text-success mb-0">{{ avg_pb|default:"1.8"|floatformat:1 }}</h3>
                      <div class="text-muted mb-1">市场PB</div>
                      <span class="badge {% if pb_change > 0 %}bg-danger{% else %}bg-success{% endif %}">
                        {% if pb_change > 0 %}+{% endif %}{{ pb_change|default:"-0.1"|floatformat:1 }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 估值范围 -->
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-muted">市场PE范围</span>
              </div>

              <div class="progress mb-2" style="height: 8px; border-radius: 4px">
                <div class="progress-bar bg-success" style="width: 30%" role="progressbar"></div>
                <div class="progress-bar bg-info" style="width: 40%" role="progressbar"></div>
                <div class="progress-bar bg-warning" style="width: 30%" role="progressbar"></div>
              </div>

              <div class="d-flex justify-content-between small text-muted mb-4">
                <div><i class="ti ti-arrow-down-right text-success"></i> 最低: 12.5</div>
                <div><i class="ti ti-point text-primary"></i> 当前: {{ avg_pe|default:"18.5"|floatformat:1 }}</div>
                <div><i class="ti ti-arrow-up-right text-danger"></i> 最高: 25.3</div>
              </div>

              <!-- 技术指标 -->
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-muted">技术指标</span>
              </div>

              <div class="row g-2 text-center">
                <div class="col-6">
                  <div
                    class="card border-0 {% if market_sentiment.up_count > market_sentiment.down_count %}bg-danger-subtle{% else %}bg-success-subtle{% endif %}"
                  >
                    <div class="card-body p-2">
                      <h4 class="{% if market_sentiment.up_count > market_sentiment.down_count %}text-danger{% else %}text-success{% endif %} mb-0">92.0</h4>
                      <div class="small text-muted">MACD</div>
                      <span class="badge {% if market_sentiment.up_count > market_sentiment.down_count %}bg-danger{% else %}bg-success{% endif %} mt-1">
                        {% if market_sentiment.up_count > market_sentiment.down_count %}看多{% else %}看空{% endif %}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <div
                    class="card border-0 {% if market_sentiment.up_count < market_sentiment.down_count %}bg-danger-subtle{% else %}bg-success-subtle{% endif %}"
                  >
                    <div class="card-body p-2">
                      <h4 class="{% if market_sentiment.up_count < market_sentiment.down_count %}text-danger{% else %}text-success{% endif %} mb-0">7.0</h4>
                      <div class="small text-muted">KDJ</div>
                      <span class="badge {% if market_sentiment.up_count < market_sentiment.down_count %}bg-danger{% else %}bg-success{% endif %} mt-1">
                        {% if market_sentiment.up_count < market_sentiment.down_count %}看多{% else %}看空{% endif %}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- RSI指标 -->
              <div class="row g-2 text-center mt-3">
                <div class="col-12">
                  <div class="card border-0 bg-secondary-subtle">
                    <div class="card-body p-2">
                      <h4 class="text-secondary mb-0">1.0</h4>
                      <div class="small text-muted">RSI</div>
                      <span class="badge bg-secondary mt-1">中性</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 涨跌榜新布局 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">
          <div class="d-flex align-items-center">
            <div class="avatar bg-orange-lt me-3">
              <i class="ti ti-list-numbers"></i>
            </div>
            涨跌榜
          </div>
        </h3>
      </div>

      <div class="row g-3">
        <!-- 涨幅榜 -->
        <div class="col-md-6">
          <div class="card h-100">
            <div class="card-header bg-danger-subtle">
              <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title text-danger">
                  <i class="ti ti-trending-up me-2"></i>
                  涨幅榜
                </h3>
                <a href="{% url 'market_data:stock_list' %}?sort=-change_percent" class="btn btn-sm btn-outline-danger">
                  <i class="ti ti-arrow-right me-1"></i>
                  更多
                </a>
              </div>
            </div>
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table table-vcenter card-table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th>股票代码</th>
                      <th>股票名称</th>
                      <th class="text-end">最新价</th>
                      <th class="text-end">涨跌幅</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for stock in top_gainers %}
                    <tr>
                      <td class="font-monospace">
                        <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset">{{ stock.stock_code }}</a>
                      </td>
                      <td class="text-truncate" style="max-width: 120px">
                        <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset" title="{{ stock.stock_name }}">
                          {{ stock.stock_name }}
                        </a>
                      </td>
                      <td class="text-end fw-bold">{{ stock.close_price|floatformat:2 }}</td>
                      <td class="text-end text-danger fw-bold">+{{ stock.change_percent|floatformat:2 }}%</td>
                    </tr>
                    {% empty %}
                    <tr>
                      <td colspan="4" class="text-center text-muted py-4">
                        <div class="empty">
                          <div class="empty-img">
                            <i class="ti ti-mood-empty" style="font-size: 2rem"></i>
                          </div>
                          <p class="empty-title">暂无数据</p>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- 跌幅榜 -->
        <div class="col-md-6">
          <div class="card h-100">
            <div class="card-header bg-success-subtle">
              <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title text-success">
                  <i class="ti ti-trending-down me-2"></i>
                  跌幅榜
                </h3>
                <a href="{% url 'market_data:stock_list' %}?sort=change_percent" class="btn btn-sm btn-outline-success">
                  <i class="ti ti-arrow-right me-1"></i>
                  更多
                </a>
              </div>
            </div>
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table table-vcenter card-table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th>股票代码</th>
                      <th>股票名称</th>
                      <th class="text-end">最新价</th>
                      <th class="text-end">涨跌幅</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for stock in top_losers %}
                    <tr>
                      <td class="font-monospace">
                        <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset">{{ stock.stock_code }}</a>
                      </td>
                      <td class="text-truncate" style="max-width: 120px">
                        <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset" title="{{ stock.stock_name }}">
                          {{ stock.stock_name }}
                        </a>
                      </td>
                      <td class="text-end fw-bold">{{ stock.close_price|floatformat:2 }}</td>
                      <td class="text-end text-success fw-bold">{{ stock.change_percent|floatformat:2 }}%</td>
                    </tr>
                    {% empty %}
                    <tr>
                      <td colspan="4" class="text-center text-muted py-4">
                        <div class="empty">
                          <div class="empty-img">
                            <i class="ti ti-mood-empty" style="font-size: 2rem"></i>
                          </div>
                          <p class="empty-title">暂无数据</p>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 成交额榜新布局 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">
          <div class="d-flex align-items-center">
            <div class="avatar bg-blue-lt me-3">
              <i class="ti ti-coin"></i>
            </div>
            成交额榜
          </div>
        </h3>
        <a href="{% url 'market_data:stock_list' %}?sort=-amount" class="btn btn-sm btn-outline-primary">
          <i class="ti ti-arrow-right me-1"></i>
          查看更多
        </a>
      </div>

      <div class="card">
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-vcenter card-table mb-0">
              <thead class="bg-light">
                <tr>
                  <th>股票代码</th>
                  <th>股票名称</th>
                  <th class="text-end">最新价</th>
                  <th class="text-end">涨跌幅</th>
                  <th class="text-end">成交量(万手)</th>
                  <th class="text-end">成交额(亿)</th>
                  <th class="text-end">换手率</th>
                </tr>
              </thead>
              <tbody>
                {% for stock in top_turnover %}
                <tr>
                  <td class="font-monospace">
                    <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset">{{ stock.stock_code }}</a>
                  </td>
                  <td class="text-truncate" style="max-width: 120px">
                    <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-reset" title="{{ stock.stock_name }}">
                      {{ stock.stock_name }}
                    </a>
                  </td>
                  <td class="text-end fw-bold">{{ stock.close_price|floatformat:2 }}</td>
                  <td class="text-end {% if stock.change_percent > 0 %}text-danger{% elif stock.change_percent < 0 %}text-success{% endif %} fw-bold">
                    {% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:2 }}%
                  </td>
                  <td class="text-end">{{ stock.volume|floatformat:2 }}</td>
                  <td class="text-end fw-bold">{{ stock.amount|floatformat:2 }}</td>
                  <td class="text-end">{{ stock.turnover_rate|floatformat:2 }}%</td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="7" class="text-center text-muted py-4">
                    <div class="empty">
                      <div class="empty-img">
                        <i class="ti ti-mood-empty" style="font-size: 2rem"></i>
                      </div>
                      <p class="empty-title">暂无数据</p>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  // 日期选择器交互逻辑
  document.addEventListener('DOMContentLoaded', function () {
    const datePicker = document.getElementById('tradeDatePicker')

    // 日期选择器变化事件
    datePicker.addEventListener('change', function (e) {
      const selectedDate = e.target.value
      // 显示加载指示器
      showLoading()
      // 重定向到选择的日期
      window.location.href = `{% url 'market_data:index' %}?trade_date=${selectedDate}`
    })

    // 显示加载指示器函数
    function showLoading() {
      // 创建加载遮罩
      const overlay = document.createElement('div')
      overlay.className = 'loading-overlay'
      overlay.innerHTML = `
        <div class="loading-spinner">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <div class="mt-2">正在加载数据...</div>
        </div>
      `

      // 添加到文档中
      document.body.appendChild(overlay)

      // 添加样式
      const style = document.createElement('style')
      style.textContent = `
        .loading-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.8);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        }
        .loading-spinner {
          text-align: center;
        }
      `
      document.head.appendChild(style)
    }
  })
</script>
{% endblock %}
