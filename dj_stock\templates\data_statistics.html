{% extends "base.html" %} {% block title %}数据统计 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">数据分析</div>
        <h2 class="page-title">数据统计</h2>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 股票总数和行业分布 -->
    <div class="row row-cards row-deck">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">股票总数和行业分布</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-4">
                <div class="card card-sm">
                  <div class="card-body">
                    <div class="row align-items-center">
                      <div class="col-auto">
                        <span class="bg-blue text-white avatar">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="icon"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            stroke-width="2"
                            stroke="currentColor"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                            <path d="M12 8l0 8"></path>
                            <path d="M8 12l8 0"></path>
                          </svg>
                        </span>
                      </div>
                      <div class="col">
                        <div class="font-weight-medium">股票总数</div>
                        <div class="text-muted">{{ total_stocks }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-8">
                <div id="industryChart" style="height: 300px"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 风险预警趋势 -->
    <div class="row row-cards row-deck mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">风险预警股票数量变化趋势</h3>
          </div>
          <div class="card-body">
            <div id="riskTrendChart" style="height: 300px"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分红送配统计 -->
    <div class="row row-cards row-deck mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">分红送配统计</h3>
          </div>
          <div class="card-body">
            <div id="dividendChart" style="height: 300px"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 北向资金趋势 -->
    <div class="row row-cards row-deck mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">北向资金持仓及增持趋势</h3>
          </div>
          <div class="card-body">
            <div id="northTrendChart" style="height: 300px"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行业板块涨跌幅分布 -->
    <div class="row row-cards row-deck mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">行业板块涨跌幅分布</h3>
          </div>
          <div class="card-body">
            <div id="industryPerformanceChart" style="height: 300px"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %} {% block extra_js %}
<script src="{% static 'js/echarts.min.js' %}"></script>
<script>
  // 行业分布图表
  const industryChart = echarts.init(document.getElementById('industryChart'));
  const industryData = {{ industry_stats|safe }};
  industryChart.setOption({
      title: {
          text: '行业分布'
      },
      tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
      },
      series: [{
          type: 'pie',
          radius: '50%',
          data: industryData.map(item => ({
              name: item.industry || '未分类',
              value: item.count
          })),
          emphasis: {
              itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
          }
      }]
  });

  // 风险预警趋势图表
  const riskTrendChart = echarts.init(document.getElementById('riskTrendChart'));
  const riskData = {{ risk_trend|safe }};
  riskTrendChart.setOption({
      title: {
          text: '风险预警趋势'
      },
      tooltip: {
          trigger: 'axis'
      },
      xAxis: {
          type: 'category',
          data: riskData.map(item => item.date)
      },
      yAxis: {
          type: 'value'
      },
      series: [{
          data: riskData.map(item => item.count),
          type: 'line',
          smooth: true
      }]
  });

  // 分红送配统计图表
  const dividendChart = echarts.init(document.getElementById('dividendChart'));
  const dividendData = {{ dividend_stats|safe }};
  dividendChart.setOption({
      title: {
          text: '分红送配统计'
      },
      tooltip: {
          trigger: 'axis',
          axisPointer: {
              type: 'shadow'
          }
      },
      legend: {
          data: ['分红公司数', '平均现金分红', '平均送股', '平均转增']
      },
      xAxis: {
          type: 'category',
          data: dividendData.map(item => item.dividend_year + '年')
      },
      yAxis: [
          {
              type: 'value',
              name: '公司数量'
          },
          {
              type: 'value',
              name: '分红金额/股数'
          }
      ],
      series: [
          {
              name: '分红公司数',
              type: 'bar',
              data: dividendData.map(item => item.count)
          },
          {
              name: '平均现金分红',
              type: 'line',
              yAxisIndex: 1,
              data: dividendData.map(item => item.avg_cash)
          },
          {
              name: '平均送股',
              type: 'line',
              yAxisIndex: 1,
              data: dividendData.map(item => item.avg_share)
          },
          {
              name: '平均转增',
              type: 'line',
              yAxisIndex: 1,
              data: dividendData.map(item => item.avg_transfer)
          }
      ]
  });

  // 北向资金趋势图表
  const northTrendChart = echarts.init(document.getElementById('northTrendChart'));
  const northData = {{ north_trend|safe }};
  northTrendChart.setOption({
      title: {
          text: '北向资金持仓及增持趋势'
      },
      tooltip: {
          trigger: 'axis'
      },
      legend: {
          data: ['持仓市值', '增持市值']
      },
      xAxis: {
          type: 'category',
          data: northData.map(item => item.date)
      },
      yAxis: {
          type: 'value',
          name: '市值(元)'
      },
      series: [
          {
              name: '持仓市值',
              type: 'line',
              data: northData.map(item => item.total_inflow)
          },
          {
              name: '增持市值',
              type: 'line',
              data: northData.map(item => item.total_outflow)
          }
      ]
  });

  // 行业板块涨跌幅分布图表
  const industryPerformanceChart = echarts.init(document.getElementById('industryPerformanceChart'));
  const industryPerformanceData = {{ industry_performance|safe }};
  industryPerformanceChart.setOption({
      title: {
          text: '行业板块涨跌幅分布'
      },
      tooltip: {
          trigger: 'axis',
          axisPointer: {
              type: 'shadow'
          }
      },
      xAxis: {
          type: 'value',
          name: '涨跌幅(%)'
      },
      yAxis: {
          type: 'category',
          data: industryPerformanceData.map(item => item.board_name)
      },
      series: [{
          type: 'bar',
          data: industryPerformanceData.map(item => item.avg_change)
      }]
  });

  // 响应式调整
  window.addEventListener('resize', function() {
      industryChart.resize();
      riskTrendChart.resize();
      dividendChart.resize();
      northTrendChart.resize();
      industryPerformanceChart.resize();
  });
</script>
{% endblock %}
