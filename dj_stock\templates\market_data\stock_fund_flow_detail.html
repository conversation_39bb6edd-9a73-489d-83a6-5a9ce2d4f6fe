{% extends 'base.html' %}

{% block title %}{{ stock.stock_name }} 资金流向详情 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">{{ stock.stock_name }} ({{ stock.stock_code }}) 资金流向详情</h2>
        <div class="text-muted mt-1">查看个股的历史资金流向数据</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="btn">
            <i class="ti ti-chart-line me-2"></i>
            股票详情
          </a>
          <a href="{% url 'market_data:stock_fund_flow' %}" class="btn">
            <i class="ti ti-list me-2"></i>
            返回列表
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">股票代码</div>
            </div>
            <div class="h1 mb-3 mt-1">{{ stock.stock_code }}</div>
            <div class="d-flex mb-2">
              <div>所属行业</div>
              <div class="ms-auto">{{ stock.industry|default:"未知" }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">股票名称</div>
            </div>
            <div class="h1 mb-3 mt-1">{{ stock.stock_name }}</div>
            <div class="d-flex mb-2">
              <div>市场类型</div>
              <div class="ms-auto">{{ stock.market|default:"未知" }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">最新主力净流入</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">{{ fund_flow_data.0.date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if fund_flow_data.0.main_net_inflow > 0 %}
              <span class="text-up">{{ fund_flow_data.0.main_net_inflow|floatformat:2 }}万</span>
              {% else %}
              <span class="text-down">{{ fund_flow_data.0.main_net_inflow|floatformat:2 }}万</span>
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>总净流入</div>
              <div class="ms-auto">
                {% if fund_flow_data.0.total_net_inflow > 0 %}
                <span class="text-up">{{ fund_flow_data.0.total_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ fund_flow_data.0.total_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">30日累计净流入</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">近30个交易日</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if cumulative_inflow > 0 %}
              <span class="text-up">{{ cumulative_inflow|floatformat:2 }}万</span>
              {% else %}
              <span class="text-down">{{ cumulative_inflow|floatformat:2 }}万</span>
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>日均</div>
              <div class="ms-auto">
                {% if cumulative_inflow > 0 %}
                <span class="text-up">{{ cumulative_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ cumulative_inflow|floatformat:2 }}万</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史资金流向表格 -->
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="card-title">历史资金流向数据</h3>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>交易日期</th>
              <th>主力净流入</th>
              <th>散户净流入</th>
              <th>总净流入</th>
            </tr>
          </thead>
          <tbody>
            {% for data in fund_flow_data %}
            <tr>
              <td>{{ data.date|date:"Y-m-d" }}</td>
              <td>
                {% if data.main_net_inflow > 0 %}
                <span class="text-up">{{ data.main_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ data.main_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                {% if data.retail_net_inflow > 0 %}
                <span class="text-up">{{ data.retail_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ data.retail_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                {% if data.total_net_inflow > 0 %}
                <span class="text-up">{{ data.total_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ data.total_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="4" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前股票没有资金流向数据。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- 同行业资金流向表格 -->
    {% if industry_fund_flow %}
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">同行业资金流向数据 ({{ stock.industry }})</h3>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>主力净流入</th>
              <th>散户净流入</th>
              <th>总净流入</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for data in industry_fund_flow %}
            <tr>
              <td>{{ data.code }}</td>
              <td>{{ data.name }}</td>
              <td>
                {% if data.main_net_inflow > 0 %}
                <span class="text-up">{{ data.main_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ data.main_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                {% if data.retail_net_inflow > 0 %}
                <span class="text-up">{{ data.retail_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ data.retail_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                {% if data.total_net_inflow > 0 %}
                <span class="text-up">{{ data.total_net_inflow|floatformat:2 }}万</span>
                {% else %}
                <span class="text-down">{{ data.total_net_inflow|floatformat:2 }}万</span>
                {% endif %}
              </td>
              <td>
                <a href="{% url 'market_data:stock_fund_flow_detail' data.code %}" class="btn btn-sm">详情</a>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% endif %}
  </div>
</div>
{% endblock %}
