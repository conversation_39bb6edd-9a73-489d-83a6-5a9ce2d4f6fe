/*
 * 市场数据小部件样式
 * 优化市场情绪、市场热点、资金流向和市场估值等UI组件
 */

/* 通用卡片样式 */
.market-widget-card {
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
}

.market-widget-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.market-widget-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.75rem 1rem;
}

.market-widget-card .card-body {
  padding: 1rem;
}

/* 市场热点板块样式 */
.hot-sector-widget {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.hot-sector-item {
  flex: 1;
  min-width: 80px;
  text-align: center;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.hot-sector-item:hover {
  transform: scale(1.05);
}

.hot-sector-item .name {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hot-sector-item .change {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.hot-sector-item .stocks {
  font-size: 0.75rem;
  color: #6c757d;
}

.hot-sector-item .badge {
  margin-top: 0.5rem;
}

/* 市场情绪样式 */
.sentiment-progress {
  height: 12px;
  border-radius: 6px;
  overflow: visible;
  margin-bottom: 1.5rem;
}

.sentiment-progress .progress-bar {
  position: relative;
}

.sentiment-progress .progress-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.7rem;
  margin-top: 0.25rem;
  white-space: nowrap;
}

.sentiment-stats {
  display: flex;
  justify-content: space-between;
  gap: 0.75rem;
}

.sentiment-stat-item {
  flex: 1;
  text-align: center;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.sentiment-stat-item:hover {
  transform: scale(1.05);
}

.sentiment-stat-item .count {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.sentiment-stat-item .label {
  font-size: 0.75rem;
  color: #6c757d;
}

.sentiment-stat-item .percent {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* 资金流向样式 */
.fund-flow-widget {
  margin-bottom: 1rem;
}

.fund-flow-item {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.fund-flow-item:hover {
  background-color: #f0f0f0;
}

.fund-flow-item .flow-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.fund-flow-item .flow-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fund-flow-item .flow-value {
  font-weight: 700;
}

.fund-flow-item .flow-progress {
  height: 8px;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.fund-flow-item .flow-detail {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6c757d;
}

/* 市场估值指标样式 */
.valuation-widget {
  display: flex;
  gap: 0.75rem;
}

.valuation-item {
  flex: 1;
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.valuation-item:hover {
  transform: scale(1.05);
}

.valuation-item .icon {
  margin-bottom: 0.75rem;
}

.valuation-item .value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.valuation-item .label {
  font-size: 0.875rem;
  color: #6c757d;
}

.valuation-item .change {
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.valuation-chart {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  height: 120px;
}

.valuation-range {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.valuation-range .low {
  color: #28a745;
}

.valuation-range .high {
  color: #dc3545;
}

/* 技术指标样式 */
.tech-indicator-widget {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.tech-indicator-item {
  flex: 1;
  min-width: 100px;
  text-align: center;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.tech-indicator-item:hover {
  transform: scale(1.05);
}

.tech-indicator-item .name {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.tech-indicator-item .value {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.tech-indicator-item .signal {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  font-weight: 500;
}

.tech-indicator-item .signal-buy {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.tech-indicator-item .signal-sell {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.tech-indicator-item .signal-neutral {
  background-color: rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
  .hot-sector-item .change,
  .sentiment-stat-item .count,
  .valuation-item .value,
  .tech-indicator-item .value {
    font-size: 1.25rem;
  }

  .hot-sector-item .name,
  .sentiment-stat-item .label,
  .valuation-item .label,
  .tech-indicator-item .name {
    font-size: 0.75rem;
  }

  .fund-flow-item .flow-title {
    flex-direction: column;
    align-items: flex-start;
  }

  .fund-flow-item .flow-detail {
    flex-direction: column;
    gap: 0.25rem;
  }
}
