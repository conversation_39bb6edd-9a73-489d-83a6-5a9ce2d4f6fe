# -*- coding: utf-8 -*-
"""
市场情绪分析工具
通过技术指标、资金流向、涨跌停数据等分析市场情绪
"""

from datetime import datetime, timedelta
from django.db.models import Count, Sum, Avg
from decimal import Decimal


class MarketSentimentAnalyzer:
    """市场情绪分析器"""
    
    def __init__(self):
        self.sentiment_score = 0
        self.sentiment_level = "中性"
        self.sentiment_color = "warning"
        
    def analyze_limit_sentiment(self, limit_data):
        """分析涨跌停情况的市场情绪"""
        if not limit_data:
            return {'score': 50, 'description': '无涨跌停数据'}
        
        up_limit_count = limit_data.get('up_limit_count', 0)
        down_limit_count = limit_data.get('down_limit_count', 0)
        total_limit = up_limit_count + down_limit_count
        
        if total_limit == 0:
            return {'score': 50, 'description': '市场平稳，无涨跌停股票'}
        
        # 计算涨停占比
        up_limit_ratio = up_limit_count / total_limit
        
        if up_limit_ratio >= 0.8:
            score = 85
            description = f"市场情绪极度乐观，涨停股{up_limit_count}只，跌停股{down_limit_count}只"
        elif up_limit_ratio >= 0.6:
            score = 70
            description = f"市场情绪乐观，涨停股{up_limit_count}只，跌停股{down_limit_count}只"
        elif up_limit_ratio >= 0.4:
            score = 50
            description = f"市场情绪中性，涨停股{up_limit_count}只，跌停股{down_limit_count}只"
        elif up_limit_ratio >= 0.2:
            score = 30
            description = f"市场情绪悲观，涨停股{up_limit_count}只，跌停股{down_limit_count}只"
        else:
            score = 15
            description = f"市场情绪极度悲观，涨停股{up_limit_count}只，跌停股{down_limit_count}只"
        
        return {'score': score, 'description': description}
    
    def analyze_fund_flow_sentiment(self, fund_flow_data):
        """分析资金流向的市场情绪"""
        if not fund_flow_data:
            return {'score': 50, 'description': '无资金流向数据'}
        
        net_inflow = fund_flow_data.get('net_inflow', 0)
        main_inflow = fund_flow_data.get('main_inflow', 0)
        
        # 主力资金净流入情况
        if main_inflow > 100:  # 主力净流入超过100亿
            score = 80
            description = f"主力资金大幅净流入{main_inflow:.1f}亿，市场情绪积极"
        elif main_inflow > 50:
            score = 70
            description = f"主力资金净流入{main_inflow:.1f}亿，市场情绪较好"
        elif main_inflow > 0:
            score = 60
            description = f"主力资金净流入{main_inflow:.1f}亿，市场情绪偏乐观"
        elif main_inflow > -50:
            score = 40
            description = f"主力资金净流出{abs(main_inflow):.1f}亿，市场情绪偏悲观"
        elif main_inflow > -100:
            score = 30
            description = f"主力资金净流出{abs(main_inflow):.1f}亿，市场情绪较差"
        else:
            score = 20
            description = f"主力资金大幅净流出{abs(main_inflow):.1f}亿，市场情绪低迷"
        
        return {'score': score, 'description': description}
    
    def analyze_sector_sentiment(self, sector_data):
        """分析板块表现的市场情绪"""
        if not sector_data:
            return {'score': 50, 'description': '无板块数据'}
        
        rising_sectors = len([s for s in sector_data if s.get('change_percent', 0) > 0])
        total_sectors = len(sector_data)
        
        if total_sectors == 0:
            return {'score': 50, 'description': '无板块数据'}
        
        rising_ratio = rising_sectors / total_sectors
        
        if rising_ratio >= 0.8:
            score = 85
            description = f"板块普涨，{rising_sectors}/{total_sectors}个板块上涨，市场情绪高涨"
        elif rising_ratio >= 0.6:
            score = 70
            description = f"多数板块上涨，{rising_sectors}/{total_sectors}个板块上涨，市场情绪良好"
        elif rising_ratio >= 0.4:
            score = 50
            description = f"板块涨跌互现，{rising_sectors}/{total_sectors}个板块上涨，市场情绪中性"
        elif rising_ratio >= 0.2:
            score = 30
            description = f"多数板块下跌，{rising_sectors}/{total_sectors}个板块上涨，市场情绪低迷"
        else:
            score = 15
            description = f"板块普跌，{rising_sectors}/{total_sectors}个板块上涨，市场情绪极度悲观"
        
        return {'score': score, 'description': description}
    
    def analyze_volume_sentiment(self, volume_data):
        """分析成交量的市场情绪"""
        if not volume_data:
            return {'score': 50, 'description': '无成交量数据'}
        
        current_volume = volume_data.get('current_volume', 0)
        avg_volume = volume_data.get('avg_volume', 0)
        
        if avg_volume == 0:
            return {'score': 50, 'description': '无历史成交量数据'}
        
        volume_ratio = current_volume / avg_volume
        
        if volume_ratio >= 2.0:
            score = 80
            description = f"成交量放大{volume_ratio:.1f}倍，市场活跃度极高"
        elif volume_ratio >= 1.5:
            score = 70
            description = f"成交量放大{volume_ratio:.1f}倍，市场活跃度较高"
        elif volume_ratio >= 1.2:
            score = 60
            description = f"成交量温和放大{volume_ratio:.1f}倍，市场活跃度正常"
        elif volume_ratio >= 0.8:
            score = 50
            description = f"成交量正常，市场活跃度一般"
        elif volume_ratio >= 0.5:
            score = 40
            description = f"成交量萎缩{(1-volume_ratio)*100:.1f}%，市场活跃度较低"
        else:
            score = 30
            description = f"成交量严重萎缩{(1-volume_ratio)*100:.1f}%，市场活跃度低迷"
        
        return {'score': score, 'description': description}
    
    def calculate_comprehensive_sentiment(self, limit_data=None, fund_flow_data=None, 
                                        sector_data=None, volume_data=None):
        """计算综合市场情绪"""
        sentiment_components = []
        
        # 涨跌停情绪 (权重: 30%)
        if limit_data:
            limit_sentiment = self.analyze_limit_sentiment(limit_data)
            sentiment_components.append({
                'name': '涨跌停情绪',
                'score': limit_sentiment['score'],
                'weight': 0.3,
                'description': limit_sentiment['description']
            })
        
        # 资金流向情绪 (权重: 30%)
        if fund_flow_data:
            fund_sentiment = self.analyze_fund_flow_sentiment(fund_flow_data)
            sentiment_components.append({
                'name': '资金流向情绪',
                'score': fund_sentiment['score'],
                'weight': 0.3,
                'description': fund_sentiment['description']
            })
        
        # 板块表现情绪 (权重: 25%)
        if sector_data:
            sector_sentiment = self.analyze_sector_sentiment(sector_data)
            sentiment_components.append({
                'name': '板块表现情绪',
                'score': sector_sentiment['score'],
                'weight': 0.25,
                'description': sector_sentiment['description']
            })
        
        # 成交量情绪 (权重: 15%)
        if volume_data:
            volume_sentiment = self.analyze_volume_sentiment(volume_data)
            sentiment_components.append({
                'name': '成交量情绪',
                'score': volume_sentiment['score'],
                'weight': 0.15,
                'description': volume_sentiment['description']
            })
        
        # 计算加权平均分
        if sentiment_components:
            total_weight = sum([comp['weight'] for comp in sentiment_components])
            weighted_score = sum([comp['score'] * comp['weight'] for comp in sentiment_components])
            comprehensive_score = weighted_score / total_weight if total_weight > 0 else 50
        else:
            comprehensive_score = 50
        
        # 确定情绪等级和颜色
        if comprehensive_score >= 80:
            sentiment_level = "极度乐观"
            sentiment_color = "success"
            sentiment_icon = "ti-mood-happy"
        elif comprehensive_score >= 65:
            sentiment_level = "乐观"
            sentiment_color = "primary"
            sentiment_icon = "ti-mood-smile"
        elif comprehensive_score >= 45:
            sentiment_level = "中性"
            sentiment_color = "warning"
            sentiment_icon = "ti-mood-neutral"
        elif comprehensive_score >= 30:
            sentiment_level = "悲观"
            sentiment_color = "danger"
            sentiment_icon = "ti-mood-sad"
        else:
            sentiment_level = "极度悲观"
            sentiment_color = "dark"
            sentiment_icon = "ti-mood-cry"
        
        return {
            'comprehensive_score': round(comprehensive_score, 1),
            'sentiment_level': sentiment_level,
            'sentiment_color': sentiment_color,
            'sentiment_icon': sentiment_icon,
            'components': sentiment_components,
            'investment_suggestion': self._get_investment_suggestion(comprehensive_score)
        }
    
    def _get_investment_suggestion(self, score):
        """根据情绪分数给出投资建议"""
        if score >= 80:
            return {
                'suggestion': '市场情绪极度乐观，建议适当获利了结，控制仓位',
                'action': '减仓',
                'risk_level': '高'
            }
        elif score >= 65:
            return {
                'suggestion': '市场情绪乐观，可适当参与，但需注意风险控制',
                'action': '持有',
                'risk_level': '中'
            }
        elif score >= 45:
            return {
                'suggestion': '市场情绪中性，建议观望为主，等待明确信号',
                'action': '观望',
                'risk_level': '中'
            }
        elif score >= 30:
            return {
                'suggestion': '市场情绪悲观，可关注优质股票的建仓机会',
                'action': '轻仓',
                'risk_level': '中'
            }
        else:
            return {
                'suggestion': '市场情绪极度悲观，优质股票可能出现较好的建仓机会',
                'action': '建仓',
                'risk_level': '低'
            }
