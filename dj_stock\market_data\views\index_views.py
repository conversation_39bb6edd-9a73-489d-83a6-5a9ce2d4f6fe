# -*- coding: utf-8 -*-
# -*- coding: utf-8 -*-
"""
市场数据首页视图模块

本模块包含两个视图函数：
1. index: 基于函数的视图，展示市场概况、指数行情、涨跌榜等信息
2. IndexView: 基于类的视图，功能与index相同，但使用了Django的通用视图类
"""

# Django核心导入
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Avg
from django.db.models.functions import Abs
from django.views.generic import TemplateView
from django.utils import timezone

# Python标准库导入
from datetime import datetime, timedelta
import logging

# 应用模型导入
from .base_views import (
    StockBasic,  # 股票基本信息模型
    StockRiskWarning,  # 股票风险预警模型
    StockIPO,  # 新股发行模型
    StockDailyQuote,  # 股票每日行情模型
    StockMarketIndex,  # 市场指数模型
)

# 创建日志对象
logger = logging.getLogger(__name__)


# 常量定义
# 涨停跌停阈值
CREATIVE_BOARD_LIMIT_PCT = 19.5  # 创业板涨停阈值
MAIN_BOARD_LIMIT_PCT = 9.5  # 主板涨停阈值

# 常用指数代码
MAIN_INDICES = [
    "000001",  # 上证指数
    "399001",  # 深证成指
    "399006",  # 创业板指
    "000016",  # 上证50
    "000300",  # 沪深300
    "000905",  # 中证500
    "000852",  # 中证1000
    "399005",  # 中小100
]

# 主要指数代码
KEY_INDICES = [
    "000001.SH",  # 上证指数
    "399001.SZ",  # 深证成指
    "399006.SZ",  # 创业板指
]


# 辅助函数
def get_latest_trade_date():
    """获取最新交易日期

    Returns:
        date: 最新交易日期，如果没有数据则返回当前日期
    """
    latest_daily = StockDailyQuote.objects.order_by("-trade_date").first()
    return latest_daily.trade_date if latest_daily else timezone.now().date()


def get_limit_up_down_count(stock_daily_qs):
    """获取涨停跌停股票数量

    Args:
        stock_daily_qs: 股票每日行情查询集

    Returns:
        tuple: (涨停数量, 跌停数量)
    """
    # 创业板（股票代码以3开头）使用20%，其他使用10%
    limit_up_count = stock_daily_qs.filter(
        Q(
            stock_code__startswith="3", change_percent__gte=CREATIVE_BOARD_LIMIT_PCT
        )  # 创业板涨停
        | Q(
            ~Q(stock_code__startswith="3"), change_percent__gte=MAIN_BOARD_LIMIT_PCT
        )  # 主板涨停
    ).count()

    limit_down_count = stock_daily_qs.filter(
        Q(
            stock_code__startswith="3", change_percent__lte=-CREATIVE_BOARD_LIMIT_PCT
        )  # 创业板跌停
        | Q(
            ~Q(stock_code__startswith="3"), change_percent__lte=-MAIN_BOARD_LIMIT_PCT
        )  # 主板跌停
    ).count()

    return limit_up_count, limit_down_count


def get_industry_changes(stock_daily_qs, limit=10):
    """获取行业涨跌幅排名

    Args:
        stock_daily_qs: 股票每日行情查询集
        limit: 返回的行业数量限制

    Returns:
        list: 行业涨跌幅排名列表
    """
    # 首先获取股票代码和行业的映射
    stock_industry_map = {}
    for stock in StockBasic.objects.all():
        if stock.industry:  # 确保行业不为空
            stock_industry_map[stock.stock_code] = stock.industry

    # 然后将行业信息添加到每日行情数据中
    industry_data = {}
    for quote in stock_daily_qs:
        # 跳过change_percent为None的记录
        if quote.change_percent is None:
            continue

        industry = stock_industry_map.get(quote.stock_code)
        if industry:
            if industry not in industry_data:
                industry_data[industry] = {"total_change": 0, "count": 0}
            industry_data[industry]["total_change"] += quote.change_percent
            industry_data[industry]["count"] += 1

    # 计算平均涨跌幅并排序
    industry_changes = []
    for industry, data in industry_data.items():
        if data["count"] > 0:  # 避免除零错误
            avg_change = data["total_change"] / data["count"]
            industry_changes.append({"industry": industry, "avg_change": avg_change})

    # 按平均涨跌幅降序排序并取前指定数量
    return sorted(industry_changes, key=lambda x: x["avg_change"], reverse=True)[:limit]


def process_market_indices(indices_data):
    """处理市场指数数据，进行单位换算和格式化

    Args:
        indices_data: 市场指数查询集

    Returns:
        list: 处理后的市场指数列表
    """
    market_indices = []

    for index in indices_data:
        # 成交量转为亿手
        if index.volume:
            index.volume = float(index.volume) / 100000000
        # 成交额转为亿元
        if index.amount:
            index.amount = float(index.amount) / 100000000

        # 计算涨跌幅和涨跌额
        if index.close_price and index.pre_close and float(index.pre_close) > 0:
            index.change_percent = (
                (float(index.close_price) - float(index.pre_close))
                / float(index.pre_close)
                * 100
            )

        # 将涨跌幅转为百分比形式
        if hasattr(index, "change_percent") and index.change_percent is not None:
            index.change_percent_display = f"{index.change_percent:.2f}%"
        else:
            index.change_percent_display = "0.00%"

        # 振幅也转为百分比显示
        if index.amplitude is not None:
            index.amplitude_display = f"{float(index.amplitude):.2f}%"
        else:
            index.amplitude_display = "--"

        market_indices.append(index)

    return market_indices


def process_turnover_data(turnover_data):
    """处理成交额排行数据，进行单位换算

    Args:
        turnover_data: 成交额排行查询集

    Returns:
        list: 处理后的成交额排行列表
    """
    result = []

    for stock in turnover_data:
        # 处理成交量和成交额单位换算
        if stock.volume:
            stock.volume = float(stock.volume) / 10000  # 转为万手
        if stock.amount:
            stock.amount = float(stock.amount) / 100000000  # 转为亿元
        result.append(stock)

    return result


def calculate_market_sentiment(up_count, down_count, flat_count):
    """计算市场情绪统计数据

    Args:
        up_count: 上涨股票数量
        down_count: 下跌股票数量
        flat_count: 平盈股票数量

    Returns:
        dict: 市场情绪统计数据
    """
    total_count = up_count + down_count + flat_count
    if total_count <= 0:
        return {}

    return {
        "up_count": up_count,
        "down_count": down_count,
        "flat_count": flat_count,
        "up_percent": round(up_count / total_count * 100),
        "down_percent": round(down_count / total_count * 100),
        "flat_percent": round(flat_count / total_count * 100),
    }


def calculate_exchange_stats():
    """计算交易所分布统计

    Returns:
        dict: 交易所分布统计数据
    """
    return {
        "sh_count": StockBasic.objects.filter(
            Q(market__contains="沪")
            | Q(market__contains="上")
            | Q(stock_code__startswith="6")
        ).count(),
        "sz_count": StockBasic.objects.filter(
            Q(market__contains="深")
            | Q(stock_code__startswith="0")
            | Q(stock_code__startswith="3")
        ).count(),
        "bj_count": StockBasic.objects.filter(
            Q(market__contains="北") | Q(stock_code__startswith="8")
        ).count(),
    }


def index(request):
    """首页视图"""
    # 获取日期参数,如果没有则使用最新交易日
    trade_date_str = request.GET.get("trade_date")
    if trade_date_str:
        trade_date = datetime.strptime(trade_date_str, "%Y-%m-%d").date()
    else:
        # 获取最新交易日
        latest_daily = StockDailyQuote.objects.order_by("-trade_date").first()
        trade_date = latest_daily.trade_date if latest_daily else timezone.now().date()

    # 获取最新交易日期(用于限制日期选择器的最大值)
    latest_trade_date = (
        StockDailyQuote.objects.order_by("-trade_date").first().trade_date
    )

    # 获取指定日期的股票数据
    stock_daily_qs = StockDailyQuote.objects.filter(trade_date=trade_date)

    # 涨跌家数统计
    up_count = stock_daily_qs.filter(change_percent__gt=0).count()
    down_count = stock_daily_qs.filter(change_percent__lt=0).count()
    flat_count = stock_daily_qs.filter(change_percent=0).count()

    # 涨停跌停统计
    limit_up_count = stock_daily_qs.filter(is_limit_up=True).count()
    limit_down_count = stock_daily_qs.filter(is_limit_down=True).count()

    # 获取指数行情
    index_codes = [
        "000001.SH",
        "399001.SZ",
        "399006.SZ",
    ]  # 上证指数、深证成指、创业板指
    index_dailies = StockMarketIndex.objects.filter(
        trade_date=trade_date, code__in=index_codes
    ).select_related("index")

    # 获取行业涨跌幅排名
    industry_changes = (
        stock_daily_qs.values("stock__industry")
        .annotate(avg_change=Avg("change_percent"))
        .order_by("-avg_change")[:10]
    )

    # 获取最新的市场指数数据 - 添加更多常用指数
    market_indices = []
    main_indices = [
        "000001",  # 上证指数
        "399001",  # 深证成指
        "399006",  # 创业板指
        "000016",  # 上证50
        "000300",  # 沪深300
        "000905",  # 中证500
        "000852",  # 中证1000
        "399005",  # 中小100
    ]

    if trade_date:
        indices_data = StockMarketIndex.objects.filter(
            trade_date=trade_date, index_code__in=main_indices
        ).order_by("index_code")

        # 处理成交量和成交额单位换算
        for index in indices_data:
            # 成交量转为亿手
            if index.volume:
                index.volume = float(index.volume) / 100000000
            # 成交额转为亿元
            if index.amount:
                index.amount = float(index.amount) / 100000000

            # 计算涨跌幅和涨跌额 - 不需要*100
            if index.close_price and index.pre_close and float(index.pre_close) > 0:
                index.change_percent = (
                    (float(index.close_price) - float(index.pre_close))
                    / float(index.pre_close)
                    * 100
                )
                # 已有change_amount字段，无需再计算

            # 将涨跌幅转为百分比形式
            if hasattr(index, "change_percent") and index.change_percent is not None:
                index.change_percent_display = f"{index.change_percent:.2f}%"
            else:
                index.change_percent_display = "0.00%"

            # 振幅也转为百分比显示
            if index.amplitude is not None:
                index.amplitude_display = f"{float(index.amplitude):.2f}%"
            else:
                index.amplitude_display = "--"

            market_indices.append(index)

    # 获取上涨股票数量和下跌股票数量
    daily_quotes = StockDailyQuote.objects.filter(trade_date=trade_date)
    up_stocks = daily_quotes.filter(change_percent__gt=0).count()
    down_stocks = daily_quotes.filter(change_percent__lt=0).count()
    flat_stocks = daily_quotes.filter(change_percent=0).count()

    # 获取涨停和跌停股票数量
    # 创业板（股票代码以3开头）使用20%，其他使用10%
    limit_up_stocks = daily_quotes.filter(
        Q(stock_code__startswith="3", change_percent__gte=19.5)  # 创业板涨停
        | Q(~Q(stock_code__startswith="3"), change_percent__gte=9.5)  # 主板涨停
    ).count()

    limit_down_stocks = daily_quotes.filter(
        Q(stock_code__startswith="3", change_percent__lte=-19.5)  # 创业板跌停
        | Q(~Q(stock_code__startswith="3"), change_percent__lte=-9.5)  # 主板跌停
    ).count()

    # 打印涨停跌停调试信息
    print(f"\n=== 涨停跌停调试 ===")
    print(f"涨停数量: {limit_up_stocks}")
    print(f"跌停数量: {limit_down_stocks}")

    # 查看涨停股票列表（分别获取创业板和主板的涨停股）
    top_limit_up = daily_quotes.filter(
        Q(stock_code__startswith="3", change_percent__gte=19.5)
        | Q(~Q(stock_code__startswith="3"), change_percent__gte=9.5)
    ).order_by("-change_percent")[:5]

    for stock in top_limit_up:
        print(f"涨停股票: {stock.stock_code}, 涨幅: {float(stock.change_percent):.2f}%")

    # 获取涨幅榜和跌幅榜
    top_gainers = (
        StockDailyQuote.objects.filter(trade_date=trade_date)
        .filter(change_percent__gt=0)
        .annotate(abs_change=Abs("change_percent"))
        .order_by("-abs_change")[:5]
    )
    top_losers = (
        StockDailyQuote.objects.filter(trade_date=trade_date)
        .filter(change_percent__lt=0)
        .annotate(abs_change=Abs("change_percent"))
        .order_by("-abs_change")[:5]
    )

    # 获取股票名称并处理涨跌幅 - 不再乘以100
    for quote in top_gainers:
        try:
            # stock = StockBasic.objects.get(stock_code=quote.stock_code)
            # quote.stock_name = stock.stock_name
            # 不再需要将涨跌幅乘以100
            pass
        except StockBasic.DoesNotExist:
            quote.stock_name = "未知"

    for quote in top_losers:
        try:
            # stock = StockBasic.objects.get(stock_code=quote.stock_code)
            # quote.stock_name = stock.stock_name
            # 不再需要将涨跌幅乘以100
            pass
        except StockBasic.DoesNotExist:
            quote.stock_name = "未知"

    # 打印调试信息
    print("\n=== 涨跌幅数据 ===")
    print("涨幅榜:")
    for quote in top_gainers:
        print(f"{quote.stock_code} {quote.stock_name}: {quote.change_percent:.2f}%")
    print("\n跌幅榜:")
    for quote in top_losers:
        print(f"{quote.stock_code} {quote.stock_name}: {quote.change_percent:.2f}%")

    # 获取今日成交额前5的股票 - 按成交额从高到低排序
    top_turnover_data = daily_quotes.order_by("-amount")[:5]
    top_turnover = []

    for stock in top_turnover_data:
        # 处理成交量和成交额单位换算
        if stock.volume:
            stock.volume = float(stock.volume) / 10000  # 转为万手
        if stock.amount:
            stock.amount = float(stock.amount) / 100000000  # 转为亿元
        # 涨跌幅不再乘以100
        # 换手率不再乘以100
        top_turnover.append(stock)

    # 获取最新风险预警股票数量
    risk_count = StockRiskWarning.objects.filter(status="纳入").count()

    # 获取待发行新股数量
    ipo_count = StockIPO.objects.filter(listing_date__isnull=True).count()

    # 计算市场情绪统计数据
    market_sentiment = {}
    if trade_date:
        total_count = up_stocks + down_stocks + flat_stocks
        if total_count > 0:
            market_sentiment = {
                "up_count": up_stocks,
                "down_count": down_stocks,
                "flat_count": flat_stocks,
                "up_percent": round(up_stocks / total_count * 100),
                "down_percent": round(down_stocks / total_count * 100),
                "flat_percent": round(flat_stocks / total_count * 100),
            }

    # 计算交易所分布统计
    exchange_stats = {
        "sh_count": StockBasic.objects.filter(
            Q(market__contains="沪")
            | Q(market__contains="上")
            | Q(stock_code__startswith="6")
        ).count(),
        "sz_count": StockBasic.objects.filter(
            Q(market__contains="深")
            | Q(stock_code__startswith="0")
            | Q(stock_code__startswith="3")
        ).count(),
        "bj_count": StockBasic.objects.filter(
            Q(market__contains="北") | Q(stock_code__startswith="8")
        ).count(),
    }

    # 计算涨跌幅超过5%的股票数量
    up_5_count = daily_quotes.filter(change_percent__gt=5).count()
    down_5_count = daily_quotes.filter(change_percent__lt=-5).count()

    # 计算风险预警股票数量变化百分比（与上周比较）
    current_date = datetime.now().date()
    last_week = current_date - timedelta(days=7)
    risk_count_last_week = StockRiskWarning.objects.filter(
        status="纳入", update_time__date__lte=last_week
    ).count()
    if risk_count_last_week > 0:
        risk_change = round(
            ((risk_count - risk_count_last_week) / risk_count_last_week) * 100, 2
        )
    else:
        risk_change = 0

    context = {
        "trade_date": trade_date,
        "latest_trade_date": latest_trade_date,
        "up_count": up_count,
        "down_count": down_count,
        "flat_count": flat_count,
        "limit_up_count": limit_up_count,
        "limit_down_count": limit_down_count,
        "index_dailies": index_dailies,
        "industry_changes": industry_changes,
        "market_indices": market_indices,
        "up_stocks": up_stocks,
        "down_stocks": down_stocks,
        "flat_stocks": flat_stocks,
        "limit_up_stocks": limit_up_stocks,
        "limit_down_stocks": limit_down_stocks,
        "top_gainers": top_gainers,
        "top_losers": top_losers,
        "top_turnover": top_turnover,
        "risk_count": risk_count,
        "ipo_count": ipo_count,
        "market_sentiment": market_sentiment,
        "exchange_stats": exchange_stats,
        "up_5_count": up_5_count,
        "down_5_count": down_5_count,
        "risk_change": risk_change,
    }

    return render(request, "market_data/index.html", context)


class IndexView(TemplateView):
    """首页类视图

    基于 Django 的 TemplateView 类，展示市场概况、指数行情、涨跌榜等信息
    功能与 index 函数相同，但使用了基于类的视图实现
    """

    template_name = "market_data/index.html"

    def get_context_data(self, **kwargs):
        """获取模板上下文数据

        重写 TemplateView 的 get_context_data 方法，添加首页所需的数据

        Args:
            **kwargs: 从 URL 中捕获的关键字参数

        Returns:
            dict: 包含首页所需数据的上下文字典
        """
        # 首先调用父类的 get_context_data 方法
        context = super().get_context_data(**kwargs)

        # 1. 获取交易日期参数
        trade_date_str = self.request.GET.get("trade_date")
        if trade_date_str:
            # 如果有日期参数，则使用该日期
            trade_date = datetime.strptime(trade_date_str, "%Y-%m-%d").date()
        else:
            # 否则使用最新交易日
            trade_date = get_latest_trade_date()

        # 获取最新交易日期(用于限制日期选择器的最大值)
        latest_trade_date = get_latest_trade_date()

        # 2. 获取指定日期的股票数据
        stock_daily_qs = StockDailyQuote.objects.filter(trade_date=trade_date)

        # 3. 涨跌家数统计
        up_count = stock_daily_qs.filter(change_percent__gt=0).count()
        down_count = stock_daily_qs.filter(change_percent__lt=0).count()
        flat_count = stock_daily_qs.filter(change_percent=0).count()

        # 4. 涨停跌停统计
        limit_up_count, limit_down_count = get_limit_up_down_count(stock_daily_qs)

        # 5. 获取指数行情
        index_dailies = StockMarketIndex.objects.filter(
            trade_date=trade_date, index_code__in=KEY_INDICES
        ).select_related("index")

        # 6. 获取行业涨跌幅排名
        industry_changes = get_industry_changes(stock_daily_qs, limit=10)

        # 7. 获取市场指数数据
        indices_data = StockMarketIndex.objects.filter(
            trade_date=trade_date, index_code__in=MAIN_INDICES
        ).order_by("index_code")
        market_indices = process_market_indices(indices_data)

        # 8. 涨跌家数统计
        up_stocks = up_count
        down_stocks = down_count
        flat_stocks = flat_count
        limit_up_stocks = limit_up_count
        limit_down_stocks = limit_down_count

        # 9. 计算涨跌幅超过5%的股票数量
        up_5_count = stock_daily_qs.filter(change_percent__gt=5).count()
        down_5_count = stock_daily_qs.filter(change_percent__lt=-5).count()

        # 10. 获取涨幅榜和跌幅榜
        top_gainers = (
            stock_daily_qs.filter(change_percent__gt=0)
            .annotate(abs_change=Abs("change_percent"))
            .order_by("-abs_change")[:5]
        )

        top_losers = (
            stock_daily_qs.filter(change_percent__lt=0)
            .annotate(abs_change=Abs("change_percent"))
            .order_by("-abs_change")[:5]
        )

        # 11. 获取成交额排行
        top_turnover_data = stock_daily_qs.order_by("-amount")[:5]
        top_turnover = process_turnover_data(top_turnover_data)

        # 12. 获取风险预警和新股发行数据
        risk_count = StockRiskWarning.objects.filter(status="纳入").count()
        ipo_count = StockIPO.objects.filter(listing_date__isnull=True).count()

        # 13. 计算市场情绪统计数据
        market_sentiment = calculate_market_sentiment(
            up_stocks, down_stocks, flat_stocks
        )

        # 14. 计算交易所分布统计
        exchange_stats = calculate_exchange_stats()

        # 15. 计算风险预警股票数量变化百分比（与上周比较）
        current_date = datetime.now().date()
        last_week = current_date - timedelta(days=7)
        risk_count_last_week = StockRiskWarning.objects.filter(
            status="纳入", update_time__date__lte=last_week
        ).count()

        if risk_count_last_week > 0:
            risk_change = round(
                ((risk_count - risk_count_last_week) / risk_count_last_week) * 100, 2
            )
        else:
            risk_change = 0

        # 16. 构建模板上下文
        context.update(
            {
                # 交易日期信息
                "trade_date": trade_date,
                "latest_trade_date": latest_trade_date,
                # 涨跌家数统计
                "up_count": up_count,
                "down_count": down_count,
                "flat_count": flat_count,
                "up_stocks": up_stocks,
                "down_stocks": down_stocks,
                "flat_stocks": flat_stocks,
                # 涨停跌停统计
                "limit_up_count": limit_up_count,
                "limit_down_count": limit_down_count,
                "limit_up_stocks": limit_up_stocks,
                "limit_down_stocks": limit_down_stocks,
                # 涨跌幅超过5%的股票数量
                "up_5_count": up_5_count,
                "down_5_count": down_5_count,
                # 指数和行业数据
                "index_dailies": index_dailies,
                "industry_changes": industry_changes,
                "market_indices": market_indices,
                # 涨跌榜和成交额榜
                "top_gainers": top_gainers,
                "top_losers": top_losers,
                "top_turnover": top_turnover,
                # 风险预警和新股发行
                "risk_count": risk_count,
                "ipo_count": ipo_count,
                "risk_change": risk_change,
                # 市场情绪和交易所分布
                "market_sentiment": market_sentiment,
                "exchange_stats": exchange_stats,
            }
        )

        return context
