<!-- 行业资金流向图表组件 -->
<div class="card chart-container">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h3 class="card-title">
        <i class="ti ti-chart-line me-2 text-primary"></i>
        {{ sector_name }}资金流向趋势
      </h3>
      <div class="d-flex gap-2">
        <!-- 时间周期选择器 -->
        <select class="form-select form-select-sm" id="sector-fund-flow-period-select" style="width: auto">
          <option value="30">近30天</option>
          <option value="60" selected>近60天</option>
          <option value="90">近90天</option>
          <option value="180">近半年</option>
          <option value="365">近一年</option>
        </select>

        <!-- 图表类型选择器 -->
        <select class="form-select form-select-sm" id="sector-fund-flow-chart-type" style="width: auto">
          <option value="trend">趋势图</option>
          <option value="bar">柱状图</option>
          <option value="area">面积图</option>
        </select>
      </div>
    </div>
  </div>
  <div class="card-body">
    <!-- 图表容器 -->
    <div id="sector-fund-flow-chart" style="height: 400px"></div>
  </div>
</div>

<script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
<script>
  // 行业资金流向图表组件
  class SectorFundFlowChart {
    constructor(containerId, sectorName, options = {}) {
      this.containerId = containerId
      this.sectorName = sectorName
      this.chart = null
      this.currentPeriod = 60
      this.currentChartType = 'trend'
      this.data = []
      this.summary = {}

      this.options = {
        apiUrl: options.apiUrl || `/market_data/api/sector-fund-flow-trend/${encodeURIComponent(sectorName)}/`,
        sectorType: options.sectorType || '行业板块',
        showLegend: options.showLegend !== false,
        showToolbox: options.showToolbox !== false,
        colors: {
          mainInflow: '#ef4444', // 红色 - 主力净流入
          superBig: '#dc2626', // 深红 - 超大单
          big: '#f97316', // 橙色 - 大单
          medium: '#eab308', // 黄色 - 中单
          small: '#22c55e', // 绿色 - 小单
          changeRate: '#3b82f6', // 蓝色 - 涨跌幅
        },
        ...options,
      }

      this.init()
    }

    init() {
      this.chart = echarts.init(document.getElementById(this.containerId))
      this.bindEvents()
      this.loadData()
    }

    bindEvents() {
      // 时间周期选择
      const periodSelect = document.getElementById('sector-fund-flow-period-select')
      if (periodSelect) {
        periodSelect.addEventListener('change', (e) => {
          this.currentPeriod = parseInt(e.target.value)
          this.loadData()

          // 通知外部更新资金类型分布图
          if (this.options.onPeriodChange) {
            this.options.onPeriodChange(this.currentPeriod)
          }
        })
      }

      // 图表类型选择
      const chartTypeSelect = document.getElementById('sector-fund-flow-chart-type')
      if (chartTypeSelect) {
        chartTypeSelect.addEventListener('change', (e) => {
          this.currentChartType = e.target.value
          this.updateChart()
        })
      }

      // 窗口大小变化时重新调整图表
      window.addEventListener('resize', () => {
        if (this.chart) {
          this.chart.resize()
        }
      })
    }

    async loadData() {
      try {
        const url = `${this.options.apiUrl}?days=${this.currentPeriod}&sector_type=${encodeURIComponent(this.options.sectorType)}`
        console.log('正在请求API:', url)

        // 显示加载状态
        this.chart.showLoading('default', {
          text: '加载中...',
          color: '#c23531',
          textColor: '#000',
          maskColor: 'rgba(255, 255, 255, 0.8)',
          zlevel: 0,
        })

        const response = await fetch(url)
        console.log('API响应状态:', response.status)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('API响应数据:', result)

        // 隐藏加载状态
        this.chart.hideLoading()

        if (result.success) {
          this.data = result.data
          this.summary = result.summary || {}
          console.log('行业资金流向数据加载成功:', this.data.length, '条记录')
          console.log('数据示例:', this.data.slice(0, 3))
          this.updateChart()
        } else {
          console.error('API返回错误:', result.error)
          this.showError('加载数据失败: ' + result.error)
        }
      } catch (error) {
        console.error('网络请求失败:', error)
        this.chart.hideLoading()
        this.showError('网络请求失败: ' + error.message)
      }
    }

    updateChart() {
      if (!this.data || this.data.length === 0) {
        this.showNoData()
        return
      }

      const dates = this.data.map((item) => item.trade_date)
      const mainInflows = this.data.map((item) => item.main_net_inflow)
      const superBigInflows = this.data.map((item) => item.super_big_net_inflow)
      const bigInflows = this.data.map((item) => item.big_net_inflow)
      const mediumInflows = this.data.map((item) => item.medium_net_inflow)
      const smallInflows = this.data.map((item) => item.small_net_inflow)
      const changeRates = this.data.map((item) => item.net_inflow_rate)

      let option = {
        backgroundColor: 'transparent',
        title: {
          text: `${this.sectorName}资金流向趋势`,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
          },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
          formatter: this.getTooltipFormatter(),
        },
        legend: this.options.showLegend
          ? {
              data: ['主力净流入', '涨跌幅'],
              top: 30,
              textStyle: {
                fontSize: 12,
              },
            }
          : null,
        toolbox: this.options.showToolbox
          ? {
              feature: {
                dataZoom: {
                  yAxisIndex: 'none',
                },
                restore: {},
                saveAsImage: {},
              },
              right: 20,
            }
          : null,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: this.options.showLegend ? '15%' : '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            formatter: function (value) {
              return value.substring(5) // 只显示月-日
            },
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '资金流入(亿元)',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
              },
            },
          },
          {
            type: 'value',
            name: '涨跌幅(%)',
            position: 'right',
            axisLabel: {
              formatter: '{value}%',
            },
          },
        ],
        series: this.getSeriesConfig(mainInflows, superBigInflows, bigInflows, mediumInflows, smallInflows, changeRates),
      }

      this.chart.setOption(option, true)
    }

    getSeriesConfig(mainInflows, superBigInflows, bigInflows, mediumInflows, smallInflows, changeRates) {
      const baseConfig = {
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          width: 2,
        },
      }

      if (this.currentChartType === 'trend') {
        return [
          {
            name: '主力净流入',
            type: 'line',
            yAxisIndex: 0,
            data: mainInflows,
            itemStyle: { color: this.options.colors.mainInflow },
            lineStyle: { width: 3 }, // 加粗主力净流入线
            ...baseConfig,
          },
          {
            name: '涨跌幅',
            type: 'line',
            yAxisIndex: 1,
            data: changeRates,
            itemStyle: { color: this.options.colors.changeRate },
            lineStyle: { width: 2, type: 'dashed' }, // 虚线显示涨跌幅
            ...baseConfig,
          },
        ]
      } else if (this.currentChartType === 'bar') {
        return [
          {
            name: '主力净流入',
            type: 'bar',
            yAxisIndex: 0,
            data: mainInflows,
            itemStyle: {
              color: function (params) {
                return params.value >= 0 ? '#ef4444' : '#22c55e'
              },
            },
          },
        ]
      } else if (this.currentChartType === 'area') {
        return [
          {
            name: '主力净流入',
            type: 'line',
            yAxisIndex: 0,
            data: mainInflows,
            itemStyle: { color: this.options.colors.mainInflow },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(239, 68, 68, 0.3)' },
                { offset: 1, color: 'rgba(239, 68, 68, 0.05)' },
              ]),
            },
            ...baseConfig,
          },
        ]
      }
    }

    getTooltipFormatter() {
      const self = this
      return function (params) {
        const dataIndex = params[0].dataIndex
        const date = params[0].name
        const currentData = self.data[dataIndex]

        if (!currentData) return ''

        let html = `<div style="font-weight:bold;margin-bottom:8px;font-size:14px;">${date}</div>`

        // 主要指标
        const mainInflow = currentData.main_net_inflow
        const changeRate = currentData.net_inflow_rate

        html += `<div style="border-bottom:1px solid #eee;padding-bottom:6px;margin-bottom:6px;">`

        // 主力净流入
        const mainColor = mainInflow >= 0 ? '#ef4444' : '#22c55e'
        const mainSymbol = mainInflow >= 0 ? '+' : ''
        html += `<div style="margin-bottom:3px;display:flex;justify-content:space-between;">
          <span>主力净流入:</span>
          <span style="font-weight:bold;color:${mainColor}">
            ${mainSymbol}${mainInflow.toFixed(2)}亿
          </span>
        </div>`

        // 涨跌幅
        const rateColor = changeRate >= 0 ? '#ef4444' : '#22c55e'
        const rateSymbol = changeRate >= 0 ? '+' : ''
        html += `<div style="margin-bottom:3px;display:flex;justify-content:space-between;">
          <span>涨跌幅:</span>
          <span style="font-weight:bold;color:${rateColor}">
            ${rateSymbol}${changeRate.toFixed(2)}%
          </span>
        </div>`

        html += `</div>`

        // 资金类型分布
        html += `<div style="font-size:12px;color:#666;margin-bottom:4px;">资金类型分布:</div>`

        const fundTypes = [
          { name: '超大单', value: currentData.super_big_net_inflow, color: '#dc2626' },
          { name: '大单', value: currentData.big_net_inflow, color: '#f97316' },
          { name: '中单', value: currentData.medium_net_inflow, color: '#eab308' },
          { name: '小单', value: currentData.small_net_inflow, color: '#22c55e' },
        ]

        fundTypes.forEach((fund) => {
          const color = fund.value >= 0 ? fund.color : '#22c55e'
          const symbol = fund.value >= 0 ? '+' : ''
          html += `<div style="margin-bottom:2px;display:flex;justify-content:space-between;font-size:12px;">
            <span style="color:${fund.color};">● ${fund.name}:</span>
            <span style="color:${color};">
              ${symbol}${fund.value.toFixed(2)}亿
            </span>
          </div>`
        })

        return html
      }
    }

    showError(message) {
      if (this.chart) {
        this.chart.setOption(
          {
            title: {
              text: '加载失败',
              subtext: message,
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#dc3545',
                fontSize: 16,
              },
              subtextStyle: {
                color: '#6c757d',
                fontSize: 12,
              },
            },
            xAxis: { show: false },
            yAxis: { show: false },
            series: [],
          },
          true
        )
      }
    }

    showNoData() {
      if (this.chart) {
        this.chart.setOption(
          {
            title: {
              text: '暂无数据',
              subtext: `在选定的时间范围内没有找到${this.sectorName}的资金流向数据`,
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#6c757d',
                fontSize: 16,
              },
              subtextStyle: {
                color: '#adb5bd',
                fontSize: 12,
              },
            },
            xAxis: { show: false },
            yAxis: { show: false },
            series: [],
          },
          true
        )
      }
    }

    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    }

    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }

  // 全局函数，供外部调用
  window.initSectorFundFlowChart = function (containerId, sectorName, options) {
    return new SectorFundFlowChart(containerId, sectorName, options)
  }
</script>
