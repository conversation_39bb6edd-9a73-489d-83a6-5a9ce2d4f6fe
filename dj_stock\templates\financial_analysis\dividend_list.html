{% extends "base.html" %} {% load static %} {% block title %}分红送配 - 股票分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">分红送配</h2>
        <div class="text-muted mt-1">
          <span class="text-nowrap">共 {{ total_dividends }} 条记录</span>
        </div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-3">
            <div class="input-icon">
              <form method="get" class="d-flex">
                <input type="text" name="q" value="{{ search_query }}" class="form-control" placeholder="搜索股票代码或名称..." />
                <span class="input-icon-addon">
                  <i class="ti ti-search"></i>
                </span>
              </form>
            </div>
          </div>
          <div class="btn-list">
            <a href="?sort=registration_date" class="btn btn-outline-primary d-none d-sm-inline-block">
              <i class="ti ti-sort-ascending me-1"></i>
              登记日期升序
            </a>
            <a href="?sort=-registration_date" class="btn btn-primary d-none d-sm-inline-block">
              <i class="ti ti-sort-descending me-1"></i>
              登记日期降序
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="card mt-3">
  <div class="card-body">
    <div class="card mb-4 bg-blue-lt">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="avatar bg-blue-lt me-3">
            <i class="ti ti-info-circle"></i>
          </div>
          <div>
            <h3 class="mb-1">分红送配说明</h3>
            <div class="text-muted">
              <p class="mb-1">1. <strong>现金分红(元)：</strong>每10股派发的现金红利金额，以人民币元为单位</p>
              <p class="mb-1">2. <strong>股票股利(股)：</strong>每10股送股数量</p>
              <p class="mb-1">3. <strong>资本公积金转增(股)：</strong>每10股转增的股份数量</p>
              <p class="mb-0">4. <strong>方案进度：</strong>分红方案的实施状态，包括董事会预案、股东大会通过、实施中和已实施完成</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    {% if page_obj %}
    <div class="table-responsive">
      <table class="table table-vcenter card-table table-striped">
        <thead>
          <tr>
            <th>股票代码</th>
            <th>股票名称</th>
            <th class="text-center">现金分红<br />(元/10股)</th>
            <th class="text-center">股票股利<br />(股/10股)</th>
            <th class="text-center">资本公积金转增<br />(股/10股)</th>
            <th class="text-center">预案公告日</th>
            <th class="text-center">股权登记日</th>
            <th class="text-center">红利发放日</th>
            <th class="text-center">方案进度</th>
          </tr>
        </thead>
        <tbody>
          {% for dividend in page_obj %}
          <tr>
            <td class="font-monospace">
              <a href="{% url 'market_data:stock_detail' dividend.stock_code %}">{{ dividend.stock_code }}</a>
            </td>
            <td>{{ dividend.stock_name }}</td>
            <td class="text-center {% if dividend.cash_dividend > 0 %}text-up fw-bold{% endif %}">
              {% if dividend.cash_dividend %}{{ dividend.cash_dividend|floatformat:4 }}{% else %}-{% endif %}
            </td>
            <td class="text-center {% if dividend.share_dividend > 0 %}text-up fw-bold{% endif %}">
              {% if dividend.share_dividend %}{{ dividend.share_dividend|floatformat:2 }}{% else %}-{% endif %}
            </td>
            <td class="text-center {% if dividend.share_transfer > 0 %}text-up fw-bold{% endif %}">
              {% if dividend.share_transfer %}{{ dividend.share_transfer|floatformat:2 }}{% else %}-{% endif %}
            </td>
            <td class="text-center">{% if dividend.announcement_date %}{{ dividend.announcement_date|date:"Y-m-d" }}{% else %}-{% endif %}</td>
            <td class="text-center">{% if dividend.registration_date %}{{ dividend.registration_date|date:"Y-m-d" }}{% else %}-{% endif %}</td>
            <td class="text-center">{% if dividend.dividend_date %}{{ dividend.dividend_date|date:"Y-m-d" }}{% else %}-{% endif %}</td>
            <td class="text-center">
              {% if dividend.implementation_status == "董事会预案" %}
              <span class="badge bg-yellow-lt">{{ dividend.implementation_status }}</span>
              {% elif dividend.implementation_status == "股东大会通过" %}
              <span class="badge bg-purple-lt">{{ dividend.implementation_status }}</span>
              {% elif dividend.implementation_status == "实施中" %}
              <span class="badge bg-blue-lt">{{ dividend.implementation_status }}</span>
              {% elif dividend.implementation_status == "已实施完成" %}
              <span class="badge bg-green-lt">{{ dividend.implementation_status }}</span>
              {% else %}
              <span class="badge bg-muted-lt">{{ dividend.implementation_status|default:"-" }}</span>
              {% endif %}
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% else %}
    <div class="empty">
      <div class="empty-img">
        <i class="ti ti-coin icon-lg text-muted"></i>
      </div>
      <p class="empty-title">未找到分红记录</p>
      <p class="empty-subtitle text-muted">当前没有股票分红数据或没有符合搜索条件的记录。</p>
      <div class="empty-action">
        <a href="{% url 'financial_analysis:dividend_list' %}" class="btn btn-primary">
          <i class="ti ti-refresh me-1"></i>
          查看全部分红记录
        </a>
      </div>
    </div>
    {% endif %}
  </div>

  {% if page_obj.has_other_pages %}
  <div class="card-footer d-flex align-items-center">
    <p class="m-0 text-muted">
      显示 <span>{{ page_obj.start_index }}</span> 到 <span>{{ page_obj.end_index }}</span> 条，共 <span>{{ total_dividends }}</span> 条
    </p>
    <ul class="pagination m-0 ms-auto">
      {% if page_obj.has_previous %}
      <li class="page-item">
        <a
          class="page-link"
          href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if sort_order %}sort={{ sort_order }}&{% endif %}page=1"
          aria-label="First"
        >
          <i class="ti ti-chevrons-left"></i>
        </a>
      </li>
      <li class="page-item">
        <a
          class="page-link"
          href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if sort_order %}sort={{ sort_order }}&{% endif %}page={{ page_obj.previous_page_number }}"
          aria-label="Previous"
        >
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevrons-left"></i>
        </a>
      </li>
      <li class="page-item disabled">
        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>
      {% endif %} {% for i in page_obj.paginator.page_range %} {% if i == page_obj.number %}
      <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
      {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
      <li class="page-item">
        <a class="page-link" href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if sort_order %}sort={{ sort_order }}&{% endif %}page={{ i }}"
          >{{ i }}</a
        >
      </li>
      {% endif %} {% endfor %} {% if page_obj.has_next %}
      <li class="page-item">
        <a
          class="page-link"
          href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if sort_order %}sort={{ sort_order }}&{% endif %}page={{ page_obj.next_page_number }}"
          aria-label="Next"
        >
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
      <li class="page-item">
        <a
          class="page-link"
          href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if sort_order %}sort={{ sort_order }}&{% endif %}page={{ page_obj.paginator.num_pages }}"
          aria-label="Last"
        >
          <i class="ti ti-chevrons-right"></i>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
      <li class="page-item disabled">
        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevrons-right"></i>
        </a>
      </li>
      {% endif %}
    </ul>
  </div>
  {% endif %}
</div>
{% endblock %}
