# -*- coding: utf-8 -*-
"""
投资组合分析工具
提供投资组合的风险评估、收益分析、行业分布等功能
"""

from collections import defaultdict
from decimal import Decimal
import json


class PortfolioAnalyzer:
    """投资组合分析器"""
    
    def __init__(self, stocks_data):
        """
        初始化投资组合分析器
        :param stocks_data: 股票数据列表，包含股票代码、名称、行业、财务指标等
        """
        self.stocks_data = stocks_data
        
    def analyze_industry_distribution(self):
        """分析行业分布"""
        industry_stats = defaultdict(lambda: {'count': 0, 'weight': 0})
        total_count = len(self.stocks_data)
        
        for stock in self.stocks_data:
            industry = stock.get('industry', '未知')
            industry_stats[industry]['count'] += 1
            industry_stats[industry]['weight'] = industry_stats[industry]['count'] / total_count * 100
        
        # 转换为列表并排序
        distribution = []
        for industry, stats in industry_stats.items():
            distribution.append({
                'industry': industry,
                'count': stats['count'],
                'weight': round(stats['weight'], 2)
            })
        
        return sorted(distribution, key=lambda x: x['count'], reverse=True)
    
    def analyze_valuation_distribution(self):
        """分析估值分布"""
        pe_ranges = {
            '低估值(PE<15)': 0,
            '合理估值(15≤PE<25)': 0,
            '高估值(25≤PE<50)': 0,
            '极高估值(PE≥50)': 0,
            '亏损股票': 0
        }
        
        for stock in self.stocks_data:
            pe = stock.get('pe_ratio')
            if pe is None or pe <= 0:
                pe_ranges['亏损股票'] += 1
            elif pe < 15:
                pe_ranges['低估值(PE<15)'] += 1
            elif pe < 25:
                pe_ranges['合理估值(15≤PE<25)'] += 1
            elif pe < 50:
                pe_ranges['高估值(25≤PE<50)'] += 1
            else:
                pe_ranges['极高估值(PE≥50)'] += 1
        
        total = len(self.stocks_data)
        distribution = []
        for range_name, count in pe_ranges.items():
            if count > 0:
                distribution.append({
                    'range': range_name,
                    'count': count,
                    'percentage': round(count / total * 100, 2)
                })
        
        return distribution
    
    def analyze_quality_metrics(self):
        """分析质量指标"""
        metrics = {
            'high_roe_count': 0,  # ROE > 15%
            'high_growth_count': 0,  # 营收增长 > 20%
            'low_debt_count': 0,  # 负债率 < 50%
            'profitable_count': 0,  # 盈利股票
            'dividend_count': 0,  # 有分红记录
        }
        
        for stock in self.stocks_data:
            # ROE > 15%
            if stock.get('roe', 0) > 15:
                metrics['high_roe_count'] += 1
            
            # 营收增长 > 20%
            if stock.get('revenue_growth', 0) > 20:
                metrics['high_growth_count'] += 1
            
            # 负债率 < 50%
            if stock.get('debt_ratio', 100) < 50:
                metrics['low_debt_count'] += 1
            
            # 盈利股票
            if stock.get('net_profit', 0) > 0:
                metrics['profitable_count'] += 1
        
        total = len(self.stocks_data)
        quality_analysis = {}
        for metric, count in metrics.items():
            quality_analysis[metric] = {
                'count': count,
                'percentage': round(count / total * 100, 2)
            }
        
        return quality_analysis
    
    def calculate_portfolio_score(self):
        """计算投资组合综合评分"""
        if not self.stocks_data:
            return 0
        
        total_score = 0
        valid_stocks = 0
        
        for stock in self.stocks_data:
            stock_score = 0
            score_factors = 0
            
            # ROE评分 (权重: 25%)
            roe = stock.get('roe', 0)
            if roe > 20:
                stock_score += 25
            elif roe > 15:
                stock_score += 20
            elif roe > 10:
                stock_score += 15
            elif roe > 5:
                stock_score += 10
            score_factors += 25
            
            # 成长性评分 (权重: 25%)
            revenue_growth = stock.get('revenue_growth', 0)
            if revenue_growth > 30:
                stock_score += 25
            elif revenue_growth > 20:
                stock_score += 20
            elif revenue_growth > 10:
                stock_score += 15
            elif revenue_growth > 0:
                stock_score += 10
            score_factors += 25
            
            # 估值评分 (权重: 25%)
            pe = stock.get('pe_ratio', 0)
            if 0 < pe < 15:
                stock_score += 25
            elif pe < 25:
                stock_score += 20
            elif pe < 35:
                stock_score += 15
            elif pe < 50:
                stock_score += 10
            score_factors += 25
            
            # 财务健康度评分 (权重: 25%)
            debt_ratio = stock.get('debt_ratio', 100)
            if debt_ratio < 30:
                stock_score += 25
            elif debt_ratio < 50:
                stock_score += 20
            elif debt_ratio < 70:
                stock_score += 15
            elif debt_ratio < 90:
                stock_score += 10
            score_factors += 25
            
            if score_factors > 0:
                total_score += (stock_score / score_factors) * 100
                valid_stocks += 1
        
        return round(total_score / valid_stocks, 2) if valid_stocks > 0 else 0
    
    def get_risk_assessment(self):
        """获取风险评估"""
        portfolio_score = self.calculate_portfolio_score()
        industry_distribution = self.analyze_industry_distribution()
        
        # 计算行业集中度风险
        max_industry_weight = max([item['weight'] for item in industry_distribution]) if industry_distribution else 0
        
        # 风险等级评估
        if portfolio_score >= 80 and max_industry_weight < 30:
            risk_level = "低风险"
            risk_color = "success"
        elif portfolio_score >= 60 and max_industry_weight < 50:
            risk_level = "中等风险"
            risk_color = "warning"
        else:
            risk_level = "高风险"
            risk_color = "danger"
        
        return {
            'risk_level': risk_level,
            'risk_color': risk_color,
            'portfolio_score': portfolio_score,
            'max_industry_weight': max_industry_weight,
            'recommendations': self._get_risk_recommendations(portfolio_score, max_industry_weight)
        }
    
    def _get_risk_recommendations(self, portfolio_score, max_industry_weight):
        """获取风险建议"""
        recommendations = []
        
        if portfolio_score < 60:
            recommendations.append("建议优化投资组合，选择更高质量的股票")
        
        if max_industry_weight > 50:
            recommendations.append("行业集中度过高，建议分散投资到不同行业")
        
        if len(self.stocks_data) < 10:
            recommendations.append("持股数量较少，建议适当增加持股数量以分散风险")
        
        if not recommendations:
            recommendations.append("投资组合配置合理，继续保持")
        
        return recommendations
    
    def generate_optimization_suggestions(self):
        """生成优化建议"""
        suggestions = []
        
        # 分析行业分布
        industry_dist = self.analyze_industry_distribution()
        if industry_dist:
            max_weight = max([item['weight'] for item in industry_dist])
            if max_weight > 40:
                suggestions.append({
                    'type': '行业分散',
                    'message': f"当前{industry_dist[0]['industry']}行业占比{max_weight:.1f}%，建议分散到其他行业",
                    'priority': 'high'
                })
        
        # 分析估值分布
        valuation_dist = self.analyze_valuation_distribution()
        high_valuation = sum([item['count'] for item in valuation_dist if '高估值' in item['range']])
        if high_valuation > len(self.stocks_data) * 0.5:
            suggestions.append({
                'type': '估值优化',
                'message': "高估值股票占比较高，建议关注估值合理的优质股票",
                'priority': 'medium'
            })
        
        # 分析质量指标
        quality = self.analyze_quality_metrics()
        if quality['high_roe_count']['percentage'] < 50:
            suggestions.append({
                'type': '质量提升',
                'message': "高ROE股票占比较低，建议增加盈利能力强的股票",
                'priority': 'medium'
            })
        
        return suggestions
