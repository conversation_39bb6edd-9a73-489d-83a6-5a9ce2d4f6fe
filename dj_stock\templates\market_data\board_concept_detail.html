{% extends "base.html" %}

{% block title %}{{ board.board_name }} - 概念板块详情{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">{{ board.board_name }}</h2>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">板块信息</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-vcenter">
              <tr>
                <th>板块代码</th>
                <td>{{ board.board_code }}</td>
              </tr>
              <tr>
                <th>最新价格</th>
                <td>{{ board.latest_price|default:"-" }}</td>
              </tr>
              <tr>
                <th>涨跌幅</th>
                <td class="{% if board.change_percent > 0 %}text-danger{% elif board.change_percent < 0 %}text-success{% endif %}">
                  {{ board.change_percent|default:"-" }}%
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-vcenter">
              <tr>
                <th>涨跌额</th>
                <td class="{% if board.change_amount > 0 %}text-danger{% elif board.change_amount < 0 %}text-success{% endif %}">
                  {{ board.change_amount|default:"-" }}
                </td>
              </tr>
              <tr>
                <th>上涨/下跌家数</th>
                <td>
                  <span class="text-danger">{{ board.up_count|default:"0" }}</span> / 
                  <span class="text-success">{{ board.down_count|default:"0" }}</span>
                </td>
              </tr>
              <tr>
                <th>总市值</th>
                <td>{{ board.total_market_value|default:"-" }}亿</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 板块走势图表 -->
    {% include "components/universal_chart.html" with chart_title=board.board_name %}

    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title">成分股列表</h3>
      </div>
      <div class="card-body">
        <!-- 搜索和过滤表单 -->
        <form method="get" class="mb-3">
          <div class="row g-3">
            <div class="col-md-4">
              <input
                type="text"
                name="search"
                class="form-control"
                placeholder="搜索股票代码或名称"
                value="{{ search_query }}"
              />
            </div>
            <div class="col-md-3">
              <select name="sort" class="form-select">
                <option value="-change_percent" {% if sort_by == "-change_percent" %}selected{% endif %}>涨跌幅(高到低)</option>
                <option value="change_percent" {% if sort_by == "change_percent" %}selected{% endif %}>涨跌幅(低到高)</option>
                <option value="-volume" {% if sort_by == "-volume" %}selected{% endif %}>成交量(高到低)</option>
                <option value="volume" {% if sort_by == "volume" %}selected{% endif %}>成交量(低到高)</option>
                <option value="-amount" {% if sort_by == "-amount" %}selected{% endif %}>成交额(高到低)</option>
                <option value="amount" {% if sort_by == "amount" %}selected{% endif %}>成交额(低到高)</option>
                <option value="stock_code" {% if sort_by == "stock_code" %}selected{% endif %}>股票代码(升序)</option>
                <option value="-stock_code" {% if sort_by == "-stock_code" %}selected{% endif %}>股票代码(降序)</option>
                <option value="stock_name" {% if sort_by == "stock_name" %}selected{% endif %}>股票名称(升序)</option>
                <option value="-stock_name" {% if sort_by == "-stock_name" %}selected{% endif %}>股票名称(降序)</option>
              </select>
            </div>
            <div class="col-md-3">
              <select name="date" class="form-select">
                {% for available_date in available_dates %}
                  <option value="{{ available_date|date:'Y-m-d' }}" {% if date == available_date|date:'Y-m-d' %}selected{% endif %}>
                    {{ available_date|date:'Y-m-d' }}
                  </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-primary w-100">搜索</button>
            </div>
          </div>
        </form>

        <!-- 数据表格 -->
        <div class="table-responsive">
          <table class="table table-vcenter card-table">
            <thead>
              <tr>
                <th>股票代码</th>
                <th>股票名称</th>
                <th>最新价</th>
                <th>涨跌幅</th>
                <th>涨跌额</th>
                <th>成交量(万)</th>
                <th>成交额(亿)</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {% for item in page_obj %}
              <tr>
                <td>{{ item.stock_code }}</td>
                <td>{{ item.stock_name }}</td>
                <td>{{ item.latest_price|default:"-" }}</td>
                <td class="{% if item.change_percent > 0 %}text-danger{% elif item.change_percent < 0 %}text-success{% endif %} fw-bold">
                  {{ item.change_percent|default:"-" }}%
                </td>
                <td class="{% if item.change_amount > 0 %}text-danger{% elif item.change_amount < 0 %}text-success{% endif %} fw-bold">
                  {{ item.change_amount|default:"-" }}
                </td>
                <td>{{ item.volume|floatformat:2|default:"-" }}</td>
                <td>{{ item.amount|floatformat:2|default:"-" }}</td>
                <td>
                  <a href="{% url 'market_data:stock_detail' item.stock_code %}" class="btn btn-sm btn-primary">详情</a>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="8" class="text-center">暂无数据</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        {% include "includes/pagination.html" %}
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 动态加载ECharts，支持多个CDN源
function loadECharts() {
  return new Promise((resolve, reject) => {
    if (window.echarts) {
      resolve();
      return;
    }

    const cdnSources = [
      'https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js'
    ];

    let currentIndex = 0;

    function tryLoad() {
      if (currentIndex >= cdnSources.length) {
        reject(new Error('所有CDN源都加载失败'));
        return;
      }

      const script = document.createElement('script');
      script.src = cdnSources[currentIndex];
      script.onload = () => resolve();
      script.onerror = () => {
        currentIndex++;
        tryLoad();
      };
      document.head.appendChild(script);
    }

    tryLoad();
  });
}

document.addEventListener('DOMContentLoaded', function() {
  // 动态加载ECharts后初始化图表
  loadECharts().then(() => {
    // 初始化通用图表
    const chartData = {{ chart_data|safe }};

    if (chartData && chartData.length > 0) {
      window.initUniversalChart('universal-chart', chartData, {
        code: '{{ board.board_code }}',
        name: '{{ board.board_name }}',
        type: 'board',
        apiUrl: '/market_data/api/concept-board-chart/{{ board.board_code }}/'
      });
    }
  }).catch(error => {
    console.error('ECharts加载失败:', error);
    // 显示错误信息给用户
    const chartContainer = document.getElementById('universal-chart');
    if (chartContainer) {
      chartContainer.innerHTML = '<div class="alert alert-warning">图表加载失败，请刷新页面重试</div>';
    }
  });
});
</script>

<style>
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }

  .period-btn.active {
    background-color: #206bc4;
    border-color: #206bc4;
    color: white;
  }

  .ma-checkbox:checked + label {
    font-weight: bold;
  }
</style>
{% endblock %}