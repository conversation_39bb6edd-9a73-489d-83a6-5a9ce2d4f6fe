{"version": 3, "sources": ["tabler-flags.css"], "names": [], "mappings": "AAAA;;;;;EAKE;AACF;;;;;;EAME;AACF;EACE,kBAAkB;EAClB,qBAAqB;EACrB,cAAc;EACd,qBAAqB;EACrB,kCAAkC;EAClC,yCAAyC;EACzC,wCAAwC;EACxC,sBAAsB;AACxB;AACA;EACE,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,gDAAgD;AAClD;;AAEA;EACE,gDAAgD;AAClD;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,gDAAgD;AAClD;;AAEA;EACE,gDAAgD;AAClD;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,iDAAiD;AACnD;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,gDAAgD;AAClD;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,YAAY;AACd", "file": "tabler-flags.rtl.css", "sourcesContent": ["/**\n * Converts a given value to a percentage string.\n *\n * @param {Number} $value - The value to be converted to a percentage.\n * @return {String} - The percentage representation of the value.\n */\n/**\n * Generates a transparent version of the given color.\n *\n * @param {Color} $color - The base color to be made transparent.\n * @param {Number} $alpha - The level of transparency, ranging from 0 (fully transparent) to 1 (fully opaque). Default is 1.\n * @return {Color} - The resulting color with the specified transparency.\n */\n.flag {\n  position: relative;\n  display: inline-block;\n  height: 2.5rem;\n  aspect-ratio: 1.33333;\n  background: no-repeat center/cover;\n  box-shadow: var(--tblr-box-shadow-border);\n  border-radius: var(--tblr-border-radius);\n  vertical-align: bottom;\n}\n.flag.flag-country-np {\n  box-shadow: none;\n  border-radius: 0;\n}\n\n.flag-country-ad {\n  background-image: url(\"../img/flags/ad.svg\");\n}\n\n.flag-country-af {\n  background-image: url(\"../img/flags/af.svg\");\n}\n\n.flag-country-ae {\n  background-image: url(\"../img/flags/ae.svg\");\n}\n\n.flag-country-afrun {\n  background-image: url(\"../img/flags/afrun.svg\");\n}\n\n.flag-country-ag {\n  background-image: url(\"../img/flags/ag.svg\");\n}\n\n.flag-country-ai {\n  background-image: url(\"../img/flags/ai.svg\");\n}\n\n.flag-country-al {\n  background-image: url(\"../img/flags/al.svg\");\n}\n\n.flag-country-am {\n  background-image: url(\"../img/flags/am.svg\");\n}\n\n.flag-country-ao {\n  background-image: url(\"../img/flags/ao.svg\");\n}\n\n.flag-country-aq {\n  background-image: url(\"../img/flags/aq.svg\");\n}\n\n.flag-country-ar {\n  background-image: url(\"../img/flags/ar.svg\");\n}\n\n.flag-country-as {\n  background-image: url(\"../img/flags/as.svg\");\n}\n\n.flag-country-at {\n  background-image: url(\"../img/flags/at.svg\");\n}\n\n.flag-country-au {\n  background-image: url(\"../img/flags/au.svg\");\n}\n\n.flag-country-aw {\n  background-image: url(\"../img/flags/aw.svg\");\n}\n\n.flag-country-ax {\n  background-image: url(\"../img/flags/ax.svg\");\n}\n\n.flag-country-az {\n  background-image: url(\"../img/flags/az.svg\");\n}\n\n.flag-country-ba {\n  background-image: url(\"../img/flags/ba.svg\");\n}\n\n.flag-country-bb {\n  background-image: url(\"../img/flags/bb.svg\");\n}\n\n.flag-country-bd {\n  background-image: url(\"../img/flags/bd.svg\");\n}\n\n.flag-country-be {\n  background-image: url(\"../img/flags/be.svg\");\n}\n\n.flag-country-bf {\n  background-image: url(\"../img/flags/bf.svg\");\n}\n\n.flag-country-bg {\n  background-image: url(\"../img/flags/bg.svg\");\n}\n\n.flag-country-bh {\n  background-image: url(\"../img/flags/bh.svg\");\n}\n\n.flag-country-bi {\n  background-image: url(\"../img/flags/bi.svg\");\n}\n\n.flag-country-bj {\n  background-image: url(\"../img/flags/bj.svg\");\n}\n\n.flag-country-bl {\n  background-image: url(\"../img/flags/bl.svg\");\n}\n\n.flag-country-bm {\n  background-image: url(\"../img/flags/bm.svg\");\n}\n\n.flag-country-bn {\n  background-image: url(\"../img/flags/bn.svg\");\n}\n\n.flag-country-bo {\n  background-image: url(\"../img/flags/bo.svg\");\n}\n\n.flag-country-bq-bo {\n  background-image: url(\"../img/flags/bq-bo.svg\");\n}\n\n.flag-country-bq-sa {\n  background-image: url(\"../img/flags/bq-sa.svg\");\n}\n\n.flag-country-bq-se {\n  background-image: url(\"../img/flags/bq-se.svg\");\n}\n\n.flag-country-br {\n  background-image: url(\"../img/flags/br.svg\");\n}\n\n.flag-country-bs {\n  background-image: url(\"../img/flags/bs.svg\");\n}\n\n.flag-country-bt {\n  background-image: url(\"../img/flags/bt.svg\");\n}\n\n.flag-country-bv {\n  background-image: url(\"../img/flags/bv.svg\");\n}\n\n.flag-country-bw {\n  background-image: url(\"../img/flags/bw.svg\");\n}\n\n.flag-country-by {\n  background-image: url(\"../img/flags/by.svg\");\n}\n\n.flag-country-bz {\n  background-image: url(\"../img/flags/bz.svg\");\n}\n\n.flag-country-ca {\n  background-image: url(\"../img/flags/ca.svg\");\n}\n\n.flag-country-cc {\n  background-image: url(\"../img/flags/cc.svg\");\n}\n\n.flag-country-cd {\n  background-image: url(\"../img/flags/cd.svg\");\n}\n\n.flag-country-cf {\n  background-image: url(\"../img/flags/cf.svg\");\n}\n\n.flag-country-cg {\n  background-image: url(\"../img/flags/cg.svg\");\n}\n\n.flag-country-ch {\n  background-image: url(\"../img/flags/ch.svg\");\n}\n\n.flag-country-ci {\n  background-image: url(\"../img/flags/ci.svg\");\n}\n\n.flag-country-ck {\n  background-image: url(\"../img/flags/ck.svg\");\n}\n\n.flag-country-cl {\n  background-image: url(\"../img/flags/cl.svg\");\n}\n\n.flag-country-cm {\n  background-image: url(\"../img/flags/cm.svg\");\n}\n\n.flag-country-cn {\n  background-image: url(\"../img/flags/cn.svg\");\n}\n\n.flag-country-co {\n  background-image: url(\"../img/flags/co.svg\");\n}\n\n.flag-country-cr {\n  background-image: url(\"../img/flags/cr.svg\");\n}\n\n.flag-country-cu {\n  background-image: url(\"../img/flags/cu.svg\");\n}\n\n.flag-country-cv {\n  background-image: url(\"../img/flags/cv.svg\");\n}\n\n.flag-country-cw {\n  background-image: url(\"../img/flags/cw.svg\");\n}\n\n.flag-country-cx {\n  background-image: url(\"../img/flags/cx.svg\");\n}\n\n.flag-country-cy {\n  background-image: url(\"../img/flags/cy.svg\");\n}\n\n.flag-country-cz {\n  background-image: url(\"../img/flags/cz.svg\");\n}\n\n.flag-country-de {\n  background-image: url(\"../img/flags/de.svg\");\n}\n\n.flag-country-dj {\n  background-image: url(\"../img/flags/dj.svg\");\n}\n\n.flag-country-dk {\n  background-image: url(\"../img/flags/dk.svg\");\n}\n\n.flag-country-dm {\n  background-image: url(\"../img/flags/dm.svg\");\n}\n\n.flag-country-do {\n  background-image: url(\"../img/flags/do.svg\");\n}\n\n.flag-country-dz {\n  background-image: url(\"../img/flags/dz.svg\");\n}\n\n.flag-country-ec {\n  background-image: url(\"../img/flags/ec.svg\");\n}\n\n.flag-country-ee {\n  background-image: url(\"../img/flags/ee.svg\");\n}\n\n.flag-country-eg {\n  background-image: url(\"../img/flags/eg.svg\");\n}\n\n.flag-country-eh {\n  background-image: url(\"../img/flags/eh.svg\");\n}\n\n.flag-country-er {\n  background-image: url(\"../img/flags/er.svg\");\n}\n\n.flag-country-es {\n  background-image: url(\"../img/flags/es.svg\");\n}\n\n.flag-country-et {\n  background-image: url(\"../img/flags/et.svg\");\n}\n\n.flag-country-eu {\n  background-image: url(\"../img/flags/eu.svg\");\n}\n\n.flag-country-fi {\n  background-image: url(\"../img/flags/fi.svg\");\n}\n\n.flag-country-fj {\n  background-image: url(\"../img/flags/fj.svg\");\n}\n\n.flag-country-fk {\n  background-image: url(\"../img/flags/fk.svg\");\n}\n\n.flag-country-fm {\n  background-image: url(\"../img/flags/fm.svg\");\n}\n\n.flag-country-fo {\n  background-image: url(\"../img/flags/fo.svg\");\n}\n\n.flag-country-fr {\n  background-image: url(\"../img/flags/fr.svg\");\n}\n\n.flag-country-ga {\n  background-image: url(\"../img/flags/ga.svg\");\n}\n\n.flag-country-gb-eng {\n  background-image: url(\"../img/flags/gb-eng.svg\");\n}\n\n.flag-country-gb-sct {\n  background-image: url(\"../img/flags/gb-sct.svg\");\n}\n\n.flag-country-gb {\n  background-image: url(\"../img/flags/gb.svg\");\n}\n\n.flag-country-gb-wls {\n  background-image: url(\"../img/flags/gb-wls.svg\");\n}\n\n.flag-country-gb-nir {\n  background-image: url(\"../img/flags/gb-nir.svg\");\n}\n\n.flag-country-gd {\n  background-image: url(\"../img/flags/gd.svg\");\n}\n\n.flag-country-ge {\n  background-image: url(\"../img/flags/ge.svg\");\n}\n\n.flag-country-gf {\n  background-image: url(\"../img/flags/gf.svg\");\n}\n\n.flag-country-gg {\n  background-image: url(\"../img/flags/gg.svg\");\n}\n\n.flag-country-gh {\n  background-image: url(\"../img/flags/gh.svg\");\n}\n\n.flag-country-gi {\n  background-image: url(\"../img/flags/gi.svg\");\n}\n\n.flag-country-gl {\n  background-image: url(\"../img/flags/gl.svg\");\n}\n\n.flag-country-gm {\n  background-image: url(\"../img/flags/gm.svg\");\n}\n\n.flag-country-gn {\n  background-image: url(\"../img/flags/gn.svg\");\n}\n\n.flag-country-gp {\n  background-image: url(\"../img/flags/gp.svg\");\n}\n\n.flag-country-gq {\n  background-image: url(\"../img/flags/gq.svg\");\n}\n\n.flag-country-gr {\n  background-image: url(\"../img/flags/gr.svg\");\n}\n\n.flag-country-gs {\n  background-image: url(\"../img/flags/gs.svg\");\n}\n\n.flag-country-gt {\n  background-image: url(\"../img/flags/gt.svg\");\n}\n\n.flag-country-gu {\n  background-image: url(\"../img/flags/gu.svg\");\n}\n\n.flag-country-gw {\n  background-image: url(\"../img/flags/gw.svg\");\n}\n\n.flag-country-gy {\n  background-image: url(\"../img/flags/gy.svg\");\n}\n\n.flag-country-hk {\n  background-image: url(\"../img/flags/hk.svg\");\n}\n\n.flag-country-hm {\n  background-image: url(\"../img/flags/hm.svg\");\n}\n\n.flag-country-hn {\n  background-image: url(\"../img/flags/hn.svg\");\n}\n\n.flag-country-hr {\n  background-image: url(\"../img/flags/hr.svg\");\n}\n\n.flag-country-ht {\n  background-image: url(\"../img/flags/ht.svg\");\n}\n\n.flag-country-hu {\n  background-image: url(\"../img/flags/hu.svg\");\n}\n\n.flag-country-id {\n  background-image: url(\"../img/flags/id.svg\");\n}\n\n.flag-country-ie {\n  background-image: url(\"../img/flags/ie.svg\");\n}\n\n.flag-country-il {\n  background-image: url(\"../img/flags/il.svg\");\n}\n\n.flag-country-im {\n  background-image: url(\"../img/flags/im.svg\");\n}\n\n.flag-country-in {\n  background-image: url(\"../img/flags/in.svg\");\n}\n\n.flag-country-io {\n  background-image: url(\"../img/flags/io.svg\");\n}\n\n.flag-country-iq {\n  background-image: url(\"../img/flags/iq.svg\");\n}\n\n.flag-country-ir {\n  background-image: url(\"../img/flags/ir.svg\");\n}\n\n.flag-country-is {\n  background-image: url(\"../img/flags/is.svg\");\n}\n\n.flag-country-it {\n  background-image: url(\"../img/flags/it.svg\");\n}\n\n.flag-country-je {\n  background-image: url(\"../img/flags/je.svg\");\n}\n\n.flag-country-jm {\n  background-image: url(\"../img/flags/jm.svg\");\n}\n\n.flag-country-jo {\n  background-image: url(\"../img/flags/jo.svg\");\n}\n\n.flag-country-jp {\n  background-image: url(\"../img/flags/jp.svg\");\n}\n\n.flag-country-ke {\n  background-image: url(\"../img/flags/ke.svg\");\n}\n\n.flag-country-kg {\n  background-image: url(\"../img/flags/kg.svg\");\n}\n\n.flag-country-kh {\n  background-image: url(\"../img/flags/kh.svg\");\n}\n\n.flag-country-ki {\n  background-image: url(\"../img/flags/ki.svg\");\n}\n\n.flag-country-km {\n  background-image: url(\"../img/flags/km.svg\");\n}\n\n.flag-country-kn {\n  background-image: url(\"../img/flags/kn.svg\");\n}\n\n.flag-country-kp {\n  background-image: url(\"../img/flags/kp.svg\");\n}\n\n.flag-country-kr {\n  background-image: url(\"../img/flags/kr.svg\");\n}\n\n.flag-country-kw {\n  background-image: url(\"../img/flags/kw.svg\");\n}\n\n.flag-country-ky {\n  background-image: url(\"../img/flags/ky.svg\");\n}\n\n.flag-country-kz {\n  background-image: url(\"../img/flags/kz.svg\");\n}\n\n.flag-country-la {\n  background-image: url(\"../img/flags/la.svg\");\n}\n\n.flag-country-lb {\n  background-image: url(\"../img/flags/lb.svg\");\n}\n\n.flag-country-lc {\n  background-image: url(\"../img/flags/lc.svg\");\n}\n\n.flag-country-li {\n  background-image: url(\"../img/flags/li.svg\");\n}\n\n.flag-country-lk {\n  background-image: url(\"../img/flags/lk.svg\");\n}\n\n.flag-country-lr {\n  background-image: url(\"../img/flags/lr.svg\");\n}\n\n.flag-country-ls {\n  background-image: url(\"../img/flags/ls.svg\");\n}\n\n.flag-country-lt {\n  background-image: url(\"../img/flags/lt.svg\");\n}\n\n.flag-country-lu {\n  background-image: url(\"../img/flags/lu.svg\");\n}\n\n.flag-country-lv {\n  background-image: url(\"../img/flags/lv.svg\");\n}\n\n.flag-country-ly {\n  background-image: url(\"../img/flags/ly.svg\");\n}\n\n.flag-country-ma {\n  background-image: url(\"../img/flags/ma.svg\");\n}\n\n.flag-country-mc {\n  background-image: url(\"../img/flags/mc.svg\");\n}\n\n.flag-country-md {\n  background-image: url(\"../img/flags/md.svg\");\n}\n\n.flag-country-me {\n  background-image: url(\"../img/flags/me.svg\");\n}\n\n.flag-country-mf {\n  background-image: url(\"../img/flags/mf.svg\");\n}\n\n.flag-country-mg {\n  background-image: url(\"../img/flags/mg.svg\");\n}\n\n.flag-country-mh {\n  background-image: url(\"../img/flags/mh.svg\");\n}\n\n.flag-country-mk {\n  background-image: url(\"../img/flags/mk.svg\");\n}\n\n.flag-country-ml {\n  background-image: url(\"../img/flags/ml.svg\");\n}\n\n.flag-country-mm {\n  background-image: url(\"../img/flags/mm.svg\");\n}\n\n.flag-country-mn {\n  background-image: url(\"../img/flags/mn.svg\");\n}\n\n.flag-country-mo {\n  background-image: url(\"../img/flags/mo.svg\");\n}\n\n.flag-country-mp {\n  background-image: url(\"../img/flags/mp.svg\");\n}\n\n.flag-country-mq {\n  background-image: url(\"../img/flags/mq.svg\");\n}\n\n.flag-country-mr {\n  background-image: url(\"../img/flags/mr.svg\");\n}\n\n.flag-country-ms {\n  background-image: url(\"../img/flags/ms.svg\");\n}\n\n.flag-country-mt {\n  background-image: url(\"../img/flags/mt.svg\");\n}\n\n.flag-country-mu {\n  background-image: url(\"../img/flags/mu.svg\");\n}\n\n.flag-country-mv {\n  background-image: url(\"../img/flags/mv.svg\");\n}\n\n.flag-country-mw {\n  background-image: url(\"../img/flags/mw.svg\");\n}\n\n.flag-country-mx {\n  background-image: url(\"../img/flags/mx.svg\");\n}\n\n.flag-country-my {\n  background-image: url(\"../img/flags/my.svg\");\n}\n\n.flag-country-mz {\n  background-image: url(\"../img/flags/mz.svg\");\n}\n\n.flag-country-na {\n  background-image: url(\"../img/flags/na.svg\");\n}\n\n.flag-country-nc {\n  background-image: url(\"../img/flags/nc.svg\");\n}\n\n.flag-country-ne {\n  background-image: url(\"../img/flags/ne.svg\");\n}\n\n.flag-country-nf {\n  background-image: url(\"../img/flags/nf.svg\");\n}\n\n.flag-country-ng {\n  background-image: url(\"../img/flags/ng.svg\");\n}\n\n.flag-country-ni {\n  background-image: url(\"../img/flags/ni.svg\");\n}\n\n.flag-country-nl {\n  background-image: url(\"../img/flags/nl.svg\");\n}\n\n.flag-country-no {\n  background-image: url(\"../img/flags/no.svg\");\n}\n\n.flag-country-np {\n  background-image: url(\"../img/flags/np.svg\");\n}\n\n.flag-country-nr {\n  background-image: url(\"../img/flags/nr.svg\");\n}\n\n.flag-country-nu {\n  background-image: url(\"../img/flags/nu.svg\");\n}\n\n.flag-country-nz {\n  background-image: url(\"../img/flags/nz.svg\");\n}\n\n.flag-country-om {\n  background-image: url(\"../img/flags/om.svg\");\n}\n\n.flag-country-pa {\n  background-image: url(\"../img/flags/pa.svg\");\n}\n\n.flag-country-pe {\n  background-image: url(\"../img/flags/pe.svg\");\n}\n\n.flag-country-pf {\n  background-image: url(\"../img/flags/pf.svg\");\n}\n\n.flag-country-pg {\n  background-image: url(\"../img/flags/pg.svg\");\n}\n\n.flag-country-ph {\n  background-image: url(\"../img/flags/ph.svg\");\n}\n\n.flag-country-pk {\n  background-image: url(\"../img/flags/pk.svg\");\n}\n\n.flag-country-pl {\n  background-image: url(\"../img/flags/pl.svg\");\n}\n\n.flag-country-pm {\n  background-image: url(\"../img/flags/pm.svg\");\n}\n\n.flag-country-pn {\n  background-image: url(\"../img/flags/pn.svg\");\n}\n\n.flag-country-pr {\n  background-image: url(\"../img/flags/pr.svg\");\n}\n\n.flag-country-ps {\n  background-image: url(\"../img/flags/ps.svg\");\n}\n\n.flag-country-pt {\n  background-image: url(\"../img/flags/pt.svg\");\n}\n\n.flag-country-pw {\n  background-image: url(\"../img/flags/pw.svg\");\n}\n\n.flag-country-py {\n  background-image: url(\"../img/flags/py.svg\");\n}\n\n.flag-country-qa {\n  background-image: url(\"../img/flags/qa.svg\");\n}\n\n.flag-country-rainbow {\n  background-image: url(\"../img/flags/rainbow.svg\");\n}\n\n.flag-country-re {\n  background-image: url(\"../img/flags/re.svg\");\n}\n\n.flag-country-ro {\n  background-image: url(\"../img/flags/ro.svg\");\n}\n\n.flag-country-rs {\n  background-image: url(\"../img/flags/rs.svg\");\n}\n\n.flag-country-ru {\n  background-image: url(\"../img/flags/ru.svg\");\n}\n\n.flag-country-rw {\n  background-image: url(\"../img/flags/rw.svg\");\n}\n\n.flag-country-sa {\n  background-image: url(\"../img/flags/sa.svg\");\n}\n\n.flag-country-sb {\n  background-image: url(\"../img/flags/sb.svg\");\n}\n\n.flag-country-sc {\n  background-image: url(\"../img/flags/sc.svg\");\n}\n\n.flag-country-sd {\n  background-image: url(\"../img/flags/sd.svg\");\n}\n\n.flag-country-se {\n  background-image: url(\"../img/flags/se.svg\");\n}\n\n.flag-country-sg {\n  background-image: url(\"../img/flags/sg.svg\");\n}\n\n.flag-country-sh {\n  background-image: url(\"../img/flags/sh.svg\");\n}\n\n.flag-country-si {\n  background-image: url(\"../img/flags/si.svg\");\n}\n\n.flag-country-sj {\n  background-image: url(\"../img/flags/sj.svg\");\n}\n\n.flag-country-sk {\n  background-image: url(\"../img/flags/sk.svg\");\n}\n\n.flag-country-sl {\n  background-image: url(\"../img/flags/sl.svg\");\n}\n\n.flag-country-sm {\n  background-image: url(\"../img/flags/sm.svg\");\n}\n\n.flag-country-sn {\n  background-image: url(\"../img/flags/sn.svg\");\n}\n\n.flag-country-so {\n  background-image: url(\"../img/flags/so.svg\");\n}\n\n.flag-country-sr {\n  background-image: url(\"../img/flags/sr.svg\");\n}\n\n.flag-country-ss {\n  background-image: url(\"../img/flags/ss.svg\");\n}\n\n.flag-country-st {\n  background-image: url(\"../img/flags/st.svg\");\n}\n\n.flag-country-sv {\n  background-image: url(\"../img/flags/sv.svg\");\n}\n\n.flag-country-sx {\n  background-image: url(\"../img/flags/sx.svg\");\n}\n\n.flag-country-sy {\n  background-image: url(\"../img/flags/sy.svg\");\n}\n\n.flag-country-sz {\n  background-image: url(\"../img/flags/sz.svg\");\n}\n\n.flag-country-tc {\n  background-image: url(\"../img/flags/tc.svg\");\n}\n\n.flag-country-td {\n  background-image: url(\"../img/flags/td.svg\");\n}\n\n.flag-country-tf {\n  background-image: url(\"../img/flags/tf.svg\");\n}\n\n.flag-country-tg {\n  background-image: url(\"../img/flags/tg.svg\");\n}\n\n.flag-country-th {\n  background-image: url(\"../img/flags/th.svg\");\n}\n\n.flag-country-tj {\n  background-image: url(\"../img/flags/tj.svg\");\n}\n\n.flag-country-tk {\n  background-image: url(\"../img/flags/tk.svg\");\n}\n\n.flag-country-tl {\n  background-image: url(\"../img/flags/tl.svg\");\n}\n\n.flag-country-tm {\n  background-image: url(\"../img/flags/tm.svg\");\n}\n\n.flag-country-tn {\n  background-image: url(\"../img/flags/tn.svg\");\n}\n\n.flag-country-to {\n  background-image: url(\"../img/flags/to.svg\");\n}\n\n.flag-country-tr {\n  background-image: url(\"../img/flags/tr.svg\");\n}\n\n.flag-country-tt {\n  background-image: url(\"../img/flags/tt.svg\");\n}\n\n.flag-country-tv {\n  background-image: url(\"../img/flags/tv.svg\");\n}\n\n.flag-country-tw {\n  background-image: url(\"../img/flags/tw.svg\");\n}\n\n.flag-country-tz {\n  background-image: url(\"../img/flags/tz.svg\");\n}\n\n.flag-country-ua {\n  background-image: url(\"../img/flags/ua.svg\");\n}\n\n.flag-country-ug {\n  background-image: url(\"../img/flags/ug.svg\");\n}\n\n.flag-country-um {\n  background-image: url(\"../img/flags/um.svg\");\n}\n\n.flag-country-unasur {\n  background-image: url(\"../img/flags/unasur.svg\");\n}\n\n.flag-country-us {\n  background-image: url(\"../img/flags/us.svg\");\n}\n\n.flag-country-uy {\n  background-image: url(\"../img/flags/uy.svg\");\n}\n\n.flag-country-uz {\n  background-image: url(\"../img/flags/uz.svg\");\n}\n\n.flag-country-va {\n  background-image: url(\"../img/flags/va.svg\");\n}\n\n.flag-country-vc {\n  background-image: url(\"../img/flags/vc.svg\");\n}\n\n.flag-country-ve {\n  background-image: url(\"../img/flags/ve.svg\");\n}\n\n.flag-country-vg {\n  background-image: url(\"../img/flags/vg.svg\");\n}\n\n.flag-country-vi {\n  background-image: url(\"../img/flags/vi.svg\");\n}\n\n.flag-country-vn {\n  background-image: url(\"../img/flags/vn.svg\");\n}\n\n.flag-country-vu {\n  background-image: url(\"../img/flags/vu.svg\");\n}\n\n.flag-country-wf {\n  background-image: url(\"../img/flags/wf.svg\");\n}\n\n.flag-country-ws {\n  background-image: url(\"../img/flags/ws.svg\");\n}\n\n.flag-country-ye {\n  background-image: url(\"../img/flags/ye.svg\");\n}\n\n.flag-country-za {\n  background-image: url(\"../img/flags/za.svg\");\n}\n\n.flag-country-zm {\n  background-image: url(\"../img/flags/zm.svg\");\n}\n\n.flag-country-zw {\n  background-image: url(\"../img/flags/zw.svg\");\n}\n\n.flag-xxs {\n  height: 1rem;\n}\n\n.flag-xs {\n  height: 1.25rem;\n}\n\n.flag-sm {\n  height: 2rem;\n}\n\n.flag-md {\n  height: 2.5rem;\n}\n\n.flag-lg {\n  height: 3rem;\n}\n\n.flag-xl {\n  height: 5rem;\n}\n\n.flag-2xl {\n  height: 7rem;\n}\n"]}