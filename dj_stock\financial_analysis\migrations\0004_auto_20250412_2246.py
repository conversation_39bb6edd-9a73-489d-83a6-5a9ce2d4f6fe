# Generated by Django 5.1.7 on 2025-04-12 14:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("financial_analysis", "0003_stockindustrype_stockshareholderdetail_and_more"),
    ]

    operations = [
        # 添加资产负债表数据字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="total_assets",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="总资产",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="net_assets",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="净资产(股东权益合计)",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="goodwill",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="商誉",
            ),
        ),
        # 添加现金流量表数据字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="operating_cash_flow",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="经营现金流量净额",
            ),
        ),
        # 添加每股指标字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="diluted_eps",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="稀释每股收益",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="surplus_reserve",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="每股盈余公积金",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="retained_earnings",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="每股留存收益",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="cash_flow_per_share",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="每股现金流",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="free_cash_flow_per_share",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="每股企业自由现金流量",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="shareholder_free_cash_flow_per_share",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="每股股东自由现金流量",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="revenue_per_share",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="每股营业收入",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="ebit_per_share",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="每股息税前利润",
            ),
        ),
        # 添加盈利能力指标字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="operating_profit_margin",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="营业利润率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="ebit_margin",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="息税前利润率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="expense_ratio",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="期间费用率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="cost_expense_ratio",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="成本费用率",
            ),
        ),
        # 添加收益率指标字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="roa",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="总资产报酬率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="total_capital_return",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="总资本回报率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="invested_capital_return",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="投入资本回报率",
            ),
        ),
        # 添加现金流指标字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="cash_to_sales_ratio",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="经营活动净现金/销售收入",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="cash_to_revenue_ratio",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="经营性现金净流量/营业总收入",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="cash_to_profit_ratio",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="经营活动净现金/归属母公司的净利润",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="tax_to_profit_ratio",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="所得税/利润总额",
            ),
        ),
        # 添加营运能力指标字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="accounts_payable_turnover",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="应付账款周转率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="total_assets_turnover",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="总资产周转率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="total_assets_turnover_days",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="总资产周转天数",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="current_assets_turnover",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="流动资产周转率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="current_assets_turnover_days",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="流动资产周转天数",
            ),
        ),
        # 添加偿债能力指标字段
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="cash_ratio",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="现金比率",
            ),
        ),
        migrations.AddField(
            model_name="stockfinancialindicator",
            name="equity_multiplier",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="权益乘数",
            ),
        ),
    ]
