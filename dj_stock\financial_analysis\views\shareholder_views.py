# -*- coding: utf-8 -*-
"""
股东持股视图模块

本模块包含与股东持股相关的视图函数：
1. 股东持股明细
2. 行业市盈率数据
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Avg
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.utils import timezone
from django.views.generic import ListView
from datetime import datetime
import logging

from financial_analysis.models import (
    StockShareholderDetail,
    StockIndustryPE,
    StockShareholderStatistics,
)
from market_data.models import StockBasic

logger = logging.getLogger(__name__)


def shareholder_detail_list(request):
    """股东持股明细列表视图

    展示股东持股明细数据，包括股东名称、持股数量、持股变动等

    Args:
        request: HTTP请求对象

    Returns:
        HttpResponse: 渲染后的股东持股明细列表页面
    """
    # 获取报告期参数
    period_str = request.GET.get("period")
    if period_str:
        selected_period = datetime.strptime(period_str, "%Y-%m-%d").date()
    else:
        # 默认使用最新报告期
        latest_data = StockShareholderDetail.objects.order_by("-report_period").first()
        selected_period = (
            latest_data.report_period if latest_data else timezone.now().date()
        )

    # 获取搜索参数
    search_query = request.GET.get("q", "")

    # 获取股东类型参数
    shareholder_type = request.GET.get("type", "")

    # 获取持股变动趋势参数
    holding_trend = request.GET.get("trend", "")

    # 构建查询条件
    query = Q(report_period=selected_period)
    if search_query:
        query &= (
            Q(stock_code__icontains=search_query)
            | Q(stock_name__icontains=search_query)
            | Q(shareholder_name__icontains=search_query)
        )
    if shareholder_type:
        query &= Q(shareholder_type=shareholder_type)
    if holding_trend:
        query &= Q(holding_trend=holding_trend)

    # 获取排序参数
    sort_by = request.GET.get("sort", "-holding_amount")

    # 获取股东持股明细数据
    shareholder_data = StockShareholderDetail.objects.filter(query).order_by(sort_by)

    # 分页处理
    page = request.GET.get("page", 1)
    paginator = Paginator(shareholder_data, 20)

    try:
        shareholders = paginator.page(page)
    except PageNotAnInteger:
        shareholders = paginator.page(1)
    except EmptyPage:
        shareholders = paginator.page(paginator.num_pages)

    # 获取最新报告期（用于日期选择器的最大值）
    latest_data = StockShareholderDetail.objects.order_by("-report_period").first()
    latest_period = latest_data.report_period if latest_data else timezone.now().date()

    # 获取可用的股东类型和持股变动趋势
    shareholder_types = StockShareholderDetail.objects.values_list(
        "shareholder_type", flat=True
    ).distinct()
    holding_trends = StockShareholderDetail.objects.values_list(
        "holding_trend", flat=True
    ).distinct()

    context = {
        "shareholders": shareholders,
        "selected_period": selected_period,
        "latest_period": latest_period,
        "search_query": search_query,
        "shareholder_type": shareholder_type,
        "shareholder_types": shareholder_types,
        "holding_trend": holding_trend,
        "holding_trends": holding_trends,
        "sort_by": sort_by,
    }

    return render(request, "financial_analysis/shareholder_detail_list.html", context)


def shareholder_detail_by_stock(request, code):
    """特定股票的股东持股明细视图

    展示特定股票的股东持股明细数据

    Args:
        request: HTTP请求对象
        code: 股票代码

    Returns:
        HttpResponse: 渲染后的股票股东持股明细页面
    """
    # 获取股票基本信息
    stock = get_object_or_404(StockBasic, stock_code=code)

    # 获取报告期参数
    period_str = request.GET.get("period")
    if period_str:
        selected_period = datetime.strptime(period_str, "%Y-%m-%d").date()
    else:
        # 默认使用最新报告期
        latest_data = (
            StockShareholderDetail.objects.filter(stock_code=code)
            .order_by("-report_period")
            .first()
        )
        selected_period = (
            latest_data.report_period if latest_data else timezone.now().date()
        )

    # 获取股东类型参数
    shareholder_type = request.GET.get("type", "")

    # 构建查询条件
    query = Q(stock_code=code, report_period=selected_period)
    if shareholder_type:
        query &= Q(shareholder_type=shareholder_type)

    # 获取股东持股明细数据
    shareholder_data = StockShareholderDetail.objects.filter(query).order_by(
        "-holding_amount"
    )

    # 获取可用的报告期
    available_periods = (
        StockShareholderDetail.objects.filter(stock_code=code)
        .values_list("report_period", flat=True)
        .distinct()
        .order_by("-report_period")
    )

    # 获取可用的股东类型
    shareholder_types = (
        StockShareholderDetail.objects.filter(stock_code=code)
        .values_list("shareholder_type", flat=True)
        .distinct()
    )

    # 获取股东统计数据
    # 注意：StockShareholderStatistics 模型没有 stock_code 字段
    # 我们需要通过股东持股明细来获取与该股票相关的股东
    shareholder_names = (
        StockShareholderDetail.objects.filter(stock_code=code)
        .values_list("shareholder_name", flat=True)
        .distinct()
    )

    # 然后查询这些股东的统计数据
    try:
        shareholder_stats = (
            StockShareholderStatistics.objects.filter(
                shareholder_name__in=shareholder_names
            )
            .order_by("-report_date")
            .first()
        )
    except StockShareholderStatistics.DoesNotExist:
        shareholder_stats = None

    context = {
        "stock": stock,
        "shareholder_data": shareholder_data,
        "selected_period": selected_period,
        "available_periods": available_periods,
        "shareholder_type": shareholder_type,
        "shareholder_types": shareholder_types,
        "shareholder_stats": shareholder_stats,
    }

    return render(
        request, "financial_analysis/shareholder_detail_by_stock.html", context
    )


def industry_pe_list(request):
    """行业市盈率数据视图

    展示各行业的市盈率、市净率等估值数据

    Args:
        request: HTTP请求对象

    Returns:
        HttpResponse: 渲染后的行业市盈率数据页面
    """
    # 获取日期参数
    date_str = request.GET.get("date")
    if date_str:
        selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    else:
        # 默认使用最新交易日
        latest_data = StockIndustryPE.objects.order_by("-trade_date").first()
        selected_date = latest_data.trade_date if latest_data else timezone.now().date()

    # 获取行业分类标准参数
    industry_type = request.GET.get("type", "")

    # 获取排序参数
    sort_by = request.GET.get("sort", "pe_ttm")

    # 构建查询条件
    query = Q(trade_date=selected_date)
    if industry_type:
        query &= Q(industry_type=industry_type)

    # 获取行业市盈率数据
    industry_pe_data = StockIndustryPE.objects.filter(query).order_by(sort_by)

    # 分页处理
    page = request.GET.get("page", 1)
    paginator = Paginator(industry_pe_data, 20)

    try:
        industries = paginator.page(page)
    except PageNotAnInteger:
        industries = paginator.page(1)
    except EmptyPage:
        industries = paginator.page(paginator.num_pages)

    # 获取最新交易日期（用于日期选择器的最大值）
    latest_data = StockIndustryPE.objects.order_by("-trade_date").first()
    latest_date = latest_data.trade_date if latest_data else timezone.now().date()

    # 获取可用的行业分类标准
    industry_types = StockIndustryPE.objects.values_list(
        "industry_type", flat=True
    ).distinct()

    # 计算平均值
    avg_pe = StockIndustryPE.objects.filter(trade_date=selected_date).aggregate(
        Avg("pe_ttm")
    )["pe_ttm__avg"]
    avg_pb = StockIndustryPE.objects.filter(trade_date=selected_date).aggregate(
        Avg("pb")
    )["pb__avg"]

    context = {
        "industries": industries,
        "selected_date": selected_date,
        "latest_date": latest_date,
        "industry_type": industry_type,
        "industry_types": industry_types,
        "sort_by": sort_by,
        "avg_pe": avg_pe,
        "avg_pb": avg_pb,
    }

    return render(request, "financial_analysis/industry_pe_list.html", context)


class IndustryPEListView(ListView):
    """行业市盈率数据类视图

    展示各行业的市盈率、市净率等估值数据
    """

    model = StockIndustryPE
    template_name = "financial_analysis/industry_pe_list_class.html"
    context_object_name = "industries"
    paginate_by = 20

    def get_queryset(self):
        # 获取日期参数
        date_str = self.request.GET.get("date")
        if date_str:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        else:
            # 默认使用最新交易日
            latest_data = StockIndustryPE.objects.order_by("-trade_date").first()
            selected_date = (
                latest_data.trade_date if latest_data else timezone.now().date()
            )

        # 获取行业分类标准参数
        industry_type = self.request.GET.get("type", "")

        # 获取排序参数
        sort_by = self.request.GET.get("sort", "pe_ttm")

        # 构建查询条件
        query = Q(trade_date=selected_date)
        if industry_type:
            query &= Q(industry_type=industry_type)

        # 返回查询结果
        return StockIndustryPE.objects.filter(query).order_by(sort_by)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取日期参数
        date_str = self.request.GET.get("date")
        if date_str:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        else:
            # 默认使用最新交易日
            latest_data = StockIndustryPE.objects.order_by("-trade_date").first()
            selected_date = (
                latest_data.trade_date if latest_data else timezone.now().date()
            )

        # 获取行业分类标准参数
        industry_type = self.request.GET.get("type", "")

        # 获取排序参数
        sort_by = self.request.GET.get("sort", "pe_ttm")

        # 获取最新交易日期（用于日期选择器的最大值）
        latest_data = StockIndustryPE.objects.order_by("-trade_date").first()
        latest_date = latest_data.trade_date if latest_data else timezone.now().date()

        # 获取可用的行业分类标准
        industry_types = StockIndustryPE.objects.values_list(
            "industry_type", flat=True
        ).distinct()

        # 计算平均值
        avg_pe = StockIndustryPE.objects.filter(trade_date=selected_date).aggregate(
            Avg("pe_ttm")
        )["pe_ttm__avg"]
        avg_pb = StockIndustryPE.objects.filter(trade_date=selected_date).aggregate(
            Avg("pb")
        )["pb__avg"]

        # 添加额外的上下文数据
        context.update(
            {
                "selected_date": selected_date,
                "latest_date": latest_date,
                "industry_type": industry_type,
                "industry_types": industry_types,
                "sort_by": sort_by,
                "avg_pe": avg_pe,
                "avg_pb": avg_pb,
            }
        )

        return context
