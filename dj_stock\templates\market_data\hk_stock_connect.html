{% extends 'base.html' %}

{% block title %}港股通成份股 - 股票市场数据{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          港股通成份股
        </h2>
        <div class="text-muted mt-1">沪深港通下的港股通成份股列表</div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 筛选条件卡片 -->
    <div class="card mb-3">
      <div class="card-header">
        <h3 class="card-title">筛选条件</h3>
      </div>
      <div class="card-body">
        <form method="get" class="row g-3">
          <div class="col-md-3">
            <label class="form-label">股票代码</label>
            <div class="input-icon">
              <span class="input-icon-addon">
                <i class="ti ti-number"></i>
              </span>
              <input type="text" class="form-control" name="stock_code" value="{{ stock_code }}" placeholder="输入股票代码">
            </div>
          </div>
          <div class="col-md-3">
            <label class="form-label">股票名称</label>
            <div class="input-icon">
              <span class="input-icon-addon">
                <i class="ti ti-building"></i>
              </span>
              <input type="text" class="form-control" name="stock_name" value="{{ stock_name }}" placeholder="输入股票名称">
            </div>
          </div>
          <div class="col-md-3">
            <label class="form-label">交易方向</label>
            <select class="form-select" name="direction">
              <option value="">全部</option>
              <option value="沪股通" {% if direction == '沪股通' %}selected{% endif %}>沪股通</option>
              <option value="深股通" {% if direction == '深股通' %}selected{% endif %}>深股通</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">纳入日期</label>
            <input type="date" class="form-control" name="date" value="{{ date }}" placeholder="选择日期">
          </div>
          <div class="col-12">
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ti ti-search me-1"></i>
                搜索
              </button>
              <a href="{% url 'market_data:hk_stock_connect' %}" class="btn btn-link">重置</a>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="row row-cards mb-3">
      <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-auto">
                <span class="bg-primary text-white avatar">
                  <i class="ti ti-file-analytics"></i>
                </span>
              </div>
              <div class="col">
                <div class="font-weight-medium">
                  总股票数
                </div>
                <div class="text-muted">
                  {{ page_obj.paginator.count }} 只
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-auto">
                <span class="bg-green text-white avatar">
                  <i class="ti ti-building-bank"></i>
                </span>
              </div>
              <div class="col">
                <div class="font-weight-medium">
                  沪股通
                </div>
                <div class="text-muted">
                  {{ sh_count|default:0 }} 只
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-auto">
                <span class="bg-azure text-white avatar">
                  <i class="ti ti-building-bank"></i>
                </span>
              </div>
              <div class="col">
                <div class="font-weight-medium">
                  深股通
                </div>
                <div class="text-muted">
                  {{ sz_count|default:0 }} 只
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-auto">
                <span class="bg-yellow text-white avatar">
                  <i class="ti ti-calendar"></i>
                </span>
              </div>
              <div class="col">
                <div class="font-weight-medium">
                  最新纳入
                </div>
                <div class="text-muted">
                  {{ latest_date|date:"Y-m-d"|default:"暂无" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据列表卡片 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">成份股列表</h3>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-vcenter table-hover card-table">
            <thead>
              <tr>
                <th>股票代码</th>
                <th>股票名称</th>
                <th>所属交易所</th>
                <th>上市日期</th>
                <th>纳入日期</th>
                <th class="text-center">上证50</th>
                <th class="text-center">沪深300</th>
                <th class="text-center">中证500</th>
                <th class="text-center">港股通</th>
                <th class="text-center">操作</th>
              </tr>
            </thead>
            <tbody>
              {% for item in page_obj %}
              <tr>
                <td class="text-nowrap fw-bold">{{ item.stock_code }}</td>
                <td>
                  <div class="d-flex align-items-center">
                    <span class="avatar avatar-xs me-2 bg-blue-lt">{{ item.stock_name|slice:":1" }}</span>
                    {{ item.stock_name }}
                  </div>
                </td>
                <td>{{ item.get_exchange_display }}</td>
                <td>{{ item.list_date|date:"Y-m-d" }}</td>
                <td>{{ item.hk_date|date:"Y-m-d" }}</td>
                <td class="text-center">
                  {% if item.is_sse50 %}
                    <span class="badge bg-green-lt">是</span>
                  {% else %}
                    <span class="badge bg-muted">否</span>
                  {% endif %}
                </td>
                <td class="text-center">
                  {% if item.is_hs300 %}
                    <span class="badge bg-green-lt">是</span>
                  {% else %}
                    <span class="badge bg-muted">否</span>
                  {% endif %}
                </td>
                <td class="text-center">
                  {% if item.is_zz500 %}
                    <span class="badge bg-green-lt">是</span>
                  {% else %}
                    <span class="badge bg-muted">否</span>
                  {% endif %}
                </td>
                <td class="text-center">
                  {% if item.is_hk %}
                    <span class="badge bg-green-lt">是</span>
                  {% else %}
                    <span class="badge bg-muted">否</span>
                  {% endif %}
                </td>
                <td class="text-center">
                  <a href="{% url 'market_data:stock_detail' item.stock_code %}" class="btn btn-sm btn-primary">
                    <i class="ti ti-chart-line me-1"></i>
                    详情
                  </a>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="10" class="text-center py-3">暂无数据</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% include "includes/pagination.html" with page_obj=page_obj %}
      </div>
    </div>

    <!-- 港股通介绍卡片 -->
    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title">港股通介绍</h3>
      </div>
      <div class="card-body">
        <div class="markdown">
          <h4>什么是港股通？</h4>
          <p>港股通是指内地投资者委托内地证券公司，通过上海证券交易所或深圳证券交易所设立的证券交易服务公司，向香港联合交易所进行申报，买卖规定范围内的香港联合交易所上市的股票。</p>
          
          <h4>港股通特点</h4>
          <ul>
            <li>交易时间与港股一致，但需要考虑内地与香港节假日安排</li>
            <li>交易以港币报价，但投资者使用人民币进行交易结算</li>
            <li>额度控制：每日额度和总额度限制</li>
            <li>投资标的限制：只能买卖规定范围内的股票</li>
          </ul>
          
          <h4>投资门槛</h4>
          <p>个人投资者参与港股通交易，证券账户及资金账户合计资产不低于人民币50万元。</p>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} 