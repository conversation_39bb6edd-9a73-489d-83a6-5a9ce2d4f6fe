/*
 * 增强版市场数据小部件样式
 * 优化市场情绪、市场热点、资金流向和市场估值等UI组件
 */

/* 通用卡片样式 */
.market-widget-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: none;
  transition: all 0.25s ease;
  overflow: hidden;
  height: 100%;
}

.market-widget-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.market-widget-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.6rem 1rem;
}

.market-widget-card .card-body {
  padding: 0.8rem;
}

/* 市场热点板块样式 */
.hot-sector-widget {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  margin-bottom: 0.8rem;
}

.hot-sector-item {
  flex: 1;
  min-width: 80px;
  text-align: center;
  padding: 0.6rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.hot-sector-item:hover {
  transform: scale(1.03);
}

.hot-sector-item .name {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hot-sector-item .change {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
}

.hot-sector-item .badge {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
}

/* 资金流向样式 */
.fund-flow-widget {
  margin-bottom: 0.8rem;
}

.fund-flow-item {
  margin-bottom: 0.6rem;
  padding: 0.7rem;
  border-radius: 6px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.fund-flow-item:hover {
  background-color: #f0f0f0;
}

.fund-flow-item .flow-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.4rem;
}

.fund-flow-item .flow-name {
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fund-flow-item .flow-value {
  font-weight: 700;
  font-size: 0.9rem;
}

.fund-flow-item .flow-progress {
  height: 6px;
  border-radius: 3px;
  margin-bottom: 0.4rem;
}

.fund-flow-item .flow-detail {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6c757d;
}

/* 市场估值指标样式 */
.valuation-widget {
  display: flex;
  gap: 0.6rem;
  margin-bottom: 0.8rem;
}

.valuation-item {
  flex: 1;
  text-align: center;
  padding: 0.8rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.valuation-item:hover {
  transform: scale(1.03);
}

.valuation-item .icon {
  margin-bottom: 0.5rem;
}

.valuation-item .icon .avatar {
  margin: 0 auto;
  width: 2.2rem;
  height: 2.2rem;
}

.valuation-item .value {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
}

.valuation-item .label {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.2rem;
}

.valuation-item .change {
  font-size: 0.8rem;
  font-weight: 500;
}

.valuation-chart {
  padding: 0.6rem;
  border-radius: 6px;
  background-color: #f8f9fa;
  height: 100px;
  margin-bottom: 0.6rem;
}

.valuation-range {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6c757d;
}

.valuation-range .low {
  color: #28a745;
}

.valuation-range .high {
  color: #dc3545;
}

/* 技术指标样式 */
.tech-indicator-widget {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
}

.tech-indicator-item {
  flex: 1;
  min-width: 80px;
  text-align: center;
  padding: 0.6rem;
  border-radius: 6px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.tech-indicator-item:hover {
  background-color: #f0f0f0;
}

.tech-indicator-item .name {
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.2rem;
}

.tech-indicator-item .value {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
}

.tech-indicator-item .signal {
  display: inline-block;
  font-size: 0.7rem;
  font-weight: 500;
  padding: 0.15rem 0.4rem;
  border-radius: 3px;
}

.tech-indicator-item .signal-buy {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.tech-indicator-item .signal-sell {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.tech-indicator-item .signal-neutral {
  background-color: rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
  .hot-sector-item .change,
  .valuation-item .value,
  .tech-indicator-item .value {
    font-size: 1.1rem;
  }

  .hot-sector-item .name,
  .valuation-item .label,
  .tech-indicator-item .name {
    font-size: 0.75rem;
  }

  .fund-flow-item .flow-title {
    flex-direction: column;
    align-items: flex-start;
  }

  .fund-flow-item .flow-detail {
    flex-direction: column;
    gap: 0.25rem;
  }
}
