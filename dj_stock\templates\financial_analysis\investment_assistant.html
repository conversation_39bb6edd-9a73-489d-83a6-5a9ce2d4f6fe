{% extends "base.html" %} {% load static %} {% block title %}智能选股助手 - 股票数据分析系统{% endblock %} {% block extra_css %}
<style>
  /* 页面整体样式 */
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    border-radius: 12px;
    margin-bottom: 2rem;
  }

  .strategy-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    overflow: hidden;
  }

  .strategy-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .strategy-header {
    padding: 1.5rem;
    color: white;
    position: relative;
  }

  .strategy-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  }

  .strategy-header.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
  }

  .strategy-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  }

  .strategy-header.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
  }

  .strategy-header.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
  }

  .strategy-header.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  }

  .strategy-body {
    padding: 1.5rem;
    background: white;
  }

  .strategy-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .strategy-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .strategy-description {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 1rem;
  }

  .strategy-filters {
    font-size: 0.85rem;
    color: #6c757d;
  }

  .filter-item {
    display: inline-block;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin: 0.125rem;
  }

  .portfolio-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
  }

  .portfolio-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
  }

  .portfolio-body {
    padding: 1.5rem;
  }

  .metric-card {
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    background: #f8f9fa;
    margin-bottom: 1rem;
  }

  .metric-value {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }

  .metric-label {
    font-size: 0.85rem;
    color: #6c757d;
  }

  .risk-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .suggestion-item {
    padding: 0.75rem;
    border-left: 4px solid #007bff;
    background: #f8f9fa;
    margin-bottom: 0.5rem;
    border-radius: 0 4px 4px 0;
  }

  .suggestion-item.high {
    border-left-color: #dc3545;
  }

  .suggestion-item.medium {
    border-left-color: #ffc107;
  }

  .suggestion-item.low {
    border-left-color: #28a745;
  }

  @media (max-width: 768px) {
    .strategy-card {
      margin-bottom: 1rem;
    }

    .page-header {
      padding: 1.5rem 0;
    }
  }
</style>
{% endblock %} {% block content %}
<div class="page-body">
  <div class="container-xl">
    <!-- 页面标题 -->
    <div class="page-header text-center">
      <div class="container-xl">
        <h1 class="page-title">
          <i class="ti ti-brain me-2"></i>
          智能选股助手
        </h1>
        <p class="page-subtitle">基于专业投资策略，帮您发现优质投资机会</p>
      </div>
    </div>

    <!-- 投资策略模板 -->
    <div class="row mb-4">
      <div class="col-12">
        <h3 class="mb-3">
          <i class="ti ti-strategy me-2"></i>
          投资策略模板
        </h3>
        <p class="text-muted mb-4">选择适合您的投资策略，快速筛选优质股票</p>
      </div>
    </div>

    <div class="row">
      {% for strategy in strategies %}
      <div class="col-lg-4 col-md-6 mb-4">
        <div class="strategy-card">
          <div class="strategy-header bg-{{ strategy.color }}">
            <div class="strategy-icon">
              <i class="{{ strategy.icon }}"></i>
            </div>
            <div class="strategy-title">{{ strategy.name }}</div>
            <div class="strategy-description">{{ strategy.description }}</div>
          </div>
          <div class="strategy-body">
            <div class="strategy-filters mb-3">
              <strong>筛选条件：</strong><br />
              {% if strategy.filters.pe_max %}
              <span class="filter-item">PE ≤ {{ strategy.filters.pe_max }}</span>
              {% endif %} {% if strategy.filters.roe_min %}
              <span class="filter-item">ROE ≥ {{ strategy.filters.roe_min }}%</span>
              {% endif %} {% if strategy.filters.revenue_growth_min %}
              <span class="filter-item">营收增长 ≥ {{ strategy.filters.revenue_growth_min }}%</span>
              {% endif %} {% if strategy.filters.debt_ratio_max %}
              <span class="filter-item">负债率 ≤ {{ strategy.filters.debt_ratio_max }}%</span>
              {% endif %}
            </div>
            <a href="{% url 'financial_analysis:financial_screener' %}?strategy={{ strategy.name }}" class="btn btn-{{ strategy.color }} w-100">
              <i class="ti ti-search me-2"></i>
              应用此策略
            </a>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- 智能筛选器 -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="ti ti-filter me-2"></i>
                智能筛选器
            </h3>
            <p class="text-muted mb-4">根据财务指标和技术指标筛选优质股票</p>
        </div>
    </div>

    <!-- 筛选表单 -->
    <div class="card">
        <div class="card-body">
            <form method="get" id="screening-form">
                <div class="row g-3">
                    <!-- 基本筛选 -->
                    <div class="col-md-3">
                        <label class="form-label">行业</label>
                        <select class="form-select" name="industry">
                            <option value="">全部行业</option>
                            {% for industry in industries %}
                            <option value="{{ industry }}" {% if filters.industry == industry %}selected{% endif %}>{{ industry }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- PE范围 -->
                    <div class="col-md-3">
                        <label class="form-label">PE范围</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="pe_min" placeholder="最小值" value="{{ filters.pe_min }}">
                            <span class="input-group-text">-</span>
                            <input type="number" class="form-control" name="pe_max" placeholder="最大值" value="{{ filters.pe_max }}">
                        </div>
                    </div>

                    <!-- ROE范围 -->
                    <div class="col-md-3">
                        <label class="form-label">ROE范围(%)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="roe_min" placeholder="最小值" value="{{ filters.roe_min }}">
                            <span class="input-group-text">-</span>
                            <input type="number" class="form-control" name="roe_max" placeholder="最大值" value="{{ filters.roe_max }}">
                        </div>
                    </div>

                    <!-- 市值范围 -->
                    <div class="col-md-3">
                        <label class="form-label">市值范围(亿元)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="market_cap_min" placeholder="最小值" value="{{ filters.market_cap_min }}">
                            <span class="input-group-text">-</span>
                            <input type="number" class="form-control" name="market_cap_max" placeholder="最大值" value="{{ filters.market_cap_max }}">
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-search me-2"></i>
                            开始筛选
                        </button>
                        <a href="{% url 'financial_analysis:investment_assistant' %}" class="btn btn-outline-secondary ms-2">
                            <i class="ti ti-refresh me-2"></i>
                            重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 筛选结果 -->
    {% if show_screener and page_obj %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-list me-2"></i>
                        筛选结果 (共 {{ total_count }} 只股票)
                    </h4>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-vcenter">
                            <thead>
                                <tr>
                                    <th>股票代码</th>
                                    <th>股票名称</th>
                                    <th>行业</th>
                                    <th>最新价</th>
                                    <th>涨跌幅</th>
                                    <th>PE</th>
                                    <th>ROE(%)</th>
                                    <th>市值(亿)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock in page_obj %}
                                <tr>
                                    <td><strong>{{ stock.stock_code }}</strong></td>
                                    <td>{{ stock.stock_name }}</td>
                                    <td>{{ stock.industry|default:"-" }}</td>
                                    <td>{{ stock.latest_price|default:"-" }}</td>
                                    <td>
                                        {% if stock.change_percent %}
                                        <span class="{% if stock.change_percent > 0 %}text-success{% elif stock.change_percent < 0 %}text-danger{% endif %}">
                                            {{ stock.change_percent|floatformat:2 }}%
                                        </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>{{ stock.pe_ratio|default:"-"|floatformat:2 }}</td>
                                    <td>{{ stock.roe|default:"-"|floatformat:2 }}</td>
                                    <td>{{ stock.market_cap|default:"-"|floatformat:2 }}</td>
                                    <td>
                                        <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="btn btn-sm btn-outline-primary">详情</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 投资组合分析 -->
    {% if user.is_authenticated %}
    <div class="row mt-5">
      <div class="col-12">
        <h3 class="mb-3">
          <i class="ti ti-chart-pie me-2"></i>
          我的投资组合分析
        </h3>
        <p class="text-muted mb-4">基于您的收藏股票进行投资组合分析</p>
      </div>
    </div>

    {% if portfolio_analysis %}
    <div class="row">
      <!-- 组合概览 -->
      <div class="col-lg-8">
        <div class="portfolio-card">
          <div class="portfolio-header">
            <h5 class="mb-0">
              <i class="ti ti-dashboard me-2"></i>
              组合概览
            </h5>
          </div>
          <div class="portfolio-body">
            <div class="row">
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value">{{ portfolio_analysis.total_stocks }}</div>
                  <div class="metric-label">持股数量</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ portfolio_analysis.portfolio_score }}</div>
                  <div class="metric-label">组合评分</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ portfolio_analysis.high_quality_ratio }}%</div>
                  <div class="metric-label">优质股占比</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ portfolio_analysis.industry_count }}</div>
                  <div class="metric-label">涉及行业</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 风险评估 -->
      <div class="col-lg-4">
        <div class="portfolio-card">
          <div class="portfolio-header">
            <h5 class="mb-0">
              <i class="ti ti-shield me-2"></i>
              风险评估
            </h5>
          </div>
          <div class="portfolio-body text-center">
            <div class="risk-badge bg-{{ portfolio_analysis.risk_color }} text-white mb-3">{{ portfolio_analysis.risk_level }}</div>
            <div class="small text-muted">基于组合质量、行业分散度等因素综合评估</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化建议 -->
    {% if portfolio_analysis.suggestions %}
    <div class="row">
      <div class="col-12">
        <div class="portfolio-card">
          <div class="portfolio-header">
            <h5 class="mb-0">
              <i class="ti ti-bulb me-2"></i>
              优化建议
            </h5>
          </div>
          <div class="portfolio-body">
            {% for suggestion in portfolio_analysis.suggestions %}
            <div class="suggestion-item {{ suggestion.priority }}"><strong>{{ suggestion.type }}：</strong>{{ suggestion.message }}</div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
    {% endif %} {% else %}
    <div class="row">
      <div class="col-12">
        <div class="alert alert-info">
          <i class="ti ti-info-circle me-2"></i>
          请先<a href="{% url 'financial_analysis:favorite_list' %}">收藏一些股票</a>，然后返回查看投资组合分析。
        </div>
      </div>
    </div>
    {% endif %} {% else %}
    <div class="row mt-5">
      <div class="col-12">
        <div class="alert alert-warning text-center">
          <i class="ti ti-login me-2"></i>
          请先<a href="{% url 'login' %}">登录</a>以使用投资组合分析功能
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 策略卡片动画
    const strategyCards = document.querySelectorAll('.strategy-card')
    strategyCards.forEach((card, index) => {
      card.style.opacity = '0'
      card.style.transform = 'translateY(20px)'
      setTimeout(() => {
        card.style.transition = 'all 0.6s ease'
        card.style.opacity = '1'
        card.style.transform = 'translateY(0)'
      }, index * 100)
    })
  })
</script>
{% endblock %}
