{% extends 'base.html' %} {% block title %}市场资金流向 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">市场资金流向</h2>
        <div class="text-muted mt-1">查看市场整体资金流向数据</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <a href="{% url 'market_data:market_fund_flow_trend' %}" class="btn btn-outline-primary"> <i class="ti ti-chart-line me-1"></i>查看趋势图 </a>
          </div>
          <div class="me-2">
            <form method="get" class="d-flex">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="ti ti-calendar"></i>
                </span>
                <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
                <button type="submit" class="btn btn-primary"><i class="ti ti-search me-1"></i>查询</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-md-6 col-lg-3">
        <div class="card stat-card shadow-hover fade-in">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">上证指数</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-azure-lt">
                  <i class="ti ti-calendar me-1"></i>
                  {{ market_fund_flow_data.0.trade_date|date:"Y-m-d" }}
                </div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 number">{{ market_fund_flow_data.0.sh_index_close|floatformat:2 }}</div>
            <div class="d-flex mb-2">
              <div class="text-secondary">涨跌幅</div>
              <div class="ms-auto">
                {% if market_fund_flow_data.0.sh_index_change_pct > 0 %}
                <span class="percent-change positive">{{ market_fund_flow_data.0.sh_index_change_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="percent-change negative">{{ market_fund_flow_data.0.sh_index_change_pct|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card stat-card shadow-hover fade-in" style="animation-delay: 0.1s">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">深证成指</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-azure-lt">
                  <i class="ti ti-calendar me-1"></i>
                  {{ market_fund_flow_data.0.trade_date|date:"Y-m-d" }}
                </div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 number">{{ market_fund_flow_data.0.sz_index_close|floatformat:2 }}</div>
            <div class="d-flex mb-2">
              <div class="text-secondary">涨跌幅</div>
              <div class="ms-auto">
                {% if market_fund_flow_data.0.sz_index_change_pct > 0 %}
                <span class="percent-change positive">{{ market_fund_flow_data.0.sz_index_change_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="percent-change negative">{{ market_fund_flow_data.0.sz_index_change_pct|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card stat-card shadow-hover fade-in" style="animation-delay: 0.2s">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">主力净流入</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-azure-lt">
                  <i class="ti ti-calendar me-1"></i>
                  {{ market_fund_flow_data.0.trade_date|date:"Y-m-d" }}
                </div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 number">
              {% if market_fund_flow_data.0.main_net_inflow > 0 %}
              <span class="text-up">{{ market_fund_flow_data.0.main_net_inflow|floatformat:2 }}亿</span>
              {% else %}
              <span class="text-down">{{ market_fund_flow_data.0.main_net_inflow|floatformat:2 }}亿</span>
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div class="text-secondary">净占比</div>
              <div class="ms-auto">
                {% if market_fund_flow_data.0.main_net_inflow_pct > 0 %}
                <span class="percent-change positive">{{ market_fund_flow_data.0.main_net_inflow_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="percent-change negative">{{ market_fund_flow_data.0.main_net_inflow_pct|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card stat-card shadow-hover fade-in" style="animation-delay: 0.3s">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">30日累计净流入</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-azure-lt">
                  <i class="ti ti-calendar-stats me-1"></i>
                  近30个交易日
                </div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 number">
              {% if cumulative_inflow > 0 %}
              <span class="text-up">{{ cumulative_inflow|floatformat:2 }}亿</span>
              {% else %}
              <span class="text-down">{{ cumulative_inflow|floatformat:2 }}亿</span>
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div class="text-secondary">日均</div>
              <div class="ms-auto">
                {% if cumulative_inflow > 0 %}
                <span class="percent-change positive">{{ cumulative_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="percent-change negative">{{ cumulative_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资金流向表格 -->
    <div class="card shadow-hover slide-in">
      <div class="card-header">
        <h3 class="card-title">
          <i class="ti ti-cash me-2 text-primary"></i>
          市场资金流向数据
        </h3>
        <div class="card-actions">
          <a href="#" class="btn btn-icon" title="刷新数据" data-bs-toggle="tooltip">
            <i class="ti ti-refresh"></i>
          </a>
          <a href="#" class="btn btn-icon" title="导出数据" data-bs-toggle="tooltip">
            <i class="ti ti-download"></i>
          </a>
        </div>
      </div>
      <!-- 移除了卡片内容区域，指示器已移至分页组件中 -->
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>交易日期</th>
              <th>上证指数</th>
              <th>上证涨跌幅</th>
              <th>深证成指</th>
              <th>深证涨跌幅</th>
              <th>主力净流入</th>
              <th>超大单净流入</th>
              <th>大单净流入</th>
              <th>中单净流入</th>
              <th>小单净流入</th>
            </tr>
          </thead>
          <tbody>
            {% for data in market_fund_flow_data %}
            <tr class="{% if data.main_net_inflow > 0 %}row-up{% else %}row-down{% endif %}">
              <td>
                <span class="text-emphasis">{{ data.trade_date|date:"Y-m-d" }}</span>
              </td>
              <td class="number">{{ data.sh_index_close|floatformat:2 }}</td>
              <td>
                {% if data.sh_index_change_pct > 0 %}
                <span class="percent-change positive">{{ data.sh_index_change_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="percent-change negative">{{ data.sh_index_change_pct|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td class="number">{{ data.sz_index_close|floatformat:2 }}</td>
              <td>
                {% if data.sz_index_change_pct > 0 %}
                <span class="percent-change positive">{{ data.sz_index_change_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="percent-change negative">{{ data.sz_index_change_pct|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if data.main_net_inflow > 0 %}
                <span class="text-up">{{ data.main_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ data.main_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if data.super_big_net_inflow > 0 %}
                <span class="text-up">{{ data.super_big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ data.super_big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if data.big_net_inflow > 0 %}
                <span class="text-up">{{ data.big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ data.big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if data.medium_net_inflow > 0 %}
                <span class="text-up">{{ data.medium_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ data.medium_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if data.small_net_inflow > 0 %}
                <span class="text-up">{{ data.small_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ data.small_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="10" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon icon-tabler icon-tabler-database-off"
                      width="32"
                      height="32"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path
                        d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"
                      ></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">当前日期没有市场资金流向数据，请尝试选择其他日期。</p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% include 'includes/pagination.html' with page_obj=page_obj rows_per_page=rows_per_page show_flow_indicator=True %}
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 页面大小切换功能已移除

    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // 表格行高亮
    const tableRows = document.querySelectorAll('.datatable tbody tr')
    tableRows.forEach((row) => {
      row.addEventListener('mouseenter', function () {
        this.classList.add('bg-light-hover')
      })
      row.addEventListener('mouseleave', function () {
        this.classList.remove('bg-light-hover')
      })
    })
  })
</script>
{% endblock %}
