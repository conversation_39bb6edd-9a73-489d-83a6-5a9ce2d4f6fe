#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
财务指标检查工具

该脚本用于：
1. 获取akshare API返回的所有财务指标
2. 显示当前StockFinancialIndicator模型中的字段
3. 帮助开发者了解可用的财务指标和模型字段对应关系

使用方法：
python check_indicators.py
"""

import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dj_stock.settings')
import django
django.setup()
import akshare as ak
import pandas as pd

# 获取财务摘要数据
df = ak.stock_financial_abstract(symbol='600000')

# 打印所有指标
print('=== akshare API 返回的所有财务指标 ===')
for idx, row in df.iterrows():
    print(f"{idx}. {row['指标']}")

# 打印当前模型中的字段
from financial_analysis.models import StockFinancialIndicator
model_fields = [field.name for field in StockFinancialIndicator._meta.fields
                if not field.name.startswith('_') and field.name not in ['id', 'stock_code', 'report_date', 'last_update', 'data_source', 'collection_date']]
print('\n=== StockFinancialIndicator 模型中的字段 ===')
for field in model_fields:
    print(field)

print(f'\n总计：API指标 {len(df)} 个，模型字段 {len(model_fields)} 个')
