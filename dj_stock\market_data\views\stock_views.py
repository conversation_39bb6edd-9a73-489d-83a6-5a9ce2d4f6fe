# -*- coding: utf-8 -*-
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Q, OuterRef, Subquery, F
from django.utils import timezone
import logging

from .base_views import (
    StockBasic,
    StockDailyQuote,
    StockRiskWarning,
    StockIPO,
    StockDataStatus,
    StockFinancialIndicator,
    StockDividend,
    FinancialDataFetcher,
    StockDataFetcher,
    get_pagination,
)

logger = logging.getLogger(__name__)


def stock_list(request):
    """
    股票列表视图
    """
    query = request.GET.get("q", "")
    industry = request.GET.get("industry", "")
    market = request.GET.get("market", "")
    min_price = request.GET.get("min_price", "")
    max_price = request.GET.get("max_price", "")
    sort = request.GET.get("sort", "stock_code")  # 默认按代码排序
    limit_type = request.GET.get("limit_type", "")  # 涨跌幅筛选参数

    # 构建查询
    stocks = StockBasic.objects.all()

    # 获取最新交易日期
    latest_date = (
        StockDailyQuote.objects.order_by("-trade_date").values("trade_date").first()
    )

    if latest_date:
        # 联表查询，获取股票行情数据
        latest_quotes = StockDailyQuote.objects.filter(
            stock_code=OuterRef("stock_code"), trade_date=latest_date["trade_date"]
        )

        # 为股票列表添加行情数据字段
        stocks = stocks.annotate(
            close_price=Subquery(latest_quotes.values("close_price")[:1]),
            change_percent=Subquery(latest_quotes.values("change_percent")[:1]),
            change_amount=Subquery(latest_quotes.values("change_amount")[:1]),
            volume=Subquery(latest_quotes.values("volume")[:1]),
            amount=Subquery(latest_quotes.values("amount")[:1]),
            turnover_rate=Subquery(latest_quotes.values("turnover_rate")[:1]),
            trade_date=Subquery(latest_quotes.values("trade_date")[:1]),
        )

    # 应用搜索过滤
    if query:
        stocks = stocks.filter(
            Q(stock_code__icontains=query) | Q(stock_name__icontains=query)
        )

    # 行业筛选
    if industry:
        stocks = stocks.filter(industry=industry)

    # 交易所筛选
    if market:
        stocks = stocks.filter(market__contains=market)

    # 价格范围筛选
    if min_price:
        try:
            min_price = float(min_price)
            stocks = stocks.filter(close_price__gte=min_price)
        except (ValueError, TypeError):
            pass

    if max_price:
        try:
            max_price = float(max_price)
            stocks = stocks.filter(close_price__lte=max_price)
        except (ValueError, TypeError):
            pass

    # 涨跌幅筛选
    if limit_type == "limit_up":
        # 涨停股筛选 - 创业板按20%，主板按10%
        stocks = stocks.filter(
            Q(stock_code__startswith="3", change_percent__gte=19.5)
            | Q(~Q(stock_code__startswith="3"), change_percent__gte=9.5)
        )
    elif limit_type == "limit_down":
        # 跌停股筛选
        stocks = stocks.filter(
            Q(stock_code__startswith="3", change_percent__lte=-19.5)
            | Q(~Q(stock_code__startswith="3"), change_percent__lte=-9.5)
        )
    elif limit_type == "up_5":
        # 涨幅超过5%
        stocks = stocks.filter(change_percent__gt=5)
    elif limit_type == "up_3":
        # 涨幅超过3%
        stocks = stocks.filter(change_percent__gt=3)
    elif limit_type == "down_5":
        # 跌幅超过5%
        stocks = stocks.filter(change_percent__lt=-5)
    elif limit_type == "down_3":
        # 跌幅超过3%
        stocks = stocks.filter(change_percent__lt=-3)
    elif limit_type == "zero_3":
        # 涨跌幅在±3%之间
        stocks = stocks.filter(change_percent__gt=-3, change_percent__lt=3)
    elif limit_type == "zero_5":
        # 涨跌幅在±5%之间
        stocks = stocks.filter(change_percent__gt=-5, change_percent__lt=5)

    # 排序
    if sort == "change_percent":
        stocks = stocks.order_by("-change_percent")
    elif sort == "-change_percent":
        stocks = stocks.order_by("change_percent")
    elif sort == "turnover_rate":
        stocks = stocks.order_by("-turnover_rate")
    elif sort == "amount":
        stocks = stocks.order_by("-amount")
    elif sort == "volume":
        stocks = stocks.order_by("-volume")
    else:
        stocks = stocks.order_by(sort)

    # 分页
    page_number = request.GET.get("page", 1)
    rows_per_page = int(request.GET.get("rows_per_page", 20))
    page_obj = get_pagination(stocks, page_number, per_page=rows_per_page)

    # 获取行业列表（用于筛选）
    industries = (
        StockBasic.objects.exclude(industry__isnull=True)
        .exclude(industry="")
        .values_list("industry", flat=True)
        .distinct()
        .order_by("industry")
    )

    # 准备查询参数字典，用于分页时保持筛选条件
    query_params = {}
    if query:
        query_params["q"] = query
    if industry:
        query_params["industry"] = industry
    if market:
        query_params["market"] = market
    if min_price:
        query_params["min_price"] = min_price
    if max_price:
        query_params["max_price"] = max_price
    if sort:
        query_params["sort"] = sort
    if limit_type:
        query_params["limit_type"] = limit_type

    # 计算市场情绪统计数据
    market_sentiment = {}
    if latest_date:
        daily_quotes = StockDailyQuote.objects.filter(
            trade_date=latest_date["trade_date"]
        )
        up_stocks = daily_quotes.filter(change_percent__gt=0).count()
        down_stocks = daily_quotes.filter(change_percent__lt=0).count()
        flat_stocks = daily_quotes.filter(change_percent=0).count()

        total_count = up_stocks + down_stocks + flat_stocks
        if total_count > 0:
            market_sentiment = {
                "up_count": up_stocks,
                "down_count": down_stocks,
                "flat_count": flat_stocks,
                "up_percent": round(up_stocks / total_count * 100),
                "down_percent": round(down_stocks / total_count * 100),
                "flat_percent": round(flat_stocks / total_count * 100),
            }

    # 计算交易所分布统计
    exchange_stats = {
        "sh_count": StockBasic.objects.filter(
            Q(market__contains="沪")
            | Q(market__contains="上")
            | Q(stock_code__startswith="6")
        ).count(),
        "sz_count": StockBasic.objects.filter(
            Q(market__contains="深")
            | Q(stock_code__startswith="0")
            | Q(stock_code__startswith="3")
        ).count(),
        "bj_count": StockBasic.objects.filter(
            Q(market__contains="北") | Q(stock_code__startswith="8")
        ).count(),
    }

    # 成交量和成交额单位转换
    for item in page_obj:
        if item.volume:
            item.volume = item.volume / 10000
        else:
            item.volume = None
        if item.amount:
            item.amount = item.amount / 100000000
        else:
            item.amount = None

    context = {
        "page_obj": page_obj,
        "total_stocks": stocks.count(),
        "query": query,
        "industry": industry,
        "industries": industries,
        "market": market,
        "min_price": min_price,
        "max_price": max_price,
        "sort": sort,
        "limit_type": limit_type,
        "latest_date": latest_date["trade_date"] if latest_date else None,
        "latest_trade_date": latest_date["trade_date"] if latest_date else None,
        "market_sentiment": market_sentiment,
        "exchange_stats": exchange_stats,
        "query_params": query_params,
        "rows_per_page": rows_per_page,
    }

    return render(request, "market_data/stock_list.html", context)


def stock_detail(request, code):
    """股票详情视图"""
    # 获取股票基本信息
    stock = get_object_or_404(StockBasic, stock_code=code)
    context = {"stock": stock, "data_status": {}, "data_status_message": None}

    # 获取最新行情数据
    latest_quote = (
        StockDailyQuote.objects.filter(stock_code=code).order_by("-trade_date").first()
    )
    # 成交量和成交额单位转换
    if latest_quote:
        # 安全地转换单位，处理None值的情况
        if latest_quote.volume is not None:
            latest_quote.volume = latest_quote.volume / 10000
        if latest_quote.amount is not None:
            latest_quote.amount = latest_quote.amount / 100000000
    context["latest_quote"] = latest_quote

    # 获取最新财务指标
    latest_indicator = (
        StockFinancialIndicator.objects.filter(stock_code=code)
        .order_by("-report_date")
        .first()
    )

    # 检查是否需要采集财务数据
    if not latest_indicator or (
        latest_indicator.report_date
        and (timezone.now().date() - latest_indicator.report_date).days > 90
    ):
        logger.info(f"需要采集财务数据: {code}")
        try:
            fetcher = FinancialDataFetcher()
            # 修复方法调用，使用正确的方法名
            fetcher.fetch_and_save(code)
            context["data_status"]["financial"] = "complete"
            context["data_status_message"] = "财务数据已更新"
            # 重新获取最新财务指标
            latest_indicator = (
                StockFinancialIndicator.objects.filter(stock_code=code)
                .order_by("-report_date")
                .first()
            )
        except Exception as e:
            logger.error(f"采集财务数据失败: {code}, 错误: {str(e)}")
            context["data_status"]["financial"] = "error"
            context["data_status_message"] = f"财务数据采集失败: {str(e)}"

    # 检查是否需要采集历史行情数据
    try:
        # 获取数据状态
        data_status, created = StockDataStatus.objects.get_or_create(
            stock_code=code, defaults={"status": "never_fetched"}
        )

        # 如果从未获取过数据或者获取失败，那么需要采集
        if (
            data_status.status in ["never_fetched", "error"]
            or not data_status.is_complete
        ):
            logger.info(f"需要采集历史行情数据: {code}, 状态: {data_status.status}")

            fetcher = StockDataFetcher()
            success, message = fetcher.fetch_and_save(code)

            if success:
                context["data_status"]["history"] = "complete"
                context["data_status_message"] = (
                    context.get("data_status_message", "")
                    + f" 历史行情数据已采集: {message}"
                )
                # 重新获取数据状态
                data_status = StockDataStatus.objects.get(stock_code=code)
            else:
                context["data_status"]["history"] = "error"
                context["data_status_message"] = (
                    context.get("data_status_message", "")
                    + f" 历史行情数据采集失败: {message}"
                )
        else:
            # 检查数据是否需要更新（如果最后更新时间超过3天）
            if (
                data_status.date_to
                and (timezone.now().date() - data_status.date_to).days > 3
            ):
                logger.info(
                    f"需要更新历史行情数据: {code}, 最后更新: {data_status.date_to}"
                )

                fetcher = StockDataFetcher()
                success, message = fetcher.fetch_and_save(code, force_update=True)

                if success:
                    context["data_status"]["history"] = "updated"
                    context["data_status_message"] = (
                        context.get("data_status_message", "")
                        + f" 历史行情数据已更新: {message}"
                    )
                    # 重新获取数据状态
                    data_status = StockDataStatus.objects.get(stock_code=code)
                else:
                    context["data_status"]["history"] = "error"
                    context["data_status_message"] = (
                        context.get("data_status_message", "")
                        + f" 历史行情数据更新失败: {message}"
                    )
            else:
                logger.info(
                    f"历史行情数据已是最新: {code}, 最后更新: {data_status.date_to}"
                )
                context["data_status"]["history"] = "up_to_date"

        # 将数据状态添加到上下文
        context["history_data_status"] = data_status
    except Exception as e:
        logger.error(f"处理历史行情数据时出错: {code}, 错误: {str(e)}")
        context["data_status"]["history"] = "error"
        context["data_status_message"] = (
            context.get("data_status_message", "") + f" 历史行情数据处理错误: {str(e)}"
        )

    # 如果有财务指标，计算显示值
    if latest_indicator and latest_indicator.total_revenue:
        # 将收入数值转换为亿元显示
        try:
            latest_indicator.total_revenue_display = (
                float(latest_indicator.total_revenue) / 100000000
            )
        except:
            latest_indicator.total_revenue_display = 0

    context["latest_indicator"] = latest_indicator

    # 获取最近5条分红记录
    latest_dividends = StockDividend.objects.filter(stock_code=code).order_by(
        "-registration_date"
    )[:5]
    context["latest_dividends"] = latest_dividends

    # 获取历史数据 - 使用StockDailyQuote
    history_data = StockDailyQuote.objects.filter(stock_code=code).order_by(
        "-trade_date"
    )[:30]
    # 成交量单位转换
    for item in history_data:
        if item.volume:
            item.volume = item.volume / 10000
        else:
            item.volume = None

    context["history_data"] = history_data

    # 获取相关股票
    related_stocks = StockBasic.objects.filter(industry=stock.industry).exclude(
        stock_code=code
    )[:5]
    context["related_stocks"] = related_stocks

    return render(request, "market_data/stock_detail.html", context)


def stock_history(request, code):
    """股票历史行情数据视图"""
    import json

    stock = get_object_or_404(StockBasic, stock_code=code)

    # 获取排序方式
    sort_by = request.GET.get("sort", "-trade_date")

    # 构建查询
    history_data = StockDailyQuote.objects.filter(stock_code=code)

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        history_data = history_data.order_by(f"-{sort_field}")
    else:
        history_data = history_data.order_by(sort_by)

    # 计算市盈率和市净率（如果数据库中没有）
    # 获取最新财务数据用于计算
    latest_financial = (
        StockFinancialIndicator.objects.filter(stock_code=code)
        .order_by("-report_date")
        .first()
    )

    # 成交量和成交额单位转换，并计算市盈率市净率
    for item in history_data:
        if item.volume:
            item.volume = float(item.volume) / 10000  # 转换为万手
        if item.amount:
            item.amount = float(item.amount) / 100000000  # 转换为亿元

        # 如果数据库中没有市盈率和市净率，尝试计算
        if (
            not item.pe_ratio
            and latest_financial
            and latest_financial.eps
            and item.close_price
        ):
            try:
                item.pe_ratio = round(
                    float(item.close_price) / float(latest_financial.eps), 2
                )
            except (ValueError, ZeroDivisionError):
                item.pe_ratio = None

        if (
            not item.pb_ratio
            and latest_financial
            and latest_financial.nav
            and item.close_price
        ):
            try:
                item.pb_ratio = round(
                    float(item.close_price) / float(latest_financial.nav), 2
                )
            except (ValueError, ZeroDivisionError):
                item.pb_ratio = None

    # 准备图表数据（获取更多历史数据用于图表显示）
    # 获取参数控制数据范围，默认获取最近2年的数据（约500个交易日）
    chart_days = int(request.GET.get("chart_days", 500))
    chart_data = StockDailyQuote.objects.filter(stock_code=code).order_by(
        "-trade_date"
    )[:chart_days]
    chart_data = list(reversed(chart_data))  # 按时间正序排列

    # 转换为JSON格式供前端使用
    chart_json = []
    for item in chart_data:
        # 单位转换：成交量转为万手，成交额转为亿元
        volume_in_wan = float(item.volume) / 10000 if item.volume else 0  # 转为万手
        amount_in_yi = float(item.amount) / 100000000 if item.amount else 0  # 转为亿元

        chart_json.append(
            {
                "date": item.trade_date.strftime("%Y-%m-%d"),
                "open": float(item.open_price) if item.open_price else 0,
                "close": float(item.close_price) if item.close_price else 0,
                "high": float(item.high_price) if item.high_price else 0,
                "low": float(item.low_price) if item.low_price else 0,
                "volume": volume_in_wan,
                "amount": amount_in_yi,
            }
        )

    # 分页
    page_number = request.GET.get("page", 1)
    page_obj = get_pagination(history_data, page_number, per_page=20)

    context = {
        "stock": stock,
        "page_obj": page_obj,
        "total_records": history_data.count(),
        "sort_by": sort_by,
        "chart_data": json.dumps(chart_json),
        "latest_financial": latest_financial,
    }
    return render(request, "market_data/stock_history.html", context)


def risk_warning(request):
    """风险预警股票列表视图"""
    # 获取查询参数
    query = request.GET.get("q", "")
    status = request.GET.get("status", "")  # 新增状态筛选
    risk_type = request.GET.get("risk_type", "")  # 新增风险类型筛选

    # 构建基础查询
    risks_query = StockRiskWarning.objects.all()

    # 应用状态筛选
    if status:
        risks_query = risks_query.filter(status=status)

    # 应用风险类型筛选 - 注意StockRiskWarning模型中没有warning_type字段
    # 使用stock_name字段中的ST类型进行过滤
    if risk_type:
        if risk_type == "ST":
            risks_query = risks_query.filter(stock_name__startswith="ST")
        elif risk_type == "*ST":
            risks_query = risks_query.filter(stock_name__startswith="*ST")

    # 应用搜索筛选
    if query:
        risks_query = risks_query.filter(
            Q(stock_code__icontains=query) | Q(stock_name__icontains=query)
        )

    # 以警告日期降序排序
    risks_query = risks_query.order_by("-warning_date")

    # 分页处理
    page_number = request.GET.get("page", 1)
    page_obj = get_pagination(risks_query, page_number, per_page=10)

    # 获取所有可能的风险类型和状态，用于筛选下拉框
    all_risk_types = ["ST", "*ST"]
    all_statuses = StockRiskWarning.objects.values_list("status", flat=True).distinct()

    # 打印调试信息
    print(
        f"Debug risk_warning: 总风险记录数: {risks_query.count()}, 当前页: {page_number}, 查询: '{query}', 状态: '{status}', 风险类型: '{risk_type}'"
    )

    context = {
        "page_obj": page_obj,
        "risk_warnings": page_obj.object_list,  # 兼容旧模板
        "total_risks": risks_query.count(),
        "query": query,
        "status": status,
        "risk_type": risk_type,
        "all_risk_types": all_risk_types,
        "all_statuses": all_statuses,
    }

    return render(request, "market_data/risk_warning.html", context)


def ipo_list(request):
    """
    新股上市列表
    """
    query = request.GET.get("q", "")
    year = request.GET.get("year", "")
    sort = request.GET.get("sort", "-listing_date")  # 默认按上市日期降序

    # 构建查询
    ipos = StockIPO.objects.all()

    # 搜索过滤
    if query:
        ipos = ipos.filter(
            Q(stock_code__icontains=query) | Q(stock_name__icontains=query)
        )

    # 年份过滤
    if year:
        ipos = ipos.filter(listing_date__year=year)

    # 排序
    ipos = ipos.order_by(sort)

    # 分页
    page_number = request.GET.get("page", 1)
    page_obj = get_pagination(ipos, page_number, per_page=10)

    # 获取所有上市年份
    years = StockIPO.objects.dates("listing_date", "year", order="DESC")
    years = [date.year for date in years]

    context = {
        "page_obj": page_obj,
        "total_ipos": ipos.count(),
        "query": query,
        "year": year,
        "sort": sort,
        "years": years,
    }
    return render(request, "market_data/ipo_list.html", context)


def api_stock_chart_data(request, code):
    """API: 获取股票图表数据"""
    from django.http import JsonResponse

    # 获取参数
    days = int(request.GET.get("days", 500))  # 获取天数，默认500个交易日

    try:
        # 获取股票基本信息
        stock = get_object_or_404(StockBasic, stock_code=code)

        # 获取历史数据
        history_data = StockDailyQuote.objects.filter(stock_code=code).order_by(
            "-trade_date"
        )[:days]

        # 按时间正序排列
        history_data = list(reversed(history_data))

        # 转换为图表数据格式
        chart_data = []
        for item in history_data:
            # 单位转换：成交量转为万手，成交额转为亿元
            volume_in_wan = float(item.volume) / 10000 if item.volume else 0  # 转为万手
            amount_in_yi = (
                float(item.amount) / 100000000 if item.amount else 0
            )  # 转为亿元

            chart_data.append(
                {
                    "date": item.trade_date.strftime("%Y-%m-%d"),
                    "open": float(item.open_price) if item.open_price else 0,
                    "close": float(item.close_price) if item.close_price else 0,
                    "high": float(item.high_price) if item.high_price else 0,
                    "low": float(item.low_price) if item.low_price else 0,
                    "volume": volume_in_wan,
                    "amount": amount_in_yi,
                }
            )

        return JsonResponse(
            {
                "success": True,
                "data": chart_data,
                "stock_info": {
                    "code": stock.stock_code,
                    "name": stock.stock_name,
                    "industry": stock.industry,
                },
            }
        )

    except Exception as e:
        return JsonResponse({"success": False, "error": str(e)})
