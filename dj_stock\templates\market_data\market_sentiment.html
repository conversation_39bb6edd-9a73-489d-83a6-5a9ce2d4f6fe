{% extends "base.html" %}
{% load static %}

{% block title %}市场情绪分析 - 股票数据分析系统{% endblock %}

{% block extra_css %}
<style>
/* 页面整体样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.sentiment-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.sentiment-header {
    padding: 1.5rem;
    color: white;
    text-align: center;
}

.sentiment-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.sentiment-header.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.sentiment-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.sentiment-header.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.sentiment-header.bg-dark {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
}

.sentiment-score {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.sentiment-level {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.sentiment-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.component-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.component-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.component-body {
    padding: 1rem;
}

.score-bar {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    margin: 0.5rem 0;
}

.score-fill {
    height: 100%;
    transition: width 0.6s ease;
}

.score-fill.high {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.score-fill.medium {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.score-fill.low {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.suggestion-card {
    border-left: 4px solid #007bff;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
    margin-bottom: 1rem;
}

.suggestion-card.buy {
    border-left-color: #28a745;
}

.suggestion-card.hold {
    border-left-color: #ffc107;
}

.suggestion-card.sell {
    border-left-color: #dc3545;
}

.suggestion-card.wait {
    border-left-color: #6c757d;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 500;
    color: #495057;
}

.metric-value {
    font-weight: 600;
    color: #212529;
}

@media (max-width: 768px) {
    .sentiment-score {
        font-size: 2.5rem;
    }
    
    .sentiment-icon {
        font-size: 3rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="page-body">
    <div class="container-xl">
        <!-- 页面标题 -->
        <div class="page-header text-center">
            <div class="container-xl">
                <h1 class="page-title">
                    <i class="ti ti-mood-happy me-2"></i>
                    市场情绪分析
                </h1>
                <p class="page-subtitle">
                    基于多维度数据分析当前市场情绪状态
                </p>
            </div>
        </div>

        <!-- 综合情绪指数 -->
        <div class="row mb-4">
            <div class="col-lg-4">
                <div class="sentiment-card">
                    <div class="sentiment-header bg-{{ sentiment.sentiment_color }}">
                        <div class="sentiment-icon">
                            <i class="{{ sentiment.sentiment_icon }}"></i>
                        </div>
                        <div class="sentiment-score">{{ sentiment.comprehensive_score }}</div>
                        <div class="sentiment-level">{{ sentiment.sentiment_level }}</div>
                        <div class="small">综合情绪指数</div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-8">
                <div class="sentiment-card">
                    <div class="component-header">
                        <h5 class="mb-0">
                            <i class="ti ti-chart-line me-2"></i>
                            情绪构成分析
                        </h5>
                    </div>
                    <div class="component-body">
                        {% for component in sentiment.components %}
                        <div class="component-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="fw-bold">{{ component.name }}</span>
                                <span class="badge bg-primary">{{ component.score }}分</span>
                            </div>
                            <div class="score-bar">
                                <div class="score-fill {% if component.score >= 70 %}high{% elif component.score >= 40 %}medium{% else %}low{% endif %}" 
                                     style="width: {{ component.score }}%"></div>
                            </div>
                            <div class="small text-muted">{{ component.description }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 投资建议 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="sentiment-card">
                    <div class="component-header">
                        <h5 class="mb-0">
                            <i class="ti ti-bulb me-2"></i>
                            投资建议
                        </h5>
                    </div>
                    <div class="component-body">
                        <div class="suggestion-card {{ sentiment.investment_suggestion.action|lower }}">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="mb-2">
                                        <i class="ti ti-target me-2"></i>
                                        操作建议：{{ sentiment.investment_suggestion.action }}
                                    </h6>
                                    <p class="mb-0">{{ sentiment.investment_suggestion.suggestion }}</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="small text-muted">风险等级</div>
                                    <div class="h5 mb-0">{{ sentiment.investment_suggestion.risk_level }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 市场数据详情 -->
        <div class="row">
            <!-- 涨跌停数据 -->
            {% if market_data.limit_data %}
            <div class="col-lg-6 mb-4">
                <div class="component-card">
                    <div class="component-header">
                        <h6 class="mb-0">
                            <i class="ti ti-trending-up me-2"></i>
                            涨跌停情况
                        </h6>
                    </div>
                    <div class="component-body">
                        <div class="metric-item">
                            <span class="metric-label">涨停股票</span>
                            <span class="metric-value text-danger">{{ market_data.limit_data.up_limit_count }}只</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">跌停股票</span>
                            <span class="metric-value text-success">{{ market_data.limit_data.down_limit_count }}只</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">涨停占比</span>
                            <span class="metric-value">{{ market_data.limit_data.up_limit_ratio|floatformat:1 }}%</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 资金流向数据 -->
            {% if market_data.fund_flow_data %}
            <div class="col-lg-6 mb-4">
                <div class="component-card">
                    <div class="component-header">
                        <h6 class="mb-0">
                            <i class="ti ti-currency-dollar me-2"></i>
                            资金流向
                        </h6>
                    </div>
                    <div class="component-body">
                        <div class="metric-item">
                            <span class="metric-label">主力净流入</span>
                            <span class="metric-value {% if market_data.fund_flow_data.main_inflow > 0 %}text-danger{% else %}text-success{% endif %}">
                                {% if market_data.fund_flow_data.main_inflow > 0 %}+{% endif %}{{ market_data.fund_flow_data.main_inflow|floatformat:1 }}亿
                            </span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">总净流入</span>
                            <span class="metric-value {% if market_data.fund_flow_data.net_inflow > 0 %}text-danger{% else %}text-success{% endif %}">
                                {% if market_data.fund_flow_data.net_inflow > 0 %}+{% endif %}{{ market_data.fund_flow_data.net_inflow|floatformat:1 }}亿
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 板块表现 -->
            {% if market_data.sector_data %}
            <div class="col-lg-6 mb-4">
                <div class="component-card">
                    <div class="component-header">
                        <h6 class="mb-0">
                            <i class="ti ti-building me-2"></i>
                            板块表现
                        </h6>
                    </div>
                    <div class="component-body">
                        <div class="metric-item">
                            <span class="metric-label">上涨板块</span>
                            <span class="metric-value text-danger">{{ market_data.sector_data.rising_count }}个</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">下跌板块</span>
                            <span class="metric-value text-success">{{ market_data.sector_data.falling_count }}个</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">上涨占比</span>
                            <span class="metric-value">{{ market_data.sector_data.rising_ratio|floatformat:1 }}%</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 成交量数据 -->
            {% if market_data.volume_data %}
            <div class="col-lg-6 mb-4">
                <div class="component-card">
                    <div class="component-header">
                        <h6 class="mb-0">
                            <i class="ti ti-chart-bar me-2"></i>
                            成交量分析
                        </h6>
                    </div>
                    <div class="component-body">
                        <div class="metric-item">
                            <span class="metric-label">当日成交量</span>
                            <span class="metric-value">{{ market_data.volume_data.current_volume|floatformat:0 }}亿</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">平均成交量</span>
                            <span class="metric-value">{{ market_data.volume_data.avg_volume|floatformat:0 }}亿</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">量比</span>
                            <span class="metric-value">{{ market_data.volume_data.volume_ratio|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 分数条动画
    const scoreFills = document.querySelectorAll('.score-fill');
    scoreFills.forEach((fill, index) => {
        const width = fill.style.width;
        fill.style.width = '0%';
        setTimeout(() => {
            fill.style.width = width;
        }, index * 200 + 500);
    });
    
    // 情绪卡片动画
    const sentimentCard = document.querySelector('.sentiment-card');
    if (sentimentCard) {
        sentimentCard.style.opacity = '0';
        sentimentCard.style.transform = 'scale(0.9)';
        setTimeout(() => {
            sentimentCard.style.transition = 'all 0.6s ease';
            sentimentCard.style.opacity = '1';
            sentimentCard.style.transform = 'scale(1)';
        }, 200);
    }
});
</script>
{% endblock %}
