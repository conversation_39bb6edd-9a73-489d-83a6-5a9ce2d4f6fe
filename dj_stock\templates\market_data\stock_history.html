{% extends "base.html" %} {% block title %}{{ stock.stock_name }}({{ stock.stock_code }}) - 历史行情数据{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">股票详情</div>
        <h2 class="page-title">{{ stock.stock_name }}({{ stock.stock_code }}) - 历史行情数据</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="btn btn-outline-primary">
            <i class="ti ti-arrow-back me-1"></i>
            返回股票详情
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 股票走势图表 -->
    {% include "components/universal_chart.html" with chart_title=stock.stock_name %}

    <div class="card">
      <div class="card-header">
        <h3 class="card-title">历史行情数据 ({{ total_records }})</h3>
        <div class="card-actions">
          <form method="get" action="{% url 'market_data:stock_history' stock.stock_code %}">
            <div class="input-group">
              <label class="input-group-text" for="sort-select">排序方式</label>
              <select id="sort-select" class="form-select" name="sort" onchange="this.form.submit()">
                <option value="-trade_date" {% if sort_by == "-trade_date" %}selected{% endif %}>交易日期 ↓</option>
                <option value="trade_date" {% if sort_by == "trade_date" %}selected{% endif %}>交易日期 ↑</option>
                <option value="-change_percent" {% if sort_by == "-change_percent" %}selected{% endif %}>涨跌幅 ↓</option>
                <option value="change_percent" {% if sort_by == "change_percent" %}selected{% endif %}>涨跌幅 ↑</option>
                <option value="-volume" {% if sort_by == "-volume" %}selected{% endif %}>成交量 ↓</option>
                <option value="volume" {% if sort_by == "volume" %}selected{% endif %}>成交量 ↑</option>
                <option value="-amount" {% if sort_by == "-amount" %}selected{% endif %}>成交额 ↓</option>
                <option value="amount" {% if sort_by == "amount" %}selected{% endif %}>成交额 ↑</option>
              </select>
            </div>
          </form>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table table-vcenter card-table table-striped">
          <thead>
            <tr>
              <th>交易日期</th>
              <th>开盘价</th>
              <th>收盘价</th>
              <th>最高价</th>
              <th>最低价</th>
              <th>涨跌幅</th>
              <th>涨跌额</th>
              <th>换手率</th>
              <th>成交量(万手)</th>
              <th>成交额(亿元)</th>
              <th>市盈率</th>
              <th>市净率</th>
            </tr>
          </thead>
          <tbody>
            {% for item in page_obj %}
            <tr>
              <td>{{ item.trade_date|date:"Y-m-d" }}</td>
              <td>{{ item.open_price|floatformat:2 }}</td>
              <td>{{ item.close_price|floatformat:2 }}</td>
              <td>{{ item.high_price|floatformat:2 }}</td>
              <td>{{ item.low_price|floatformat:2 }}</td>
              <td class="{% if item.change_percent > 0 %}text-danger{% elif item.change_percent < 0 %}text-success{% endif %}">
                {{ item.change_percent|floatformat:2 }}%
              </td>
              <td class="{% if item.change_amount > 0 %}text-danger{% elif item.change_amount < 0 %}text-success{% endif %}">
                {{ item.change_amount|floatformat:2 }}
              </td>
              <td>{{ item.turnover_rate|floatformat:2 }}%</td>
              <td>{{ item.volume|floatformat:2 }}</td>
              <td>{{ item.amount|floatformat:2 }}</td>
              <td>{{ item.pe_ratio|floatformat:2 }}</td>
              <td>{{ item.pb_ratio|floatformat:2 }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="12" class="text-center">暂无历史行情数据</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% if page_obj.has_other_pages %}
      <div class="card-footer d-flex align-items-center">
        <p class="m-0 text-muted">
          显示第 <span>{{ page_obj.start_index }}</span> 到 <span>{{ page_obj.end_index }}</span> 条，共 <span>{{ total_records }}</span> 条记录
        </p>
        <ul class="pagination m-0 ms-auto">
          {% if page_obj.has_previous %}
          <li class="page-item">
            <a class="page-link" href="?page=1{% if sort_by %}&sort={{ sort_by }}{% endif %}">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="icon"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M11 7l-5 5l5 5" />
                <path d="M17 7l-5 5l5 5" />
              </svg>
            </a>
          </li>
          <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="icon"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M15 6l-6 6l6 6" />
              </svg>
            </a>
          </li>
          {% endif %}

          {% for i in page_obj.paginator.page_range %}
            {% if i == page_obj.number %}
            <li class="page-item active">
              <a class="page-link" href="?page={{ i }}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ i }}</a>
            </li>
            {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
            <li class="page-item">
              <a class="page-link" href="?page={{ i }}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ i }}</a>
            </li>
            {% endif %}
          {% endfor %}

          {% if page_obj.has_next %}
          <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="icon"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M9 6l6 6l-6 6" />
              </svg>
            </a>
          </li>
          <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="icon"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M7 7l5 5l-5 5" />
                <path d="M13 7l5 5l-5 5" />
              </svg>
            </a>
          </li>
          {% endif %}
        </ul>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 初始化通用图表
  const chartData = {{ chart_data|safe }};

  if (chartData && chartData.length > 0) {
    window.initUniversalChart('universal-chart', chartData, {
      code: '{{ stock.stock_code }}',
      name: '{{ stock.stock_name }}',
      type: 'stock',
      apiUrl: '/market_data/api/stock-chart/{{ stock.stock_code }}/'
    });
  }
});
</script>

<style>
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }

  .period-btn.active {
    background-color: #206bc4;
    border-color: #206bc4;
    color: white;
  }

  .ma-checkbox:checked + label {
    font-weight: bold;
  }
</style>
{% endblock %}