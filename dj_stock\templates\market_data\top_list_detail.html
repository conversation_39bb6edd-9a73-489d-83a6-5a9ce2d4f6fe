{% extends 'base.html' %} {% block title %}{{ top_list.stock_name }}({{ top_list.stock_code }}) 龙虎榜详情 - 股票市场数据{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">{{ top_list.stock_name }}({{ top_list.stock_code }}) - 龙虎榜详情</h2>
        <div class="text-muted mt-1">上榜日期: {{ top_list.date|date:"Y-m-d" }}</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:top_list' %}" class="btn btn-outline-secondary">
            <i class="ti ti-arrow-left"></i>
            返回龙虎榜
          </a>
          <a href="{% url 'market_data:stock_detail' top_list.stock_code %}" class="btn btn-primary">
            <i class="ti ti-chart-line"></i>
            查看股票详情
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-md-4">
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">基本信息</h3>
            <div class="mb-2">
              <div class="row g-2">
                <div class="col-6">
                  <div class="form-label">股票代码</div>
                  <div>{{ top_list.stock_code }}</div>
                </div>
                <div class="col-6">
                  <div class="form-label">股票名称</div>
                  <div>{{ top_list.stock_name }}</div>
                </div>
              </div>
            </div>
            <div class="mb-2">
              <div class="row g-2">
                <div class="col-6">
                  <div class="form-label">上榜日期</div>
                  <div>{{ top_list.date|date:"Y-m-d" }}</div>
                </div>
                <div class="col-6">
                  <div class="form-label">收盘价</div>
                  <div>{{ top_list.close_price|floatformat:2 }}</div>
                </div>
              </div>
            </div>
            <div class="mb-2">
              <div class="row g-2">
                <div class="col-6">
                  <div class="form-label">涨跌幅</div>
                  <div class="{% if top_list.change_ratio > 0 %}text-danger fw-bold{% elif top_list.change_ratio < 0 %}text-success fw-bold{% endif %}">
                    {{ top_list.change_ratio|floatformat:2 }}%
                  </div>
                </div>
                <div class="col-6">
                  <div class="form-label">换手率</div>
                  <div>{{ top_list.turnover_rate|floatformat:2 }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-8">
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">龙虎榜交易统计</h3>
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <div class="form-label">龙虎榜净买额(万)</div>
                  <div class="h3 {% if top_list.net_buy > 0 %}text-danger fw-bold{% elif top_list.net_buy < 0 %}text-success fw-bold{% endif %}">
                    {{ top_list.net_buy|floatformat:2 }}
                  </div>
                </div>
                <div class="mb-3">
                  <div class="form-label">龙虎榜买入额(万)</div>
                  <div>{{ top_list.buy_amount|floatformat:2 }}</div>
                </div>
                <div class="mb-3">
                  <div class="form-label">龙虎榜卖出额(万)</div>
                  <div>{{ top_list.sell_amount|floatformat:2 }}</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <div class="form-label">龙虎榜成交额(万)</div>
                  <div>{{ top_list.total_turnover|floatformat:2 }}</div>
                </div>
                <div class="mb-3">
                  <div class="form-label">市场总成交额(万)</div>
                  <div>{{ top_list.market_total_turnover|floatformat:2 }}</div>
                </div>
                <div class="mb-3">
                  <div class="form-label">净买额占总成交比</div>
                  <div class="{% if top_list.net_buy_ratio > 0 %}text-danger fw-bold{% elif top_list.net_buy_ratio < 0 %}text-success fw-bold{% endif %}">
                    {{ top_list.net_buy_ratio|floatformat:2 }}%
                  </div>
                </div>
                <div class="mb-3">
                  <div class="form-label">成交额占总成交比</div>
                  <div>{{ top_list.turnover_ratio|floatformat:2 }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">上榜原因</h3>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <div class="form-label">上榜详细原因</div>
              <div>{{ top_list.detail_reason|default:"无" }}</div>
            </div>
            <div class="mb-3">
              <div class="form-label">上榜解读</div>
              <div>{{ top_list.reason|default:"无" }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">机构交易明细</h3>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter card-table">
                <thead>
                  <tr>
                    <th>机构名称</th>
                    <th>操作类型</th>
                    <th>成交金额(万)</th>
                    <th>净额(万)</th>
                  </tr>
                </thead>
                <tbody>
                  {% for org in org_details %}
                  <tr>
                    <td>{{ org.organization_name }}</td>
                    <td>
                      {% if org.operation == '买入' %}
                      <span class="badge bg-danger text-white"> <i class="ti ti-arrow-big-down-line me-1"></i>{{ org.operation }} </span>
                      {% elif org.operation == '卖出' %}
                      <span class="badge bg-success text-white"> <i class="ti ti-arrow-big-up-line me-1"></i>{{ org.operation }} </span>
                      {% else %}
                      <span class="badge bg-primary"> <i class="ti ti-exchange me-1"></i>{{ org.operation }} </span>
                      {% endif %}
                    </td>
                    <td>{{ org.amount|floatformat:2 }}</td>
                    <td class="{% if org.net_amount > 0 %}text-danger fw-bold{% elif org.net_amount < 0 %}text-success fw-bold{% endif %}">
                      {{ org.net_amount|floatformat:2 }}
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="4" class="text-center py-3">暂无机构交易明细数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
