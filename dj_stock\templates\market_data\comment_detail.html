{% extends 'base.html' %}

{% block title %}{{ comment.stock_name }}({{ comment.stock_code }}) 千股千评 - 股票市场数据{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          {{ comment.stock_name }}({{ comment.stock_code }}) - 千股千评
        </h2>
        <div class="text-muted mt-1">{{ comment.date|date:"Y-m-d" }} 综合评分数据</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'market_data:comment_list' %}" class="btn btn-outline-secondary">
            <i class="ti ti-arrow-left"></i>
            返回列表
          </a>
          <a href="{% url 'market_data:stock_detail' comment.stock_code %}" class="btn btn-primary">
            <i class="ti ti-chart-line"></i>
            查看股票详情
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-md-4">
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">基本信息</h3>
            <div class="mb-2">
              <div class="row g-2">
                <div class="col-6">
                  <div class="form-label">股票代码</div>
                  <div>{{ comment.stock_code }}</div>
                </div>
                <div class="col-6">
                  <div class="form-label">股票名称</div>
                  <div>{{ comment.stock_name }}</div>
                </div>
              </div>
            </div>
            <div class="mb-2">
              <div class="row g-2">
                <div class="col-6">
                  <div class="form-label">日期</div>
                  <div>{{ comment.date|date:"Y-m-d" }}</div>
                </div>
                <div class="col-6">
                  <div class="form-label">当前排名</div>
                  <div>{{ comment.current_rank }}</div>
                </div>
              </div>
            </div>
            <div class="mb-2">
              <div class="form-label">选择其他日期</div>
              <select class="form-select" id="date-selector">
                {% for date_obj in available_dates %}
                <option value="{{ date_obj|date:'Y-m-d' }}" {% if selected_date == date_obj|date:'Y-m-d' %}selected{% endif %}>{{ date_obj|date:'Y-m-d' }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-8">
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">综合评分</h3>
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <div class="form-label">综合得分</div>
                  <div class="progress mb-2">
                    <div class="progress-bar bg-primary" style="width: {{ comment.comprehensive_score|floatformat:0 }}%" role="progressbar" aria-valuenow="{{ comment.comprehensive_score|floatformat:0 }}" aria-valuemin="0" aria-valuemax="100">
                      {{ comment.comprehensive_score|floatformat:1 }}
                    </div>
                  </div>
                </div>
                <div class="mb-3">
                  <div class="form-label">机构参与度</div>
                  <div class="progress mb-2">
                    <div class="progress-bar bg-green" style="width: {{ comment.institution_participation|floatformat:0 }}%" role="progressbar" aria-valuenow="{{ comment.institution_participation|floatformat:0 }}" aria-valuemin="0" aria-valuemax="100">
                      {{ comment.institution_participation|floatformat:1 }}
                    </div>
                  </div>
                </div>
                <div class="mb-3">
                  <div class="form-label">关注指数</div>
                  <div class="progress mb-2">
                    <div class="progress-bar bg-azure" style="width: {{ comment.attention_index|floatformat:0 }}%" role="progressbar" aria-valuenow="{{ comment.attention_index|floatformat:0 }}" aria-valuemin="0" aria-valuemax="100">
                      {{ comment.attention_index|floatformat:1 }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <div class="form-label">市盈率</div>
                  <div>{{ comment.pe_ratio|floatformat:2 }}</div>
                </div>
                <div class="mb-3">
                  <div class="form-label">最新价</div>
                  <div>{{ comment.current_price|floatformat:2 }}</div>
                </div>
                <div class="mb-3">
                  <div class="form-label">涨跌幅</div>
                  <div class="{% if comment.change_ratio > 0 %}text-success{% elif comment.change_ratio < 0 %}text-danger{% endif %}">
                    {{ comment.change_ratio|floatformat:2 }}%
                  </div>
                </div>
                <div class="mb-3">
                  <div class="form-label">换手率</div>
                  <div>{{ comment.turnover_rate|floatformat:2 }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {% if industry %}
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">{{ industry }} 行业评分对比</h3>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-vcenter card-table">
                <thead>
                  <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>综合得分</th>
                    <th>机构参与度</th>
                    <th>关注指数</th>
                    <th>当前排名</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {% for other_comment in industry_comments %}
                  <tr class="{% if other_comment.stock_code == comment.stock_code %}table-active{% endif %}">
                    <td>{{ other_comment.stock_code }}</td>
                    <td>{{ other_comment.stock_name }}</td>
                    <td class="fw-bold">{{ other_comment.comprehensive_score|floatformat:1 }}</td>
                    <td>{{ other_comment.institution_participation|floatformat:1 }}</td>
                    <td>{{ other_comment.attention_index|floatformat:1 }}</td>
                    <td>{{ other_comment.current_rank }}</td>
                    <td>
                      <a href="{% url 'market_data:comment_detail' other_comment.stock_code %}?date={{ selected_date }}" class="btn btn-sm btn-outline-primary">
                        <i class="ti ti-eye"></i> 详情
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 日期选择器切换
    const dateSelector = document.getElementById('date-selector');
    dateSelector.addEventListener('change', function() {
      const selectedDate = this.value;
      window.location.href = "{% url 'market_data:comment_detail' comment.stock_code %}?date=" + selectedDate;
    });
  });
</script>
{% endblock %}
{% endblock %} 