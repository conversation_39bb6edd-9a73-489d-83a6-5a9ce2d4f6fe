# 股票数据分析系统 (Stock Data Analysis System)

[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/)
[![Django](https://img.shields.io/badge/Django-5.1.7+-green.svg)](https://www.djangoproject.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://www.mysql.com/)

这是一个基于 Django 5.1.7 开发的专业股票数据分析系统，提供全方位的股票市场数据分析、财务指标筛选、资金流向追踪、板块分析等功能。系统采用现代化的 Tabler UI 框架设计，支持丰富的数据可视化展示，并具有智能数据更新机制。

## 🎯 项目概述

本系统是一个功能完整的股票数据分析平台，集成了市场数据采集、财务分析、技术指标计算、资金流向追踪、板块分析等多个核心模块。系统通过 akshare 数据接口获取实时和历史股票数据，为投资者、分析师和金融从业者提供专业的数据分析工具。

### ✨ 核心特性

- 🚀 **实时数据更新**: 支持实时股票行情和财务数据更新，智能增量更新机制
- 📊 **数据可视化**: 基于 ECharts 的专业图表展示，支持K线图、趋势图、分布图等
- 🔍 **智能筛选**: 多维度财务指标筛选、技术指标筛选和股票筛选系统
- 📱 **响应式设计**: 支持桌面端和移动端访问，优化的用户体验
- 🔐 **用户系统**: 完整的用户认证、个人收藏和个性化设置功能
- ⚡ **高性能**: 数据库优化、缓存机制和异步处理确保快速响应
- 🎨 **现代化UI**: 采用渐变背景、毛玻璃效果、动画交互的现代化界面设计
- 📈 **专业分析**: 支持板块资金流向、市场情绪分析、投资机会发现等专业功能

## 🚀 主要功能

### 📈 市场数据模块 (market_data)

#### 股票数据管理
- **股票基础信息**: 4000+股票代码、名称、行业分类、上市日期等完整信息
- **实时行情数据**: 实时价格、涨跌幅、成交量、成交额等行情数据
- **历史行情查询**: 支持任意时间段的历史K线数据查询和图表展示
- **股票详情页面**: 集成基本信息、财务指标、K线图的综合详情页

#### 板块分析系统
- **行业板块分析**: 28个申万一级行业板块排行、成分股分析、资金流向追踪
- **概念板块分析**: 300+概念板块数据、热点概念追踪、主题投资机会发现
- **板块资金流向**: 实时板块资金净流入排行，支持多维度筛选和排序
- **板块轮动分析**: 板块间资金流向变化，把握市场轮动节奏

#### 资金流向分析
- **个股资金流向**: 主力资金、超大单、大单、中单、小单净流入分析
- **板块资金流向**: 行业和概念板块资金流向统计和趋势分析
- **市场整体资金流向**: 全市场资金流向汇总，市场情绪判断
- **资金流向详情**: 支持历史资金流向查询和趋势图表展示

#### 市场监控系统
- **涨跌停数据**: 每日涨停、跌停、一字板股票统计和分析
- **风险警示监控**: ST股票、*ST股票、退市风险股票实时监控
- **新股上市追踪**: IPO发行时间表、新股申购信息、上市表现分析
- **市场异动监控**: 异常波动股票、放量股票、价格异动提醒

#### 交易数据分析
- **活跃营业部**: 营业部交易排行、买卖行为分析、资金实力评估
- **龙虎榜数据**: 机构席位交易数据、游资操作轨迹分析
- **千股千评**: 机构评级、目标价格、投资建议汇总
- **融资融券**: 融资融券余额变化、标的股票筛选

#### 市场指数系统
- **主要指数行情**: 上证指数、深证成指、创业板指、科创50等主要指数
- **指数历史数据**: 支持指数历史走势查询和技术分析
- **指数成分股**: 各指数成分股构成和权重分析
- **市场宽度指标**: 上涨下跌家数、涨停跌停统计

### 💰 财务分析模块 (financial_analysis)

#### 财务指标体系
- **盈利能力指标**: 净利润、ROE、ROA、毛利率、净利率等核心盈利指标
- **成长性指标**: 营收增长率、净利润增长率、EPS增长率等成长性分析
- **财务健康度**: 资产负债率、流动比率、现金流量等财务安全性指标
- **估值指标**: PE、PB、PS、PEG等估值水平分析

#### 财务数据筛选
- **多维度筛选**: 支持20+财务指标的组合筛选和排序
- **智能筛选策略**: 预设价值投资、成长投资等经典筛选策略
- **行业对比分析**: 同行业公司财务指标对比和排名
- **历史数据追溯**: 支持2015年以来的完整财务数据查询

#### 投资分析工具
- **分红派息分析**: 股票分红历史、股息率、除权除息日期统计
- **股东结构分析**: 股东持股变化、机构持股统计、股权集中度分析
- **行业估值分析**: 行业PE分布、估值水平对比、投资价值评估
- **技术指标计算**: 常用技术分析指标计算和展示

#### 产业链分析
- **产业链图谱**: 上下游产业链关系展示和分析
- **供应链追踪**: 供应商和客户关系网络分析
- **行业景气度**: 产业链各环节景气度变化追踪

## 🎨 界面特色

### 现代化UI设计
- **渐变背景**: 采用紫色渐变背景，营造专业金融平台氛围
- **毛玻璃效果**: 主容器使用半透明背景和模糊效果，增强视觉层次
- **圆角设计**: 所有卡片和按钮都采用圆角设计，提升现代感
- **多层阴影**: 添加多层次阴影效果，增强立体感和深度
- **动画交互**: 卡片淡入动画、悬停效果、按钮交互动画

### 中国市场适配
- **红涨绿跌**: 严格按照中国市场习惯显示颜色（红色上涨，绿色下跌）
- **数值格式化**: 统一的数值显示格式，支持亿、万等中文单位
- **字体优化**: 使用适合中文显示的字体，提升可读性
- **本土化设计**: 符合中国用户使用习惯的界面布局和交互方式

### 数据可视化
- **专业图表**: 基于 ECharts 的K线图、趋势图、分布图等专业金融图表
- **交互式图表**: 支持缩放、平移、数据点悬停等丰富交互功能
- **多时间周期**: 支持日线、周线、月线等多种时间周期切换
- **技术指标叠加**: 支持均线、MACD、RSI等技术指标叠加显示

## 🔧 功能特点

### 📊 数据采集与处理
- **多数据源整合**: 集成 akshare、tushare 等多个数据源
- **智能增量更新**: 避免重复采集，智能判断数据更新时机
- **数据质量控制**: 完善的数据清洗、验证和异常处理机制
- **实时数据同步**: 交易时间内实时更新行情数据

### 🔍 智能筛选系统
- **多维度筛选**: 支持基本面、技术面、资金面等多维度组合筛选
- **预设策略模板**: 内置价值投资、成长投资等经典投资策略
- **自定义筛选**: 支持用户自定义筛选条件和权重配置
- **快捷筛选**: 一键筛选热门板块、强势股票等投资机会

### 📈 投资分析工具
- **板块轮动分析**: 实时追踪板块资金流向，把握市场轮动节奏
- **资金流向追踪**: 多层次资金流向分析，洞察主力资金动向
- **估值分析**: 多维度估值指标，识别低估值投资机会
- **风险监控**: 实时风险预警，规避投资风险

### 👤 用户体验优化
- **个性化收藏**: 支持股票收藏、自选股管理
- **智能推荐**: 基于用户行为的个性化股票推荐
- **数据导出**: 支持筛选结果导出为Excel、CSV等格式
- **移动端适配**: 完美支持手机、平板等移动设备访问

## 💻 技术栈

### 🔧 后端技术

| 技术栈 | 版本 | 用途 |
|--------|------|------|
| **Python** | 3.12+ | 主要开发语言 |
| **Django** | 5.1.7 | Web应用框架 |
| **MySQL** | 8.0+ | 主数据库 |
| **mysqlclient** | 2.2.7 | MySQL数据库连接器 |
| **akshare** | 1.16.65 | 股票数据API接口 |
| **pandas** | 2.2.3 | 数据处理和分析 |
| **numpy** | 2.2.4 | 数值计算 |
| **aiohttp** | 3.11.14 | 异步HTTP客户端 |
| **requests** | 2.32.3 | HTTP请求库 |
| **beautifulsoup4** | 4.13.3 | HTML解析 |
| **lxml** | 5.3.1 | XML/HTML解析器 |

### 🎨 前端技术

| 技术栈 | 版本 | 用途 |
|--------|------|------|
| **Tabler UI** | Latest | 现代化UI框架 |
| **Bootstrap** | 5.x | 响应式布局框架 |
| **ECharts** | 5.x | 专业图表库 |
| **JavaScript** | ES6+ | 前端交互逻辑 |
| **jQuery** | 3.x | DOM操作和AJAX |
| **CSS3** | - | 样式和动画效果 |

### 🛠️ 开发工具

| 工具 | 用途 |
|------|------|
| **Django ORM** | 数据库操作 |
| **原生SQL** | 复杂查询优化 |
| **Django Debug Toolbar** | 开发调试 |
| **Python logging** | 日志系统 |
| **pip/pyproject.toml** | 包管理 |

### 📡 数据采集架构

| 特性 | 描述 |
|------|------|
| **多数据源支持** | 集成 akshare、tushare 等多个数据源 |
| **智能增量更新** | 避免重复采集，90天自动检查更新时机 |
| **错误处理机制** | 完善的重试机制和异常处理 |
| **数据质量控制** | 数据验证、清洗和一致性检查 |
| **状态监控** | 实时监控数据采集状态和日志记录 |
| **异步处理** | 支持异步数据采集，提升系统性能 |

### 🎯 数据展示特性

| 特性 | 描述 |
|------|------|
| **响应式设计** | 完美适配桌面端、平板、手机等设备 |
| **图表可视化** | 基于ECharts的专业金融图表展示 |
| **智能筛选** | 多维度数据筛选和排序功能 |
| **分页组件** | 标准化分页，支持自定义每页记录数 |
| **数据导出** | 支持Excel、CSV等格式数据导出 |
| **实时更新** | 交易时间内数据实时刷新 |

### ⚡ 性能优化策略

| 优化方向 | 具体措施 |
|----------|----------|
| **数据库优化** | 索引优化、复合索引、查询优化 |
| **缓存机制** | Redis缓存、查询结果缓存、静态资源缓存 |
| **异步处理** | 异步数据加载、后台任务处理 |
| **分页优化** | 高效分页算法，支持大数据量处理 |
| **连接池管理** | 数据库连接池，减少连接开销 |
| **前端优化** | 资源压缩、懒加载、CDN加速 |

## 🏗️ 系统架构

### 📦 应用模块架构

```
dj_stock/
├── 🏠 market_data/          # 市场数据模块
├── 💰 financial_analysis/   # 财务分析模块
├── 🎨 templates/           # 模板文件
├── 📊 static/              # 静态资源
└── 🔧 utils/               # 工具函数
```

#### 📈 market_data（市场数据模块）

**URL前缀**: `/market_data/`

##### 🏠 核心功能页面

| 功能模块 | 页面路径 | 功能描述 |
|----------|----------|----------|
| **首页** | `/` | 市场概览、主要指数、涨跌幅榜单 |

##### 📊 股票数据管理
| 页面路径 | 功能描述 |
|----------|----------|
| `/stocks/` | 股票列表 - 支持多条件筛选和排序 |
| `/stocks/<code>/` | 股票详情 - 基本信息、K线图、财务指标 |
| `/stock-history/<code>/` | 股票历史 - 历史行情数据查询和图表 |

##### 🏢 板块分析系统
| 页面路径 | 功能描述 |
|----------|----------|
| `/industry-board/` | 行业板块列表 - 行业涨跌幅排行 |
| `/industry-board/<code>/` | 行业板块详情 - 成分股查询和分析 |
| `/concept-board/` | 概念板块列表 - 概念板块涨跌幅排行 |
| `/concept-board/<code>/` | 概念板块详情 - 成分股查询和分析 |
| `/sector-analysis/` | 板块资金分析 - 综合板块资金流向分析 |
| `/industry-chain/` | 产业链列表 - 产业链数据展示 |

##### 🚨 市场监控系统
| 页面路径 | 功能描述 |
|----------|----------|
| `/risk-warning/` | 风险预警 - ST和*ST股票监控 |
| `/ipo-list/` | 新股上市 - IPO信息查询 |
| `/limit-list/` | 涨跌停列表 - 每日涨跌停股票统计 |

##### 📈 指数数据中心
| 页面路径 | 功能描述 |
|----------|----------|
| `/market-index/` | 市场指数列表 - 主要指数行情 |
| `/market-index/<code>/` | 市场指数详情 - 指数历史数据和图表 |

##### 💸 资金流向分析
| 页面路径 | 功能描述 |
|----------|----------|
| `/market-fund-flow/` | 市场资金流向 - 市场整体资金流向 |
| `/sector-fund-flow/` | 板块资金流向 - 各板块资金流向排行 |
| `/stock-fund-flow/` | 个股资金流向 - 个股资金流向数据 |
| `/stock-fund-flow/<code>/` | 个股资金详情 - 单只股票资金流向历史 |

##### 🎯 交易数据分析
| 页面路径 | 功能描述 |
|----------|----------|
| `/dragon-tiger/` | 龙虎榜列表 - 龙虎榜交易数据 |
| `/dragon-tiger/<code>/` | 龙虎榜详情 - 单只股票龙虎榜记录 |
| `/active-broker/` | 活跃营业部列表 - 活跃营业部交易数据 |
| `/active-broker/<code>/` | 活跃营业部详情 - 单个营业部交易记录 |

##### 📋 其他数据服务
| 页面路径 | 功能描述 |
|----------|----------|
| `/stock-comment/` | 千股千评列表 - 机构评级和目标价格 |
| `/stock-comment/<code>/` | 千股千评详情 - 单只股票评级历史 |
| `/margin-trading/` | 融资融券列表 - 融资融券数据概览 |
| `/margin-trading/<code>/` | 融资融券详情 - 单只股票融资融券数据 |
| `/hk-connect/` | 港股通数据 - 港股通持股和成交数据 |
| `/data-statistics/` | 数据统计 - 市场统计和可视化 |

#### 💰 financial_analysis（财务分析模块）

**URL前缀**: `/financial-analysis/`

##### 📊 财务数据分析
| 页面路径 | 功能描述 |
|----------|----------|
| `/financial-report/` | 财务报表列表 - 财务报表数据查询和对比 |
| `/financial-indicator/<code>/` | 财务指标详情 - 单只股票财务指标分析 |
| `/financial-screener/` | 财务筛选器 - 多维度财务指标筛选系统 |

##### 💵 分红派息分析
| 页面路径 | 功能描述 |
|----------|----------|
| `/dividend/` | 分红记录 - 历史分红派息查询和统计 |

##### 👥 股东结构分析
| 页面路径 | 功能描述 |
|----------|----------|
| `/shareholder-statistics/<code>/` | 股东统计 - 股东结构和变动分析 |
| `/shareholder-detail/` | 股东详情列表 - 主要股东持股变动 |
| `/shareholder-detail/<code>/` | 股东详情 - 单只股票股东详情 |

##### 📈 技术分析工具
| 页面路径 | 功能描述 |
|----------|----------|
| `/technical-indicators/` | 技术指标列表 - 技术指标计算与展示 |
| `/technical-indicators/<pk>/` | 技术指标详情 - 单个技术指标详情 |

##### 🏭 行业分析系统
| 页面路径 | 功能描述 |
|----------|----------|
| `/industry-pe/` | 行业PE列表 - 行业估值水平分析 |
| `/industry-chains/` | 产业链列表 - 产业链关系图谱 |
| `/industry-chains/<chain_code>/` | 产业链详情 - 产业链详细信息 |

##### 👤 用户个性化功能
| 页面路径 | 功能描述 |
|----------|----------|
| `/favorites/` | 股票收藏 - 用户收藏的股票列表 |
| `/batch-favorite/` | 批量收藏 - 批量添加收藏功能 |
| `/ajax-add-favorite/` | AJAX收藏 - 异步添加收藏 |

## 📊 核心数据指标

### 🎯 市场概览指标

| 指标类别 | 具体指标 | 功能描述 |
|----------|----------|----------|
| **市场分布** | 行业分布统计 | 各行业股票数量和市值占比分析 |
| **成交数据** | 市场成交量趋势 | 市场成交量和成交额变化追踪 |
| **板块表现** | 行业板块涨跌幅 | 各行业涨跌幅对比和排行 |
| **概念热度** | 概念板块涨跌幅 | 各概念涨跌幅对比和热度分析 |
| **估值水平** | PE分布统计 | 市场整体估值水平分布 |
| **风险监控** | 风险预警股票 | ST和*ST股票实时监控 |
| **异动监控** | 涨跌停统计 | 每日涨跌停数量和占比分析 |

### 📈 技术分析指标

| 指标类别 | 具体指标 | 功能描述 |
|----------|----------|----------|
| **指数对比** | 市场指数走势 | 上证、深证、创业板指数对比 |
| **活跃度** | 成交量成交额 | 市场活跃度和流动性分析 |
| **资金流向** | 多层次资金流向 | 市场、行业和个股资金流向 |
| **机构行为** | 活跃营业部 | 营业部交易行为和资金实力分析 |
| **主力动向** | 龙虎榜数据 | 机构和游资操作轨迹分析 |

### 💰 财务分析指标

| 指标类别 | 具体指标 | 功能描述 |
|----------|----------|----------|
| **盈利能力** | ROE、ROA、净利率 | 企业盈利能力综合评估 |
| **成长性** | 营收增长、净利润增长 | 企业成长性和发展潜力分析 |
| **财务健康** | 负债率、流动比率 | 企业财务安全性评估 |
| **估值水平** | PE、PB、PEG | 企业估值水平和投资价值分析 |
| **分红能力** | 股息率、分红比例 | 企业分红政策和股东回报分析 |

## 🔄 数据更新策略

### 📊 智能数据更新机制

| 数据类型 | 更新频率 | 更新策略 | 特殊机制 |
|----------|----------|----------|----------|
| **财务数据** | 季度更新 | 财报公布后自动更新 | 90天智能检测 |
| **行情数据** | 实时更新 | 交易时段实时获取 | 收盘后历史数据补全 |
| **板块数据** | 周度更新 | 每周更新成分股 | 实时获取涨跌幅 |
| **资金流向** | 日度更新 | 收盘后自动更新 | 支持历史数据回填 |
| **营业部数据** | 日度更新 | 收盘后自动更新 | 活跃度实时监控 |

### 🎯 数据质量保障

| 保障措施 | 具体实现 |
|----------|----------|
| **增量更新** | 只更新新增数据，避免重复采集 |
| **错误重试** | 自动重试机制，确保数据完整性 |
| **数据验证** | 多层数据验证，确保数据准确性 |
| **状态监控** | 实时监控数据采集状态 |
| **日志记录** | 完整的操作日志和错误日志 |

### ⏰ 更新时间表

| 时间段 | 更新内容 |
|--------|----------|
| **交易时间** | 实时行情、资金流向、板块数据 |
| **收盘后** | 当日历史数据、龙虎榜、营业部数据 |
| **周末** | 板块成分股、基础数据维护 |
| **财报季** | 财务指标、业绩数据更新 |

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 推荐配置 |
|------|----------|----------|
| **Python** | 3.12+ | 3.12.x (最新稳定版) |
| **Django** | 5.1.7+ | 5.1.7 |
| **MySQL** | 8.0+ | 8.0.x |
| **内存** | 4GB+ | 8GB+ |
| **存储** | 10GB+ | 50GB+ (含历史数据) |
| **网络** | 稳定互联网 | 支持API访问 |

### 📦 核心依赖包

| 包名 | 版本 | 用途 |
|------|------|------|
| **Django** | >=5.1.7 | Web框架 |
| **akshare** | >=1.16.65 | 股票数据API |
| **pandas** | >=2.2.3 | 数据处理 |
| **numpy** | >=2.2.4 | 数值计算 |
| **mysqlclient** | >=2.2.7 | MySQL连接器 |
| **aiohttp** | >=3.11.14 | 异步HTTP |
| **requests** | >=2.32.3 | HTTP请求 |
| **beautifulsoup4** | >=4.13.3 | HTML解析 |
| **lxml** | >=5.3.1 | XML解析 |

> 📝 **注意**: 完整依赖列表请查看 `requirements.txt` 和 `pyproject.toml` 文件

### 🛠️ 安装步骤

#### 1️⃣ 获取源码
```bash
# 克隆项目仓库
git clone https://github.com/yourusername/dj_stock.git
cd dj_stock
```

#### 2️⃣ 环境准备
```bash
# 创建Python虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

#### 3️⃣ 安装依赖
```bash
# 安装Python依赖包
pip install -r requirements.txt

# 或使用 pip-tools (推荐)
pip install pip-tools
pip-sync requirements.txt
```

#### 4️⃣ 数据库配置
编辑 `dj_stock/settings.py` 文件：

```python
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "stock_data",           # 数据库名
        "USER": "your_username",        # 数据库用户名
        "PASSWORD": "your_password",    # 数据库密码
        "HOST": "localhost",            # 数据库主机
        "PORT": "3306",                # 数据库端口
        "OPTIONS": {
            "charset": "utf8mb4",
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

#### 5️⃣ 初始化系统
```bash
# 创建日志目录
mkdir -p logs

# 生成数据库迁移文件
python manage.py makemigrations

# 执行数据库迁移
python manage.py migrate

# 创建超级管理员账户
python manage.py createsuperuser
```

#### 6️⃣ 启动服务
```bash
# 启动开发服务器
python manage.py runserver

# 或指定端口
python manage.py runserver 0.0.0.0:8000
```

#### 7️⃣ 访问系统
| 服务 | 地址 | 说明 |
|------|------|------|
| **主系统** | http://127.0.0.1:8000 | 股票数据分析系统 |
| **管理后台** | http://127.0.0.1:8000/admin | Django管理后台 |
| **API测试** | http://127.0.0.1:8000/test_api.html | API接口测试页面 |

### 📊 数据初始化

#### 🎯 基础数据初始化
```bash
# 注意：以下命令需要根据实际情况调整
# 系统会自动通过API获取数据，无需手动初始化

# 如果需要手动初始化（可选）：
# python manage.py init_stocks        # 初始化股票列表
# python manage.py init_sectors       # 初始化板块数据
# python manage.py fetch_history_data # 获取历史行情
# python manage.py fetch_financial_data # 获取财务数据
```

#### 🔧 开发工具集

项目提供了丰富的开发和维护工具：

| 工具脚本 | 功能描述 | 使用场景 |
|----------|----------|----------|
| `check_indicators.py` | 财务指标检查 | 验证财务指标完整性 |
| `check_indicators_match.py` | 指标映射检查 | 检查API映射关系 |
| `check_data_encoding.py` | 数据编码检查 | 诊断编码问题 |
| `refresh_stock_data.py` | 批量数据刷新 | 批量更新股票数据 |

```bash
# 使用示例
python check_indicators.py          # 检查财务指标
python check_indicators_match.py    # 检查指标映射
python check_data_encoding.py       # 检查数据编码
python refresh_stock_data.py        # 刷新股票数据
```

> 📖 **详细说明**: 完整的工具使用说明请参考 [TOOLS_README.md](TOOLS_README.md)

## 📁 项目结构

```
dj_stock/                           # 🏠 项目根目录
├── 🔧 dj_stock/                    # Django项目配置
│   ├── settings.py                 # ⚙️ 项目设置(数据库、中间件、应用配置)
│   ├── urls.py                     # 🌐 主URL路由配置
│   ├── wsgi.py                     # 🚀 WSGI配置
│   └── asgi.py                     # ⚡ ASGI配置
├── 📈 market_data/                 # 市场数据应用
│   ├── models.py                   # 🗃️ 数据模型(股票基础、板块、资金流向等)
│   ├── admin.py                    # 🛠️ Django管理后台配置
│   ├── views/                      # 👁️ 视图模块
│   │   ├── stock_views.py          # 📊 股票相关视图
│   │   ├── board_views.py          # 🏢 板块相关视图
│   │   ├── fund_flow_views.py      # 💰 资金流向视图
│   │   ├── broker_views.py         # 🏦 营业部视图
│   │   ├── index_views.py          # 📈 指数视图
│   │   ├── sector_analysis_views.py # 🔍 板块分析视图
│   │   └── views.py                # 🎯 其他视图
│   ├── utils/                      # 🔧 工具函数
│   │   ├── stock_fetcher.py        # 📡 股票数据获取
│   │   └── pagination.py          # 📄 分页工具
│   ├── migrations/                 # 🗂️ 数据库迁移文件
│   └── urls.py                     # 🔗 URL配置
├── 💰 financial_analysis/          # 财务分析应用
│   ├── models.py                   # 🗃️ 财务数据模型
│   ├── admin.py                    # 🛠️ Django管理后台配置
│   ├── views/                      # 👁️ 视图模块
│   │   ├── views.py                # 📊 主要视图
│   │   └── shareholder_views.py    # 👥 股东视图
│   ├── utils/                      # 🔧 工具函数
│   │   └── financial_fetcher.py    # 📡 财务数据获取
│   ├── migrations/                 # 🗂️ 数据库迁移文件
│   └── urls.py                     # 🔗 URL配置
├── 🎨 static/                      # 静态资源
│   ├── css/                        # 🎨 CSS样式文件
│   │   ├── tabler.min.css          # 🎭 Tabler UI框架
│   │   ├── custom.css              # ✨ 自定义样式
│   │   ├── financial_analysis/     # 💰 财务分析专用样式
│   │   └── market-widgets.css      # 📊 市场组件样式
│   ├── js/                         # ⚡ JavaScript脚本
│   │   ├── tabler.min.js           # 🎭 Tabler UI脚本
│   │   ├── echarts.min.js          # 📈 ECharts图表库
│   │   └── market_index_chart.js   # 📊 市场指数图表
│   └── img/                        # 🖼️ 图片资源
├── 📄 templates/                   # 模板文件
│   ├── base.html                   # 🏗️ 基础模板
│   ├── login.html                  # 🔐 登录页面
│   ├── login_base.html             # 🔐 登录基础模板
│   ├── market_data/                # 📈 市场数据模板
│   │   ├── index.html              # 🏠 首页
│   │   ├── stock_list.html         # 📊 股票列表
│   │   ├── stock_detail.html       # 📋 股票详情
│   │   ├── sector_analysis.html    # 🔍 板块分析
│   │   ├── sector_fund_flow.html   # 💰 板块资金流向
│   │   └── ...                     # 📁 其他模板
│   ├── financial_analysis/         # 💰 财务分析模板
│   │   ├── financial_screener.html # 🔍 财务筛选器
│   │   ├── financial_indicator.html # 📊 财务指标
│   │   └── ...                     # 📁 其他模板
│   ├── includes/                   # 🧩 公共组件模板
│   │   ├── pagination.html         # 📄 分页组件
│   │   └── ...                     # 🧩 其他组件
│   └── components/                 # 🔧 可复用组件
│       ├── fund_flow_chart.html    # 💰 资金流向图表
│       └── universal_chart.html    # 📈 通用图表组件
├── 📊 logs/                        # 日志文件
│   ├── django_debug.log            # 🐛 Django调试日志
│   └── financial_analysis.log      # 💰 财务分析日志
├── 🧪 test_reports/                # 测试报告
├── 📋 requirements.txt             # 依赖包列表
├── ⚙️ pyproject.toml               # 项目配置文件
├── 🎯 manage.py                    # Django管理脚本
├── 📊 stock.py                     # 股票数据处理脚本
├── 🔄 refresh_stock_data.py        # 股票数据刷新工具
├── 🔍 check_indicators.py          # 财务指标检查工具
├── 🔗 check_indicators_match.py    # 指标映射检查工具
├── 🔤 check_data_encoding.py       # 数据编码检查工具
├── 📖 TOOLS_README.md              # 开发工具说明
├── 📈 STOCK_SCREENING_ENHANCEMENT.md # 选股功能增强方案
└── 📚 README.md                    # 项目说明文档
```

### 🎯 核心目录说明

| 目录 | 功能描述 |
|------|----------|
| **market_data/** | 市场数据核心模块，包含股票、板块、资金流向等功能 |
| **financial_analysis/** | 财务分析模块，包含财务指标、筛选、收藏等功能 |
| **templates/** | Django模板文件，采用模块化设计 |
| **static/** | 静态资源，包含CSS、JS、图片等 |
| **logs/** | 系统日志文件，便于调试和监控 |

## 🗄️ 数据库设计

### 📊 数据表架构

#### 📈 market_data 应用数据表

| 表名 | 功能描述 | 核心字段 |
|------|----------|----------|
| **stock_basic** | 股票基础信息 | stock_code, stock_name, industry, market |
| **stock_daily_quote** | 股票日行情数据 | stock_code, trade_date, open, close, high, low, volume |
| **stock_industry_board** | 行业板块数据 | board_code, board_name, change_percent, market_value |
| **stock_board_concept** | 概念板块数据 | board_code, board_name, change_percent, leading_stock |
| **stock_industry_board_relation** | 行业板块成分股 | board_code, stock_code, weight |
| **stock_board_concept_relation** | 概念板块成分股 | board_code, stock_code, weight |
| **stock_fund_flow** | 个股资金流向 | stock_code, net_inflow, main_inflow, retail_inflow |
| **stock_sector_fund_flow** | 板块资金流向 | sector_name, net_inflow_amount, main_net_inflow_pct |
| **stock_market_fund_flow** | 市场资金流向 | trade_date, net_inflow, main_inflow, retail_inflow |
| **stock_limit_list** | 涨跌停数据 | stock_code, limit_type, first_time, last_time |
| **stock_active_broker** | 活跃营业部数据 | broker_name, buy_amount, sell_amount, net_amount |
| **stock_top_list** | 龙虎榜数据 | stock_code, reason, buy_amount, sell_amount |
| **stock_comment** | 千股千评数据 | stock_code, rating, target_price, current_price |
| **stock_margin** | 融资融券数据 | stock_code, margin_balance, short_balance |
| **hk_stock_connect** | 港股通数据 | stock_code, hold_amount, hold_ratio |
| **stock_market_index** | 市场指数数据 | index_code, index_name, close, change_percent |
| **stock_ipo** | IPO信息 | stock_code, listing_date, issue_price, first_day_close |

#### 💰 financial_analysis 应用数据表

| 表名 | 功能描述 | 核心字段 |
|------|----------|----------|
| **stock_financial_indicator** | 股票财务指标（核心表） | stock_code, report_date, roe, net_profit, revenue |
| **stock_dividend** | 股票分红数据 | stock_code, dividend_date, dividend_amount, dividend_ratio |
| **stock_shareholder_statistics** | 股东统计数据 | stock_code, total_holders, avg_hold_amount |
| **stock_shareholder_detail** | 股东持股明细 | stock_code, holder_name, hold_amount, hold_ratio |
| **stock_industry_pe** | 行业市盈率数据 | industry_name, pe_ratio, pb_ratio, market_value |
| **technical_indicator** | 技术指标数据 | stock_code, indicator_name, indicator_value |
| **stock_industry_chain** | 产业链数据 | chain_name, upstream, downstream, core_companies |
| **stock_favorite** | 用户收藏股票 | user_id, stock_code, stock_name, create_time |

### ⚙️ 数据库配置

#### 🔧 MySQL配置示例
```python
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "stock_data",              # 数据库名称
        "USER": "your_username",           # 数据库用户名
        "PASSWORD": "your_password",       # 数据库密码
        "HOST": "localhost",               # 数据库主机
        "PORT": "3306",                   # 数据库端口
        "OPTIONS": {
            "charset": "utf8mb4",          # 字符集
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

#### 📈 数据库特性
| 特性 | 说明 |
|------|------|
| **字符集** | utf8mb4，支持完整的Unicode字符 |
| **存储引擎** | InnoDB，支持事务和外键 |
| **索引优化** | 核心查询字段建立索引，提升查询性能 |
| **数据完整性** | 外键约束和数据验证确保数据一致性 |

## 部署说明

### 生产环境部署

1. **服务器要求**

   - Linux服务器（推荐Ubuntu 20.04+或CentOS 7+）
   - Python 3.12+
   - MySQL 8.0+
   - Nginx（用于静态文件服务）
   - 至少4GB内存，20GB存储空间
2. **部署步骤**

   ```bash
   # 1. 克隆代码
   git clone <repository_url>
   cd dj_stock

   # 2. 创建虚拟环境
   python3 -m venv venv
   source venv/bin/activate

   # 3. 安装依赖
   pip install -r requirements.txt

   # 4. 配置数据库
   # 编辑 dj_stock/settings.py 中的数据库配置

   # 5. 运行迁移
   python manage.py migrate

   # 6. 收集静态文件
   python manage.py collectstatic

   # 7. 创建超级用户
   python manage.py createsuperuser

   # 8. 使用Gunicorn启动
   gunicorn dj_stock.wsgi:application --bind 0.0.0.0:8000
   ```
3. **Nginx配置示例**

   ```nginx
   server {
       listen 80;
       server_name your_domain.com;

       location /static/ {
           alias /path/to/dj_stock/static/;
       }

       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 维护和更新

### 数据更新策略

1. **实时数据**: 交易时间内每分钟更新行情数据
2. **日度数据**: 每日收盘后更新当日数据
3. **财务数据**: 每季度财报发布后更新
4. **基础数据**: 每周更新股票列表、板块成分股等

### 性能优化建议

1. **数据库优化**

   - 定期清理过期数据
   - 优化查询索引
   - 使用数据库连接池
2. **缓存策略**

   - 使用Redis缓存热点数据
   - 静态文件CDN加速
   - 数据库查询结果缓存
3. **监控和日志**

   - 配置应用性能监控
   - 设置错误日志告警
   - 定期备份数据库

## 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目。

### 开发规范

1. **代码风格**: 遵循PEP 8规范
2. **提交信息**: 使用清晰的提交信息
3. **测试**: 添加必要的单元测试
4. **文档**: 更新相关文档

### 问题反馈

如果遇到问题，请提供以下信息：

- 操作系统和Python版本
- 错误信息和堆栈跟踪
- 复现步骤
- 相关配置信息

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 文档地址: [Documentation]

---

**注意**: 本系统仅供学习和研究使用，不构成投资建议。使用本系统进行投资决策的风险由用户自行承担。
