{% extends 'base.html' %} {% block title %}{{ industry_name }} 行业股票 - 股票市场数据{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">{{ industry_name }} 行业股票</h2>
        <div class="text-muted mt-1">行情数据日期: {{ latest_date|date:"Y-m-d" }}</div>
      </div>
      <div class="col-auto ms-auto">
        <div class="btn-list">
          <a href="{% url 'market_data:stock_list' %}" class="btn btn-outline-primary">
            <i class="ti ti-arrow-left"></i>
            返回股票列表
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="page-body">
  <div class="container-xl">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">股票列表 - 共 {{ total_stocks }} 支</h3>
        <div class="card-actions btn-group">
          <a href="?sort=stock_code" class="btn {% if sort_by == 'stock_code' %}btn-primary{% else %}btn-outline-primary{% endif %}">代码 ↑</a>
          <a href="?sort=-latest_price" class="btn {% if sort_by == '-latest_price' %}btn-primary{% else %}btn-outline-primary{% endif %}">价格 ↓</a>
          <a href="?sort=-change_percent" class="btn {% if sort_by == '-change_percent' %}btn-primary{% else %}btn-outline-primary{% endif %}">涨幅 ↓</a>
          <a href="?sort=-turnover_rate" class="btn {% if sort_by == '-turnover_rate' %}btn-primary{% else %}btn-outline-primary{% endif %}">换手率 ↓</a>
          <a href="?sort=-market_value" class="btn {% if sort_by == '-market_value' %}btn-primary{% else %}btn-outline-primary{% endif %}">市值 ↓</a>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table table-vcenter card-table">
          <thead>
            <tr>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>最新价</th>
              <th>涨跌幅</th>
              <th>涨跌额</th>
              <th>成交量(万手)</th>
              <th>成交额(亿)</th>
              <th>换手率</th>
              <th>市盈率</th>
              <th>市值(亿)</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for stock in page_obj %}
            <tr>
              <td>{{ stock.stock_code }}</td>
              <td>{{ stock.stock_name }}</td>
              <td>{{ stock.latest_price|floatformat:2 }}</td>
              <td class="{% if stock.change_percent > 0 %}text-danger{% elif stock.change_percent < 0 %}text-success{% endif %}">
                {{ stock.change_percent|floatformat:2 }}%
              </td>
              <td class="{% if stock.change_amount > 0 %}text-danger{% elif stock.change_amount < 0 %}text-success{% endif %}">
                {{ stock.change_amount|floatformat:2 }}
              </td>
              <td>{{ stock.volume|floatformat:2 }}</td>
              <td>{{ stock.amount|floatformat:2 }}</td>
              <td>{{ stock.turnover_rate|floatformat:2 }}%</td>
              <td>{{ stock.pe_ratio|floatformat:2 }}</td>
              <td>{{ stock.market_value|floatformat:2 }}</td>
              <td>
                <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="btn btn-primary btn-sm">
                  <i class="ti ti-chart-line"></i>
                  详情
                </a>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="11" class="text-center">暂无股票数据</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% include "includes/pagination.html" with page_obj=page_obj %}
    </div>
  </div>
</div>
{% endblock %}
