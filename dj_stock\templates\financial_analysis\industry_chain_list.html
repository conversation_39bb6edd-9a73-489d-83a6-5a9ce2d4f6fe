{% extends "base.html" %} {% block title %}产业链 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="page-pretitle">财务分析</div>
        <h2 class="page-title">产业链</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{% url 'home' %}" class="btn btn-outline-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="icon"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M9 13l-4 -4l4 -4m-4 4h11a4 4 0 0 1 0 8h-1" />
            </svg>
            返回首页
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">产业链列表</h3>
          </div>
          <div class="card-body">
            <form method="get" class="mb-4">
              <div class="row g-3">
                <div class="col-md-3">
                  <input type="date" name="date" class="form-control" value="{{ request.GET.date|default:'' }}" placeholder="日期" />
                </div>
                <div class="col-md-3">
                  <input type="text" name="search" class="form-control" value="{{ request.GET.search|default:'' }}" placeholder="搜索产业链名称" />
                </div>
                <div class="col-md-2">
                  <button type="submit" class="btn btn-primary w-100">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" />
                      <path d="M21 21l-6 -6" />
                    </svg>
                    搜索
                  </button>
                </div>
              </div>
            </form>

            <div class="table-responsive">
              <table class="table table-vcenter card-table">
                <thead>
                  <tr>
                    <th>产业链名称</th>
                    <th>日期</th>
                    <th>涨跌幅</th>
                    <th>成交额</th>
                    <th>换手率</th>
                    <th>领涨股</th>
                    <th>领涨股涨跌幅</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {% for chain in chains %}
                  <tr>
                    <td>{{ chain.chain_name }}</td>
                    <td>{{ chain.date|date:"Y-m-d" }}</td>
                    <td>
                      {% if chain.change_percent > 0 %}
                      <span class="text-success">+{{ chain.change_percent|floatformat:2 }}%</span>
                      {% elif chain.change_percent < 0 %}
                      <span class="text-danger">{{ chain.change_percent|floatformat:2 }}%</span>
                      {% else %}
                      <span>{{ chain.change_percent|floatformat:2 }}%</span>
                      {% endif %}
                    </td>
                    <td>{{ chain.turnover|floatformat:2 }}亿</td>
                    <td>{{ chain.turnover_rate|floatformat:2 }}%</td>
                    <td>{{ chain.leading_stock }}</td>
                    <td>
                      {% if chain.leading_stock_change > 0 %}
                      <span class="text-success">+{{ chain.leading_stock_change|floatformat:2 }}%</span>
                      {% elif chain.leading_stock_change < 0 %}
                      <span class="text-danger">{{ chain.leading_stock_change|floatformat:2 }}%</span>
                      {% else %}
                      <span>{{ chain.leading_stock_change|floatformat:2 }}%</span>
                      {% endif %}
                    </td>
                    <td>
                      <a href="{% url 'financial_analysis:industry_chain_detail' chain.chain_code %}" class="btn btn-primary btn-sm"> 查看详情 </a>
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="8" class="text-center">暂无产业链数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            {% if chains.has_other_pages %}
            <div class="card-footer d-flex align-items-center">
              <p class="m-0 text-muted">
                显示第 <span>{{ chains.start_index }}</span> 到 <span>{{ chains.end_index }}</span> 条，共 <span>{{ chains.paginator.count }}</span> 条
              </p>
              <ul class="pagination m-0 ms-auto">
                {% if chains.has_previous %}
                <li class="page-item">
                  <a
                    class="page-link"
                    href="?page={{ chains.previous_page_number }}{% if request.GET.date %}&date={{ request.GET.date }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path d="M15 6l-6 6l6 6" />
                    </svg>
                  </a>
                </li>
                {% endif %} {% for num in chains.paginator.page_range %} {% if chains.number == num %}
                <li class="page-item active">
                  <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > chains.number|add:'-3' and num < chains.number|add:'3' %}
                <li class="page-item">
                  <a
                    class="page-link"
                    href="?page={{ num }}{% if request.GET.date %}&date={{ request.GET.date }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
                    >{{ num }}</a
                  >
                </li>
                {% endif %} {% endfor %} {% if chains.has_next %}
                <li class="page-item">
                  <a
                    class="page-link"
                    href="?page={{ chains.next_page_number }}{% if request.GET.date %}&date={{ request.GET.date }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path d="M9 6l6 6l-6 6" />
                    </svg>
                  </a>
                </li>
                {% endif %}
              </ul>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
