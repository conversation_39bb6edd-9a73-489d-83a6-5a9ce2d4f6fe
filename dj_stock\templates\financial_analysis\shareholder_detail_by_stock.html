{% extends 'base.html' %}

{% block title %}{{ stock.stock_name }} 股东持股明细 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">{{ stock.stock_name }} ({{ stock.stock_code }}) 股东持股明细</h2>
        <div class="text-muted mt-1">查看特定股票的股东持股明细数据</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <form method="get" class="d-flex">
              <select class="form-select" name="period">
                {% for period in available_periods %}
                <option value="{{ period|date:'Y-m-d' }}" {% if selected_period == period %}selected{% endif %}>{{ period|date:"Y-m-d" }}</option>
                {% endfor %}
              </select>
              <select class="form-select ms-2" name="type">
                <option value="">全部股东类型</option>
                {% for type in shareholder_types %}
                  {% if type %}
                  <option value="{{ type }}" {% if shareholder_type == type %}selected{% endif %}>{{ type }}</option>
                  {% endif %}
                {% endfor %}
              </select>
              <button type="submit" class="btn ms-2">查询</button>
            </form>
          </div>
          <div>
            <a href="{% url 'financial_analysis:shareholder_detail_list' %}" class="btn">
              <i class="ti ti-list me-2"></i>
              返回列表
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">股票代码</div>
            </div>
            <div class="h1 mb-3 mt-1">{{ stock.stock_code }}</div>
            <div class="d-flex mb-2">
              <div>所属行业</div>
              <div class="ms-auto">{{ stock.industry|default:"未知" }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">股票名称</div>
            </div>
            <div class="h1 mb-3 mt-1">{{ stock.stock_name }}</div>
            <div class="d-flex mb-2">
              <div>市场类型</div>
              <div class="ms-auto">{{ stock.market|default:"未知" }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">报告期</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">{{ selected_period|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">{{ shareholder_data|length }}</div>
            <div class="d-flex mb-2">
              <div>股东数量</div>
              <div class="ms-auto">{{ shareholder_data|length }}位</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">股东类型</div>
              <div class="ms-auto lh-1">
                <div class="badge bg-primary">{{ shareholder_type|default:"全部" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if shareholder_stats %}
              {{ shareholder_stats.count }}
              {% else %}
              -
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>统计次数</div>
              <div class="ms-auto">
                {% if shareholder_stats %}
                {{ shareholder_stats.count }}次
                {% else %}
                -
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 股东持股明细表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">股东持股明细数据 ({{ selected_period|date:"Y-m-d" }})</h3>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>序号</th>
              <th>股东名称</th>
              <th>股东类型</th>
              <th>持股数量(股)</th>
              <th>持股变动(股)</th>
              <th>变动比例</th>
              <th>变动趋势</th>
              <th>持股市值(元)</th>
              <th>公告日期</th>
            </tr>
          </thead>
          <tbody>
            {% for shareholder in shareholder_data %}
            <tr>
              <td>{{ forloop.counter }}</td>
              <td>{{ shareholder.shareholder_name }}</td>
              <td>{{ shareholder.shareholder_type|default:"-" }}</td>
              <td>{{ shareholder.holding_amount|floatformat:0 }}</td>
              <td>
                {% if shareholder.holding_change > 0 %}
                <span class="text-up">+{{ shareholder.holding_change|floatformat:0 }}</span>
                {% elif shareholder.holding_change < 0 %}
                <span class="text-down">{{ shareholder.holding_change|floatformat:0 }}</span>
                {% else %}
                0
                {% endif %}
              </td>
              <td>
                {% if shareholder.holding_change_ratio > 0 %}
                <span class="text-up">+{{ shareholder.holding_change_ratio|floatformat:2 }}%</span>
                {% elif shareholder.holding_change_ratio < 0 %}
                <span class="text-down">{{ shareholder.holding_change_ratio|floatformat:2 }}%</span>
                {% else %}
                0.00%
                {% endif %}
              </td>
              <td>
                {% if shareholder.holding_trend == '增持' %}
                <span class="badge bg-success">{{ shareholder.holding_trend }}</span>
                {% elif shareholder.holding_trend == '减持' %}
                <span class="badge bg-danger">{{ shareholder.holding_trend }}</span>
                {% elif shareholder.holding_trend == '不变' %}
                <span class="badge bg-secondary">{{ shareholder.holding_trend }}</span>
                {% elif shareholder.holding_trend == '新进' %}
                <span class="badge bg-primary">{{ shareholder.holding_trend }}</span>
                {% elif shareholder.holding_trend == '退出' %}
                <span class="badge bg-warning">{{ shareholder.holding_trend }}</span>
                {% else %}
                <span class="badge">{{ shareholder.holding_trend|default:"-" }}</span>
                {% endif %}
              </td>
              <td>{{ shareholder.market_value|floatformat:2 }}</td>
              <td>{{ shareholder.announce_date|date:"Y-m-d"|default:"-" }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="9" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前报告期没有股东持股明细数据，请尝试选择其他报告期。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- 股东统计数据 -->
    {% if shareholder_stats %}
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">股东统计数据</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">统计截止日期</label>
              <div class="form-control-plaintext">{{ shareholder_stats.report_date|date:"Y-m-d" }}</div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">统计次数</label>
              <div class="form-control-plaintext">{{ shareholder_stats.count }}</div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">股东类型</label>
              <div class="form-control-plaintext">{{ shareholder_stats.shareholder_type }}</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">10日平均涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.avg_return_10d > 0 %}
                <span class="text-up">+{{ shareholder_stats.avg_return_10d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.avg_return_10d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">10日最大涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.max_return_10d > 0 %}
                <span class="text-up">+{{ shareholder_stats.max_return_10d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.max_return_10d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">10日最小涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.min_return_10d > 0 %}
                <span class="text-up">+{{ shareholder_stats.min_return_10d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.min_return_10d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">30日平均涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.avg_return_30d > 0 %}
                <span class="text-up">+{{ shareholder_stats.avg_return_30d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.avg_return_30d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">30日最大涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.max_return_30d > 0 %}
                <span class="text-up">+{{ shareholder_stats.max_return_30d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.max_return_30d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">30日最小涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.min_return_30d > 0 %}
                <span class="text-up">+{{ shareholder_stats.min_return_30d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.min_return_30d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">60日平均涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.avg_return_60d > 0 %}
                <span class="text-up">+{{ shareholder_stats.avg_return_60d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.avg_return_60d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">60日最大涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.max_return_60d > 0 %}
                <span class="text-up">+{{ shareholder_stats.max_return_60d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.max_return_60d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-label">60日最小涨幅</label>
              <div class="form-control-plaintext">
                {% if shareholder_stats.min_return_60d > 0 %}
                <span class="text-up">+{{ shareholder_stats.min_return_60d|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ shareholder_stats.min_return_60d|floatformat:2 }}%</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>
{% endblock %}
