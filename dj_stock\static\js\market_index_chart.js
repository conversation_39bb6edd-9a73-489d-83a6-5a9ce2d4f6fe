// 市场指数趋势图表处理函数
function initMarketIndexChart(initialData) {
  // 使用 let 而不是 const，因为这些变量会被修改
  let marketIndicesTrend = initialData
  let marketIndexChart = echarts.init(document.getElementById('market-index-chart'))

  // 更新市场指数图表
  function updateMarketIndexChart(indexCode = '000001') {
    console.log('Updating chart for index code:', indexCode)
    console.log('Available indices:', Object.keys(marketIndicesTrend))

    const indexData = marketIndicesTrend[indexCode]
    if (!indexData) {
      console.error(`No data found for index code: ${indexCode}`)

      // 显示错误信息
      const chartContainer = document.getElementById('market-index-chart')
      if (chartContainer) {
        chartContainer.innerHTML = `
          <div class="alert alert-warning" role="alert">
            <h4 class="alert-heading">没有找到数据</h4>
            <p>没有找到指数 ${indexCode} 的数据。请尝试选择不同的指数或日期范围。</p>
          </div>
        `
      }
      return
    }

    console.log('Index data:', indexData)

    // 检查数据点数量
    if (!indexData.data || indexData.data.length === 0) {
      console.warn(`Empty data array for index code: ${indexCode}`)

      // 显示空数据提示
      const chartContainer = document.getElementById('market-index-chart')
      if (chartContainer) {
        chartContainer.innerHTML = `
          <div class="alert alert-info" role="alert">
            <h4 class="alert-heading">没有数据</h4>
            <p>在选定的日期范围内没有找到 ${indexData.name} 的数据。请尝试选择不同的日期范围。</p>
          </div>
        `
      }
      return
    }

    const marketIndexOption = {
      backgroundColor: 'transparent',
      title: {
        text: indexData.name,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        formatter: function (params) {
          const date = params[0].name
          const value = params[0].value.toFixed(2)
          const valueColor = params[0].value > params[0].data.prevClose ? '#f56c6c' : '#67c23a'
          const changePercent = (((params[0].value - params[0].data.prevClose) / params[0].data.prevClose) * 100).toFixed(2)
          const changeColor = changePercent >= 0 ? '#f56c6c' : '#67c23a'
          const changeSymbol = changePercent >= 0 ? '+' : ''

          return (
            `<div style="font-weight:bold;margin-bottom:5px;">${date}</div>` +
            `<div style="margin-bottom:3px;">${params[0].seriesName}: <span style="float:right;font-weight:bold;color:${valueColor}">${value}</span></div>` +
            `<div>涨跌幅: <span style="float:right;font-weight:bold;color:${changeColor}">${changeSymbol}${changePercent}%</span></div>`
          )
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#eee',
        borderWidth: 1,
        padding: 10,
        textStyle: { color: '#333' },
        extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: indexData.data.map((item) => item.date),
        axisLine: {
          lineStyle: {
            color: '#eaecef',
          },
        },
        axisLabel: {
          color: '#718096',
          fontSize: 12,
          formatter: function (value) {
            return value.substring(5) // 只显示月-日
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        scale: true, // 使用自适应缩放，不从0开始
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#718096',
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            color: '#eaecef',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: indexData.name,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          sampling: 'average',
          itemStyle: {
            color: function (params) {
              // 根据上一个交易日的收盘价来决定颜色
              const value = params.value
              const prevClose = params.data.prevClose
              return value >= prevClose ? '#f56c6c' : '#67c23a'
            },
            borderWidth: 2,
            borderColor: '#fff',
          },
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: 10,
            shadowOffsetY: 5,
            cap: 'round',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(32, 107, 196, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(32, 107, 196, 0.05)',
              },
            ]),
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10,
          },
          emphasis: {
            itemStyle: {
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
          data: indexData.data.map((item) => ({
            value: item.value,
            prevClose: item.prevClose || item.value, // 如果没有prevClose，则使用当前值
          })),
        },
      ],
    }

    marketIndexChart.setOption(marketIndexOption, true)
  }

  // 获取市场指数数据的函数
  function fetchMarketIndexData(startDate = null, endDate = null, days = null) {
    // 构建查询参数
    let params = new URLSearchParams()
    const currentIndex = document.getElementById('market-index-filter').value

    // 添加当前选中的指数代码
    params.append('index_code', currentIndex)
    params.append('data_type', 'market_indices')
    params.append('format', 'json') // 添加format参数，确保返回JSON数据

    if (startDate && endDate) {
      params.append('start_date', startDate)
      params.append('end_date', endDate)
    } else if (days) {
      params.append('days', days)
    }

    // 显示加载中状态
    const chartContainer = document.getElementById('market-index-chart')
    chartContainer.innerHTML =
      '<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></div>'

    // 发送AJAX请求
    const url = `/market_data/api/market-indices/?${params.toString()}`
    console.log('Sending AJAX request to:', url)

    fetch(url, {
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
      },
    })
      .then((response) => {
        console.log('Response status:', response.status)
        console.log('Response headers:', response.headers)

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`)
        }

        // 尝试获取响应文本，然后手动解析JSON
        return response.text().then((text) => {
          console.log('Response text:', text.substring(0, 200) + '...')
          try {
            return JSON.parse(text)
          } catch (e) {
            console.error('Error parsing JSON:', e)
            throw new Error('Invalid JSON response')
          }
        })
      })
      .then((data) => {
        // 添加调试日志
        console.log('Received data from server:', data)
        console.log('Data type:', typeof data)

        // 直接使用返回的JSON数据
        if (data) {
          try {
            console.log('Data structure received:', JSON.stringify(data, null, 2).substring(0, 200) + '...')
            console.log('Full data keys:', Object.keys(data))
          } catch (e) {
            console.error('Error stringifying data:', e)
          }

          // 检查数据结构
          if (typeof data === 'object' && Object.keys(data).length > 0) {
            // 安全地更新 marketIndicesTrend
            marketIndicesTrend = JSON.parse(JSON.stringify(data))
            console.log('Updated marketIndicesTrend with keys:', Object.keys(marketIndicesTrend))
            console.log('Market indices trend data sample:', marketIndicesTrend[Object.keys(marketIndicesTrend)[0]])

            // 重新初始化图表
            try {
              marketIndexChart.dispose()
            } catch (e) {
              console.warn('Error disposing chart:', e)
            }

            const chartContainer = document.getElementById('market-index-chart')
            if (!chartContainer) {
              console.error('Chart container not found!')
              return
            }

            try {
              const newChart = echarts.init(chartContainer)
              marketIndexChart = newChart
              console.log('Successfully created new chart instance')
            } catch (e) {
              console.error('Error creating new chart instance:', e)
              return
            }

            // 检查指数代码是否存在于数据中
            if (marketIndicesTrend[currentIndex]) {
              updateMarketIndexChart(currentIndex)
            } else {
              // 如果当前指数不存在于数据中，使用第一个可用的指数
              const availableIndices = Object.keys(marketIndicesTrend)
              if (availableIndices.length > 0) {
                console.log('Current index not found in data, using first available index:', availableIndices[0])
                updateMarketIndexChart(availableIndices[0])
              } else {
                console.error('No indices available in the data')
              }
            }
          } else {
            console.error('Invalid data structure received:', data)
          }
        } else {
          console.error('Received empty data from server')
        }
      })
      .catch((error) => {
        console.error('Error fetching market index data:', error)
        console.error('Error stack:', error.stack)

        // 显示错误信息
        const chartContainer = document.getElementById('market-index-chart')
        if (chartContainer) {
          chartContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
              <h4 class="alert-heading">加载数据失败</h4>
              <p>无法加载市场指数数据。请刷新页面或尝试选择不同的日期范围。</p>
              <hr>
              <p class="mb-0">错误信息: ${error.message}</p>
              <p class="mb-0">错误类型: ${error.name}</p>
            </div>
          `
        }

        // 尝试恢复图表
        try {
          updateMarketIndexChart('000001')
        } catch (e) {
          console.error('Failed to recover chart:', e)
        }
      })
  }

  // 初始化市场指数趋势图
  updateMarketIndexChart('000001')

  // 添加市场指数筛选器事件监听
  document.getElementById('market-index-filter').addEventListener('change', function (e) {
    const dateRangeFilter = document.getElementById('date-range-filter').value

    if (dateRangeFilter === 'custom') {
      // 如果是自定义日期范围，使用当前选择的日期
      const startDate = document.getElementById('start-date').value
      const endDate = document.getElementById('end-date').value

      if (startDate && endDate) {
        // 如果日期已选择，使用这些日期获取新数据
        fetchMarketIndexData(startDate, endDate)
      } else {
        // 如果日期未选择，使用默认的数据
        updateMarketIndexChart(e.target.value)
      }
    } else {
      // 如果使用预设日期范围，获取新数据
      const days = parseInt(dateRangeFilter)
      fetchMarketIndexData(null, null, days)
    }
  })

  // 日期区间选择器事件监听
  document.getElementById('date-range-filter').addEventListener('change', function (e) {
    const value = e.target.value
    const customDateRange = document.getElementById('custom-date-range')
    const today = new Date()
    const endDate = new Date(today)

    if (value === 'custom') {
      // 显示自定义日期范围选择器
      customDateRange.style.display = 'flex'
      // 设置默认日期范围（当前日期往前30天）
      const startDate = new Date(today)
      startDate.setDate(today.getDate() - 30)

      document.getElementById('end-date').valueAsDate = endDate
      document.getElementById('start-date').valueAsDate = startDate
    } else {
      // 隐藏自定义日期范围选择器，但仍然填充日期以便于后续使用
      customDateRange.style.display = 'none'

      // 解析天数并设置日期范围
      const days = parseInt(value)
      const startDate = new Date(today)
      startDate.setDate(today.getDate() - days)

      // 更新日期选择器的值（即使它不可见）
      document.getElementById('end-date').valueAsDate = endDate
      document.getElementById('start-date').valueAsDate = startDate

      // 更新显示的日期范围
      document.getElementById('date-range-display').textContent = `(近${days}天)`

      // 请求新数据并更新图表
      fetchMarketIndexData(null, null, days)
    }
  })

  // 自定义日期范围应用按钮
  document.getElementById('apply-date-range').addEventListener('click', function () {
    const startDate = document.getElementById('start-date').value
    const endDate = document.getElementById('end-date').value

    if (!startDate || !endDate) {
      alert('请选择完整的日期范围')
      return
    }

    // 验证日期范围
    const startDateObj = new Date(startDate)
    const endDateObj = new Date(endDate)

    if (startDateObj > endDateObj) {
      alert('开始日期不能晚于结束日期')
      return
    }

    // 格式化显示的日期
    const formattedStart = new Date(startDate).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    const formattedEnd = new Date(endDate).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    document.getElementById('date-range-display').textContent = `(${formattedStart} - ${formattedEnd})`

    // 请求新数据并更新图表
    fetchMarketIndexData(startDate, endDate)
  })

  // 返回公共函数
  return {
    updateChart: updateMarketIndexChart,
    fetchData: fetchMarketIndexData,
    resize: function () {
      marketIndexChart.resize()
    },
  }
}
