# Generated by Django 5.1.7 on 2025-04-23 13:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('financial_analysis', '0004_auto_20250412_2246'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='stockfinancialindicator',
            options={'managed': False, 'verbose_name': '股票财务指标', 'verbose_name_plural': '股票财务指标'},
        ),
        migrations.AlterModelOptions(
            name='stockshareholderstatistics',
            options={'managed': False, 'ordering': ['-report_date', 'shareholder_name'], 'verbose_name': '十大股东持股统计数据', 'verbose_name_plural': '十大股东持股统计数据'},
        ),
        migrations.CreateModel(
            name='StockFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '股票收藏',
                'verbose_name_plural': '股票收藏',
                'db_table': 'stock_favorite',
                'unique_together': {('user', 'stock_code')},
            },
        ),
    ]
