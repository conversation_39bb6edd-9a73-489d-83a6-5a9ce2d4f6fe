from datetime import datetime, date
from sqlalchemy import (
    Column,
    Integer,
    String,
    Float,
    Date,
    DateTime,
    Text,
    Boolean,
    Numeric,
    UniqueConstraint,
    Index,
    BigInteger,
    DECIMAL,
    Time,
    text,
)
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class StockMarketIndex(Base):
    """主板指数数据模型"""

    __tablename__ = "stock_market_index"
    __table_args__ = (
        UniqueConstraint("index_code", "trade_date", name="uk_index_code_trade_date"),
        Index("idx_market_index_date", "trade_date"),
        {"comment": "主板指数数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    index_code = Column(String(10), nullable=False, comment="指数代码")
    index_name = Column(String(50), nullable=False, comment="指数名称")
    trade_date = Column(Date, nullable=False, comment="交易日期")
    open_price = Column(Float, comment="开盘价")
    high_price = Column(Float, comment="最高价")
    low_price = Column(Float, comment="最低价")
    close_price = Column(Float, comment="收盘价")
    pre_close = Column(Float, comment="昨收价")
    change_amount = Column(Float, comment="涨跌额")
    amplitude = Column(Float, comment="振幅")
    volume = Column(Float, comment="成交量")
    amount = Column(Float, comment="成交额")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockBasic(Base):
    """股票基本信息表"""

    __tablename__ = "stock_basic"
    __table_args__ = {"comment": "股票基本信息表"}

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(
        String(10), nullable=False, unique=True, index=True, comment="股票代码"
    )
    stock_name = Column(String(100), nullable=False, comment="股票名称")
    industry = Column(String(100), nullable=True, comment="所属行业")
    market = Column(
        String(50), nullable=True, comment="交易市场(上海/深圳/北京/创业板/科创板)"
    )
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockRiskWarning(Base):
    """风险警示股票表（ST股票）"""

    __tablename__ = "stock_risk_warning"
    __table_args__ = (
        UniqueConstraint("stock_code", "warning_date", name="uk_stock_warning_date"),
        {"comment": "风险警示股票（ST股票）数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    stock_name = Column(String(100), nullable=False, comment="股票名称")
    warning_reason = Column(Text, nullable=False, comment="风险警示原因")
    warning_date = Column(Date, nullable=False, comment="风险警示日期")
    status = Column(
        String(20), nullable=False, default="纳入", comment="状态(纳入/剔除)"
    )
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockIPO(Base):
    """新股上市数据"""

    __tablename__ = "stock_ipo"
    __table_args__ = {"comment": "新股上市数据"}

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    listing_date = Column(Date, nullable=False, comment="上市日期")
    issue_price = Column(Float, nullable=False, comment="发行价")
    latest_price = Column(Float, comment="最新价")
    first_day_open = Column(Float, comment="首日开盘价")
    first_day_close = Column(Float, comment="首日收盘价")
    first_day_high = Column(Float, comment="首日最高价")
    first_day_low = Column(Float, comment="首日最低价")
    first_day_change_pct = Column(Float, comment="首日涨跌幅")
    break_issue = Column(String(10), comment="是否破发")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )

    __table_args__ = (
        UniqueConstraint("stock_code", name="uk_stock_code"),
        Index("idx_listing_date", "listing_date"),
    )


class StockDividend(Base):
    """股票分红送配数据"""

    __tablename__ = "stock_dividend"
    __table_args__ = {"comment": "股票分红送配数据"}

    id = Column(Integer, primary_key=True, autoincrement=True)
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    report_date = Column(Date, nullable=False, comment="报告期")
    announce_date = Column(Date, nullable=False, comment="公告日期")
    dividend_year = Column(Integer, nullable=False, comment="分红年度")

    # 分红方案具体数值
    cash_dividend = Column(Float, nullable=True, comment="每10股现金分红(元)")
    share_dividend = Column(Float, nullable=True, comment="每10股送股(股)")
    share_transfer = Column(Float, nullable=True, comment="每10股转增(股)")
    bonus_share = Column(Float, nullable=True, comment="每10股送转合计(股)")

    # 分红实施信息
    dividend_plan = Column(String(200), nullable=True, comment="原始分红方案")
    registration_date = Column(Date, nullable=True, comment="股权登记日")
    ex_dividend_date = Column(Date, nullable=True, comment="除权除息日")
    payment_date = Column(Date, nullable=True, comment="红利发放日")

    # 分红实施状态
    implementation_status = Column(String(20), nullable=True, comment="方案进度")

    # 时间戳
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )

    __table_args__ = (
        UniqueConstraint(
            "stock_code", "report_date", "announce_date", name="uk_stock_dividend"
        ),
        Index("idx_stock_dividend_code", "stock_code"),
        Index("idx_stock_dividend_date", "report_date"),
    )


class StockShareholderStatistics(Base):
    """十大股东持股统计数据"""

    __tablename__ = "stock_shareholder_statistics"
    __table_args__ = (
        UniqueConstraint(
            "shareholder_name", "report_date", name="uk_shareholder_report_date"
        ),
        {"comment": "十大股东持股统计数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    shareholder_name = Column(
        String(255), nullable=False, index=True, comment="股东名称"
    )
    shareholder_type = Column(String(50), nullable=False, comment="股东类型")
    report_date = Column(Date, nullable=False, comment="统计截止日期")
    count = Column(Integer, nullable=False, comment="统计次数")
    avg_return_10d = Column(Float, nullable=True, comment="10日平均涨幅")
    max_return_10d = Column(Float, nullable=True, comment="10日最大涨幅")
    min_return_10d = Column(Float, nullable=True, comment="10日最小涨幅")
    avg_return_30d = Column(Float, nullable=True, comment="30日平均涨幅")
    max_return_30d = Column(Float, nullable=True, comment="30日最大涨幅")
    min_return_30d = Column(Float, nullable=True, comment="30日最小涨幅")
    avg_return_60d = Column(Float, nullable=True, comment="60日平均涨幅")
    max_return_60d = Column(Float, nullable=True, comment="60日最大涨幅")
    min_return_60d = Column(Float, nullable=True, comment="60日最小涨幅")
    holding_stocks = Column(Text, nullable=True, comment="持有个股列表")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )


class TechnicalIndicator(Base):
    """技术指标数据"""

    __tablename__ = "technical_indicator"
    __table_args__ = (
        UniqueConstraint(
            "stock_code",
            "date",
            "indicator_type",
            name="uk_technical_indicator",
        ),
        {"comment": "技术指标数据表(创新高、连续上涨等)"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(100), nullable=False, comment="股票名称")
    date = Column(Date, nullable=False, comment="统计日期")
    indicator_type = Column(
        String(50), nullable=False, comment="指标类型(创新高/连续上涨)"
    )
    indicator_value = Column(
        Float, nullable=True, comment="指标值(创新高为价格/连续上涨为天数)"
    )
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )


class StockFundFlow(Base):
    """资金流向数据"""

    __tablename__ = "stock_fund_flow"
    __table_args__ = (
        UniqueConstraint("date", "code", "type", name="uk_fund_flow_date_code_type"),
        Index("ix_fund_flow_date", "date"),
        Index("ix_fund_flow_code", "code"),
        {"comment": "资金流向数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    code = Column(String(10), nullable=False, comment="代码(股票代码或NORTH北向资金)")
    name = Column(String(100), nullable=False, comment="名称")
    date = Column(Date, nullable=False, comment="交易日期")
    type = Column(String(50), nullable=False, comment="数据类型(个股/北向资金)")
    main_net_inflow = Column(Float, nullable=True, comment="主力净流入(元)")
    retail_net_inflow = Column(Float, nullable=True, comment="散户净流入(元)")
    total_net_inflow = Column(Float, nullable=True, comment="总净流入(元)")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockDailyQuote(Base):
    """股票每日行情"""

    __tablename__ = "stock_daily_quote"
    __table_args__ = {"comment": "股票每日行情"}

    id = Column(Integer, primary_key=True, autoincrement=True)
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    trade_date = Column(Date, nullable=False, comment="交易日期")

    # 价格相关
    open_price = Column(Float, comment="开盘价")
    close_price = Column(Float, comment="收盘价")
    high_price = Column(Float, comment="最高价")
    low_price = Column(Float, comment="最低价")
    prev_close = Column(Float, comment="昨日收盘价")

    # 交易量和金额
    volume = Column(Float, comment="成交量")
    amount = Column(Float, comment="成交额")
    turnover_rate = Column(Float, comment="换手率")
    volume_ratio = Column(Float, comment="量比")

    # 涨跌相关
    change_amount = Column(Float, comment="涨跌额")
    change_percent = Column(Float, comment="涨跌幅")
    amplitude = Column(Float, comment="振幅")
    price_speed = Column(Float, comment="涨速")
    change_60d = Column(Float, comment="60日涨跌幅")
    change_ytd = Column(Float, comment="年初至今涨跌幅")

    # 估值指标
    pe_ratio = Column(Float, comment="市盈率(动态)")
    pb_ratio = Column(Float, comment="市净率")
    total_value = Column(Float, comment="总市值")
    float_value = Column(Float, comment="流通市值")

    # 时间戳
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )

    __table_args__ = (
        UniqueConstraint("stock_code", "trade_date", name="uk_stock_daily_quote"),
        Index("idx_stock_daily_quote_date", "trade_date"),
    )

    def __repr__(self):
        return f"<StockDailyQuote(stock_code='{self.stock_code}', trade_date='{self.trade_date}')>"


# 涨跌停数据模型
class StockLimitList(Base):
    """涨跌停数据"""

    __tablename__ = "stock_limit_list"
    __table_args__ = {"comment": "涨跌停数据"}

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    change_ratio = Column(Float, comment="涨跌幅")
    latest_price = Column(Float, comment="最新价")
    amount = Column(BigInteger, comment="成交额")
    circulation_market_value = Column(Float, comment="流通市值")
    total_market_value = Column(Float, comment="总市值")
    turnover_ratio = Column(Float, comment="换手率")
    fund_amount = Column(BigInteger, comment="封单资金")
    first_time = Column(Time, nullable=True, comment="首次封板时间")
    last_time = Column(Time, nullable=False, comment="最后封板时间")
    break_count = Column(Integer, default=0, comment="炸板次数/开板次数")
    continuous_limit = Column(Integer, default=1, comment="连板数/连续跌停")
    industry = Column(String(50), comment="所属行业")
    limit_type = Column(String(10), nullable=False, comment="涨跌停类型")
    date = Column(Date, nullable=False, comment="交易日期")
    create_time = Column(DateTime, nullable=False, comment="创建时间")

    __table_args__ = (Index("idx_stock_limit_date", "stock_code", "date", unique=True),)


# 龙虎榜数据模型
class StockTopList(Base):
    """龙虎榜数据"""

    __tablename__ = "stock_top_list"
    __table_args__ = (
        UniqueConstraint("stock_code", "date", name="uk_stock_top_list_date"),
        {"comment": "龙虎榜数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    stock_name = Column(String(100), nullable=False, comment="股票名称")
    date = Column(Date, nullable=False, comment="上榜日期")
    reason = Column(Text, nullable=True, comment="上榜解读")
    detail_reason = Column(Text, nullable=True, comment="上榜原因")
    close_price = Column(Float, nullable=True, comment="收盘价")
    change_ratio = Column(Float, nullable=True, comment="涨跌幅")
    net_buy = Column(Float, nullable=True, comment="龙虎榜净买额")
    buy_amount = Column(Float, nullable=True, comment="龙虎榜买入额")
    sell_amount = Column(Float, nullable=True, comment="龙虎榜卖出额")
    total_turnover = Column(Float, nullable=True, comment="龙虎榜成交额")
    market_total_turnover = Column(Float, nullable=True, comment="市场总成交额")
    net_buy_ratio = Column(Float, nullable=True, comment="净买额占总成交比")
    turnover_ratio = Column(Float, nullable=True, comment="成交额占总成交比")
    turnover_rate = Column(Float, nullable=True, comment="换手率")
    circulation_market_value = Column(Float, nullable=True, comment="流通市值")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )


class StockMargin(Base):
    """融资融券明细数据"""

    __tablename__ = "stock_margin"

    id = Column(Integer, primary_key=True, autoincrement=True)
    trade_date = Column(Date, nullable=False, comment="信用交易日期")
    stock_code = Column(String(10), nullable=False, comment="标的证券代码")
    stock_name = Column(String(50), nullable=False, comment="标的证券简称")
    margin_balance = Column(BigInteger, nullable=True, comment="融资余额")
    margin_buy = Column(BigInteger, nullable=True, comment="融资买入额")
    margin_repay = Column(BigInteger, nullable=True, comment="融资偿还额")
    short_balance = Column(BigInteger, nullable=True, comment="融券余量")
    short_sell = Column(BigInteger, nullable=True, comment="融券卖出量")
    short_repay = Column(BigInteger, nullable=True, comment="融券偿还量")
    short_balance_amount = Column(BigInteger, nullable=True, comment="融券余额")
    total_balance = Column(BigInteger, nullable=True, comment="融资融券余额")
    exchange = Column(String(10), nullable=False, comment="交易所(SSE/SZSE)")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )

    __table_args__ = (
        UniqueConstraint(
            "trade_date", "stock_code", "exchange", name="uix_margin_date_code_exchange"
        ),
        {"comment": "融资融券明细数据"},
    )


class HKStockConnect(Base):
    """港股通成份股数据"""

    __tablename__ = "hk_stock_connect"
    __table_args__ = (
        UniqueConstraint("stock_code", "date", name="uk_hk_connect_date"),
        {"comment": "港股通成份股数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    stock_name = Column(String(100), nullable=False, comment="股票名称")
    date = Column(Date, nullable=False, comment="统计日期")
    direction = Column(String(20), nullable=False, comment="交易方向(沪股通/深股通)")
    status = Column(String(20), nullable=False, comment="状态(纳入/剔除)")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )


class StockComment(Base):
    """千股千评数据"""

    __tablename__ = "stock_comment"
    __table_args__ = (
        UniqueConstraint("stock_code", "date", name="uk_stock_comment_date"),
        {"comment": "千股千评数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    date = Column(Date, nullable=False, comment="交易日期")
    current_price = Column(Float, comment="最新价")
    change_ratio = Column(Float, comment="涨跌幅")
    turnover_rate = Column(Float, comment="换手率")
    pe_ratio = Column(Float, comment="市盈率")
    main_cost = Column(Float, comment="主力成本")
    institution_participation = Column(Float, comment="机构参与度")
    comprehensive_score = Column(Float, comment="综合得分")
    rise_rank = Column(Integer, comment="上升排名")
    current_rank = Column(Integer, comment="目前排名")
    attention_index = Column(Float, comment="关注指数")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class StockIndustryChain(Base):
    """产业链关系表"""

    __tablename__ = "stock_industry_chain"
    __table_args__ = (
        UniqueConstraint("stock_code", "related_stock_code", name="uk_stock_relation"),
        {"comment": "产业链关系表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    stock_name = Column(String(100), nullable=False, comment="股票名称")
    related_stock_code = Column(
        String(10), nullable=False, index=True, comment="关联股票代码"
    )
    related_stock_name = Column(String(100), nullable=False, comment="关联股票名称")
    relation_type = Column(
        String(50), nullable=False, comment="关系类型(上游/下游/同业)"
    )
    relation_weight = Column(Float, nullable=True, comment="关联度权重")
    industry_name = Column(String(100), nullable=False, comment="所属产业链")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockBoardConcept(Base):
    """概念板块数据"""

    __tablename__ = "stock_board_concept"
    __table_args__ = (
        UniqueConstraint("board_code", "date", name="uk_board_concept_date"),
        {"comment": "概念板块数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    board_code = Column(String(10), nullable=False, comment="板块代码")
    board_name = Column(String(50), nullable=False, comment="板块名称")
    date = Column(Date, nullable=False, comment="交易日期")
    rank = Column(Integer, comment="排名")
    latest_price = Column(Float, comment="最新价")
    change_amount = Column(Float, comment="涨跌额")
    change_percent = Column(Float, comment="涨跌幅")
    total_market_value = Column(Float, comment="总市值")
    turnover_rate = Column(Float, comment="换手率")
    up_count = Column(Integer, comment="上涨家数")
    down_count = Column(Integer, comment="下跌家数")
    leading_stock = Column(String(50), comment="领涨股票")
    leading_stock_change_percent = Column(Float, comment="领涨股票涨跌幅")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockBoardConceptStock(Base):
    """概念板块成份股数据"""

    __tablename__ = "stock_board_concept_stock"
    __table_args__ = (
        UniqueConstraint(
            "board_code", "stock_code", "date", name="uk_board_stock_date"
        ),
        {"comment": "概念板块成份股数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    board_code = Column(String(10), nullable=False, comment="板块代码")
    board_name = Column(String(50), nullable=False, comment="板块名称")
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    date = Column(Date, nullable=False, comment="交易日期")
    rank = Column(Integer, comment="序号")
    latest_price = Column(Float, comment="最新价")
    change_percent = Column(Float, comment="涨跌幅")
    change_amount = Column(Float, comment="涨跌额")
    volume = Column(Float, comment="成交量")
    amount = Column(Float, comment="成交额")
    amplitude = Column(Float, comment="振幅")
    high = Column(Float, comment="最高")
    low = Column(Float, comment="最低")
    open = Column(Float, comment="今开")
    pre_close = Column(Float, comment="昨收")
    turnover_rate = Column(Float, comment="换手率")
    pe_ratio = Column(Float, comment="市盈率-动态")
    pb_ratio = Column(Float, comment="市净率")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockBoardIndustry(Base):
    """行业板块数据"""

    __tablename__ = "stock_board_industry"
    __table_args__ = (
        UniqueConstraint("board_code", "date", name="uk_board_industry_date"),
        {"comment": "行业板块数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    board_code = Column(String(10), nullable=False, comment="板块代码")
    board_name = Column(String(50), nullable=False, comment="板块名称")
    date = Column(Date, nullable=False, comment="交易日期")
    rank = Column(Integer, comment="排名")
    latest_price = Column(Float, comment="最新价")
    change_amount = Column(Float, comment="涨跌额")
    change_percent = Column(Float, comment="涨跌幅")
    total_market_value = Column(Float, comment="总市值")
    turnover_rate = Column(Float, comment="换手率")
    up_count = Column(Integer, comment="上涨家数")
    down_count = Column(Integer, comment="下跌家数")
    leading_stock = Column(String(50), comment="领涨股票")
    leading_stock_change_percent = Column(Float, comment="领涨股票涨跌幅")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockBoardIndustryStock(Base):
    """行业板块成份股数据"""

    __tablename__ = "stock_board_industry_stock"
    __table_args__ = (
        UniqueConstraint(
            "board_code", "stock_code", "date", name="uk_board_stock_date"
        ),
        {"comment": "行业板块成份股数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    board_code = Column(String(10), nullable=False, comment="板块代码")
    board_name = Column(String(50), nullable=False, comment="板块名称")
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    date = Column(Date, nullable=False, comment="交易日期")
    rank = Column(Integer, comment="序号")
    latest_price = Column(Float, comment="最新价")
    change_percent = Column(Float, comment="涨跌幅")
    change_amount = Column(Float, comment="涨跌额")
    volume = Column(Float, comment="成交量")
    amount = Column(Float, comment="成交额")
    amplitude = Column(Float, comment="振幅")
    high = Column(Float, comment="最高")
    low = Column(Float, comment="最低")
    open = Column(Float, comment="今开")
    pre_close = Column(Float, comment="昨收")
    turnover_rate = Column(Float, comment="换手率")
    pe_ratio = Column(Float, comment="市盈率-动态")
    pb_ratio = Column(Float, comment="市净率")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockActiveBroker(Base):
    """每日活跃营业部数据"""

    __tablename__ = "stock_active_broker"

    id = Column(Integer, primary_key=True, autoincrement=True)
    broker_name = Column(String(100), nullable=False, comment="营业部名称")
    trade_date = Column(Date, nullable=False, comment="上榜日期")
    buy_stock_count = Column(Integer, comment="买入个股数")
    sell_stock_count = Column(Integer, comment="卖出个股数")
    buy_amount = Column(Numeric(20, 2), comment="买入总金额")
    sell_amount = Column(Numeric(20, 2), comment="卖出总金额")
    net_amount = Column(Numeric(20, 2), comment="总买卖净额")
    buy_stocks = Column(Text, comment="买入股票")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )

    __table_args__ = (
        UniqueConstraint("broker_name", "trade_date", name="uix_broker_date"),
    )


class StockMarketFundFlow(Base):
    """市场资金周报数据"""

    __tablename__ = "stock_market_fund_flow"
    __table_args__ = (
        UniqueConstraint("trade_date", name="uk_market_fund_flow_date"),
        Index("idx_market_fund_flow_date", "trade_date"),
        {"comment": "市场资金周报数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    trade_date = Column(Date, nullable=False, comment="交易日期")
    sh_index_close = Column(Float, nullable=True, comment="上证-收盘价")
    sh_index_change_pct = Column(Float, nullable=True, comment="上证-涨跌幅")
    sz_index_close = Column(Float, nullable=True, comment="深证-收盘价")
    sz_index_change_pct = Column(Float, nullable=True, comment="深证-涨跌幅")
    main_net_inflow = Column(Float, nullable=True, comment="主力净流入-净额")
    main_net_inflow_pct = Column(Float, nullable=True, comment="主力净流入-净占比")
    super_big_net_inflow = Column(Float, nullable=True, comment="超大单净流入-净额")
    super_big_net_inflow_pct = Column(
        Float, nullable=True, comment="超大单净流入-净占比"
    )
    big_net_inflow = Column(Float, nullable=True, comment="大单净流入-净额")
    big_net_inflow_pct = Column(Float, nullable=True, comment="大单净流入-净占比")
    medium_net_inflow = Column(Float, nullable=True, comment="中单净流入-净额")
    medium_net_inflow_pct = Column(Float, nullable=True, comment="中单净流入-净占比")
    small_net_inflow = Column(Float, nullable=True, comment="小单净流入-净额")
    small_net_inflow_pct = Column(Float, nullable=True, comment="小单净流入-净占比")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockSectorFundFlow(Base):
    """行业资金轴幅数据"""

    __tablename__ = "stock_sector_fund_flow"
    __table_args__ = (
        UniqueConstraint(
            "trade_date", "sector_name", name="uk_sector_fund_flow_date_name"
        ),
        Index("idx_sector_fund_flow_date", "trade_date"),
        Index("idx_sector_fund_flow_name", "sector_name"),
        {"comment": "行业资金轴幅数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    trade_date = Column(Date, nullable=False, comment="交易日期")
    sector_name = Column(String(50), nullable=False, comment="板块名称")
    sector_type = Column(
        String(20), nullable=False, comment="板块类型，行业板块或概念板块"
    )
    rank = Column(Integer, nullable=True, comment="排名")
    net_inflow_rate = Column(Float, nullable=True, comment="今日涨跌幅(%)")
    net_inflow_amount = Column(Float, nullable=True, comment="主力净流入-净额")
    main_net_inflow_pct = Column(Float, nullable=True, comment="主力净流入-净占比")
    super_big_net_inflow = Column(Float, nullable=True, comment="超大单净流入-净额")
    super_big_net_inflow_pct = Column(
        Float, nullable=True, comment="超大单净流入-净占比"
    )
    big_net_inflow = Column(Float, nullable=True, comment="大单净流入-净额")
    big_net_inflow_pct = Column(Float, nullable=True, comment="大单净流入-净占比")
    medium_net_inflow = Column(Float, nullable=True, comment="中单净流入-净额")
    medium_net_inflow_pct = Column(Float, nullable=True, comment="中单净流入-净占比")
    small_net_inflow = Column(Float, nullable=True, comment="小单净流入-净额")
    small_net_inflow_pct = Column(Float, nullable=True, comment="小单净流入-净占比")
    max_net_inflow_stock = Column(String(50), nullable=True, comment="主力净流入最大股")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )


class StockShareholderDetail(Base):
    """股东持股明细-十大流通股东"""

    __tablename__ = "stock_shareholder_detail"
    __table_args__ = (
        UniqueConstraint(
            "stock_code",
            "shareholder_name",
            "report_period",
            name="uk_stock_shareholder_detail",
        ),
        Index("idx_stock_shareholder_detail_code", "stock_code"),
        Index("idx_stock_shareholder_detail_period", "report_period"),
        {"comment": "股东持股明细-十大流通股东表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    shareholder_name = Column(String(100), nullable=False, comment="股东名称")
    shareholder_type = Column(String(50), nullable=True, comment="股东类型")
    stock_code = Column(String(10), nullable=False, comment="股票代码")
    stock_name = Column(String(50), nullable=False, comment="股票名称")
    report_period = Column(Date, nullable=False, comment="报告期")
    holding_amount = Column(Float, nullable=True, comment="期末持股数量（股）")
    holding_change = Column(Float, nullable=True, comment="期末持股数量变化（股）")
    holding_change_ratio = Column(
        Float, nullable=True, comment="期末持股数量变化比例（%）"
    )
    holding_trend = Column(String(20), nullable=True, comment="期末持股变动趋势")
    market_value = Column(Float, nullable=True, comment="期末持股流通市值（元）")
    announce_date = Column(Date, nullable=True, comment="公告日")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )


class StockIndustryPE(Base):
    """行业市盈率数据"""

    __tablename__ = "stock_industry_pe"
    __table_args__ = (
        UniqueConstraint(
            "industry_code", "trade_date", name="uk_industry_pe_code_date"
        ),
        Index("idx_industry_pe_date", "trade_date"),
        Index("idx_industry_pe_code", "industry_code"),
        {"comment": "行业市盈率数据表"},
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    industry_code = Column(String(20), nullable=False, comment="行业代码")
    industry_name = Column(String(50), nullable=False, comment="行业名称")
    trade_date = Column(Date, nullable=False, comment="交易日期")
    pe = Column(Float, nullable=True, comment="市盈率")
    pe_ttm = Column(Float, nullable=True, comment="市盈率TTM")
    pb = Column(Float, nullable=True, comment="市净率")
    ps = Column(Float, nullable=True, comment="市销率")
    ps_ttm = Column(Float, nullable=True, comment="市销率TTM")
    pcf = Column(Float, nullable=True, comment="市现率")
    pcf_ttm = Column(Float, nullable=True, comment="市现率TTM")
    dv_ratio = Column(Float, nullable=True, comment="股息率")
    dv_ttm = Column(Float, nullable=True, comment="股息率TTM")
    total_mv = Column(Float, nullable=True, comment="总市值(亿元)")
    industry_type = Column(String(20), nullable=False, comment="行业分类标准")
    create_time = Column(
        DateTime, nullable=False, default=datetime.now, comment="创建时间"
    )
    update_time = Column(
        DateTime,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )
