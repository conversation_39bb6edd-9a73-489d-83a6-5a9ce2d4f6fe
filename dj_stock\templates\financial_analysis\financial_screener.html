{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<style>
/* 页面整体样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.page-title {
    color: white !important;
    font-weight: 600;
}

.page-subtitle {
    color: rgba(255,255,255,0.9);
}

/* 筛选卡片样式 */
.filter-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.filter-header h6 {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

.filter-body {
    padding: 1.5rem;
    background: white;
}


/* 输入框样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    border-color: #667eea;
}

.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
}

.btn-outline-info:hover {
    background: #17a2b8;
    border-color: #17a2b8;
}

/* 表格样式 */
.table-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
}

.table-header h6 {
    margin: 0;
    color: white;
    font-weight: 600;
}

.table-responsive {
    border-radius: 0 0 12px 12px;
}

/* 表格内容优化 */
.table-sm th {
    padding: 0.5rem 0.3rem !important;
    font-size: 0.7rem;
    font-weight: 600;
    white-space: nowrap;
    border-bottom: 2px solid #dee2e6;
    background: #f8f9fa;
}

.table-sm td {
    padding: 0.4rem 0.3rem !important;
    font-size: 0.7rem;
    white-space: nowrap;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* 数值显示优化 */
.text-danger {
    color: #dc3545 !important;
    font-weight: 500;
}

.text-success {
    color: #198754 !important;
    font-weight: 500;
}

.text-warning {
    color: #fd7e14 !important;
    font-weight: 500;
}

/* 徽章样式优化 */
.badge {
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* 收藏按钮优化 */
.favorite-btn {
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem 0;
    }

    .filter-body {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-card {
    animation: fadeInUp 0.5s ease-out;
}

.filter-card:nth-child(2) {
    animation-delay: 0.1s;
}

.filter-card:nth-child(3) {
    animation-delay: 0.2s;
}

.filter-card:nth-child(4) {
    animation-delay: 0.3s;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block title %}智能选股筛选器{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="page-title">
                        <i class="ti ti-filter me-2"></i>智能选股筛选器
                    </h2>
                    <div class="page-subtitle">
                        多维度财务指标筛选，发现优质投资标的
                    </div>
                </div>
                <div class="col-auto">
                    <div class="btn-list">
                        <button type="button" class="btn btn-outline-light" data-bs-toggle="modal" data-bs-target="#helpModal">
                            <i class="ti ti-help me-1"></i>指标说明
                        </button>
                        <button type="button" class="btn btn-light" onclick="performAsyncSearch()">
                            <i class="ti ti-search me-1"></i>开始筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <form method="get" id="screenerForm">
                <!-- 基本筛选 -->
                <div class="filter-card">
                    <div class="filter-header">
                        <h6><i class="ti ti-building me-2"></i>基本筛选</h6>
                    </div>
                    <div class="filter-body">
                                <div class="row g-2">
                                    <!-- 行业选择 -->
                                    <div class="col-md-3">
                                        <label for="industry" class="form-label small">行业</label>
                                        <select class="form-select form-select-sm" id="industry" name="industry">
                                            <option value="">全部行业</option>
                                            {% for ind in industries %}
                                            <option value="{{ ind }}" {% if ind == filters.industry %}selected{% endif %}>{{ ind }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <!-- 市值范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">市值范围（亿元）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="market_cap_min" placeholder="最小值" value="{{ filters.market_cap_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="market_cap_max" placeholder="最大值" value="{{ filters.market_cap_max }}">
                                        </div>
                                    </div>

                                    <!-- 流通市值范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">流通市值（亿元）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="float_market_cap_min" placeholder="最小值" value="{{ filters.float_market_cap_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="float_market_cap_max" placeholder="最大值" value="{{ filters.float_market_cap_max }}">
                                        </div>
                                    </div>

                                    <!-- PE范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">PE范围</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="pe_min" placeholder="最小值" value="{{ filters.pe_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="pe_max" placeholder="最大值" value="{{ filters.pe_max }}">
                                        </div>
                                    </div>

                                    <!-- PB范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">PB范围</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="pb_min" placeholder="最小值" value="{{ filters.pb_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="pb_max" placeholder="最大值" value="{{ filters.pb_max }}">
                                        </div>
                                    </div>

                                    <!-- 搜索框 -->
                                    <div class="col-md-3">
                                        <label for="search" class="form-label small">搜索股票代码/名称</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="ti ti-search"></i></span>
                                            <input type="text" class="form-control" id="search" name="search" value="{{ filters.search }}" placeholder="输入股票代码或名称">
                                        </div>
                                    </div>
                                </div>
                    </div>
                </div>

                <!-- 财务指标筛选 -->
                <div class="filter-card">
                    <div class="filter-header">
                        <h6><i class="ti ti-chart-bar me-2"></i>财务指标筛选</h6>
                    </div>
                    <div class="filter-body">
                                <div class="row g-2">
                                    <!-- ROE范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">ROE范围（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="roe_min" placeholder="最小值" value="{{ filters.roe_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="roe_max" placeholder="最大值" value="{{ filters.roe_max }}">
                                        </div>
                                    </div>

                                    <!-- 净利润增长率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">净利润增长率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="net_profit_growth_min" placeholder="最小值" value="{{ filters.net_profit_growth_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="net_profit_growth_max" placeholder="最大值" value="{{ filters.net_profit_growth_max }}">
                                        </div>
                                    </div>

                                    <!-- 营收增长率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">营收增长率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="revenue_growth_min" placeholder="最小值" value="{{ filters.revenue_growth_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="revenue_growth_max" placeholder="最大值" value="{{ filters.revenue_growth_max }}">
                                        </div>
                                    </div>

                                    <!-- 毛利率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">毛利率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="gross_profit_margin_min" placeholder="最小值" value="{{ filters.gross_profit_margin_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="gross_profit_margin_max" placeholder="最大值" value="{{ filters.gross_profit_margin_max }}">
                                        </div>
                                    </div>

                                    <!-- 负债率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">负债率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="debt_ratio_min" placeholder="最小值" value="{{ filters.debt_ratio_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="debt_ratio_max" placeholder="最大值" value="{{ filters.debt_ratio_max }}">
                                        </div>
                                    </div>

                                    <!-- ROA范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">ROA范围（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="roa_min" placeholder="最小值" value="{{ filters.roa_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="roa_max" placeholder="最大值" value="{{ filters.roa_max }}">
                                        </div>
                                    </div>

                                    <!-- 净利率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">净利率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="net_profit_margin_min" placeholder="最小值" value="{{ filters.net_profit_margin_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="net_profit_margin_max" placeholder="最大值" value="{{ filters.net_profit_margin_max }}">
                                        </div>
                                    </div>

                                    <!-- 流动比率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">流动比率</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="current_ratio_min" placeholder="最小值" value="{{ filters.current_ratio_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="current_ratio_max" placeholder="最大值" value="{{ filters.current_ratio_max }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                <!-- 高级筛选 -->
                <div class="filter-card">
                    <div class="filter-header">
                        <h6><i class="ti ti-star me-2"></i>高级筛选</h6>
                    </div>
                    <div class="filter-body">
                                <div class="row g-2">
                                    <!-- 预设策略开关 -->
                                    <div class="col-12">
                                        <label class="form-label small mb-2">预设筛选策略</label>
                                        <div class="row g-2">
                                            <!-- 价值股筛选 -->
                                            <div class="col-md-4">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input preset-strategy" type="checkbox" id="value_stocks" name="value_stocks" value="1" {% if filters.value_stocks %}checked{% endif %} onchange="handlePresetStrategy('value_stocks', this.checked)">
                                                    <label class="form-check-label small" for="value_stocks">价值股筛选</label>
                                                    <small class="text-muted d-block">PE≤15, PB≤2, ROE≥10%, 负债率≤50%</small>
                                                </div>
                                            </div>

                                            <!-- 成长股筛选 -->
                                            <div class="col-md-4">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input preset-strategy" type="checkbox" id="growth_stocks" name="growth_stocks" value="1" {% if filters.growth_stocks %}checked{% endif %} onchange="handlePresetStrategy('growth_stocks', this.checked)">
                                                    <label class="form-check-label small" for="growth_stocks">成长股筛选</label>
                                                    <small class="text-muted d-block">营收增长≥20%, 净利润增长≥25%, ROE≥15%</small>
                                                </div>
                                            </div>

                                            <!-- 优质股筛选 -->
                                            <div class="col-md-4">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input preset-strategy" type="checkbox" id="quality_stocks" name="quality_stocks" value="1" {% if filters.quality_stocks %}checked{% endif %} onchange="handlePresetStrategy('quality_stocks', this.checked)">
                                                    <label class="form-check-label small" for="quality_stocks">优质股筛选</label>
                                                    <small class="text-muted d-block">连续3年ROE>15%, 负债率≤40%, 流动比率≥1.5</small>
                                                </div>
                                            </div>

                                            <!-- 分红股筛选 -->
                                            <div class="col-md-4">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input preset-strategy" type="checkbox" id="dividend_stocks" name="dividend_stocks" value="1" {% if filters.dividend_stocks %}checked{% endif %} onchange="handlePresetStrategy('dividend_stocks', this.checked)">
                                                    <label class="form-check-label small" for="dividend_stocks">分红股筛选</label>
                                                    <small class="text-muted d-block">ROE≥8%, 负债率≤60%, PE≤20, 连续分红≥5年</small>
                                                </div>
                                            </div>

                                            <!-- 小盘成长股 -->
                                            <div class="col-md-4">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input preset-strategy" type="checkbox" id="small_cap_growth" name="small_cap_growth" value="1" {% if filters.small_cap_growth %}checked{% endif %} onchange="handlePresetStrategy('small_cap_growth', this.checked)">
                                                    <label class="form-check-label small" for="small_cap_growth">小盘成长股</label>
                                                    <small class="text-muted d-block">市值≤100亿, 营收增长≥30%, 净利润增长≥35%</small>
                                                </div>
                                            </div>

                                            <!-- 低估值股票 -->
                                            <div class="col-md-4">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input preset-strategy" type="checkbox" id="undervalued_stocks" name="undervalued_stocks" value="1" {% if filters.undervalued_stocks %}checked{% endif %} onchange="handlePresetStrategy('undervalued_stocks', this.checked)">
                                                    <label class="form-check-label small" for="undervalued_stocks">低估值股票</label>
                                                    <small class="text-muted d-block">PE≤12, PB≤1.5, ROE≥12%, 净利率≥8%</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 连续3年ROE > 15% -->
                                    <div class="col-md-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="continuous_roe" name="continuous_roe" value="1" {% if filters.continuous_roe %}checked{% endif %}>
                                            <label class="form-check-label small" for="continuous_roe">连续3年ROE > 15%</label>
                                        </div>
                                    </div>

                                    <!-- 连续增长 -->
                                    <div class="col-md-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="continuous_growth" name="continuous_growth" value="1" {% if filters.continuous_growth %}checked{% endif %}>
                                            <label class="form-check-label small" for="continuous_growth">连续3年营收增长</label>
                                        </div>
                                    </div>

                                    <!-- 分红年数 -->
                                    <div class="col-md-4">
                                        <label class="form-label small">连续分红年数</label>
                                        <select class="form-select form-select-sm" name="dividend_years">
                                            <option value="">不限制</option>
                                            <option value="3" {% if filters.dividend_years == "3" %}selected{% endif %}>3年以上</option>
                                            <option value="5" {% if filters.dividend_years == "5" %}selected{% endif %}>5年以上</option>
                                            <option value="10" {% if filters.dividend_years == "10" %}selected{% endif %}>10年以上</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>

                <!-- 按钮组 -->
                <div class="text-center mt-4 mb-4">
                    <button type="submit" class="btn btn-primary px-4 me-3">
                        <i class="ti ti-search me-2"></i>开始筛选
                    </button>
                    <a href="{% url 'financial_analysis:financial_screener' %}" class="btn btn-outline-secondary px-4">
                        <i class="ti ti-refresh me-2"></i>重置条件
                    </a>
                </div>
            </form>

            <!-- 股票列表 -->
            <div class="table-card results-container">
                <div class="table-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="ti ti-table me-2"></i>
                        <h6 class="mb-0">筛选结果</h6>
                    </div>
                    <div class="d-flex align-items-center stats-info">
                        <div class="badge bg-white text-primary me-3">
                            <i class="ti ti-chart-bar me-1"></i>
                            {{ page_obj.paginator.count }}支股票
                        </div>
                        <div class="text-white small me-3" style="font-size: 0.85rem;">
                            <i class="ti ti-calendar me-1"></i>
                            行情：{{ latest_trade_date|date:"Y-m-d" }}
                        </div>
                        <div class="text-white small" style="font-size: 0.85rem;">
                            <i class="ti ti-report-money me-1"></i>
                            财报：{{ latest_report_date|date:"Y-m-d" }}
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-sm" style="font-size: 0.75rem;">
                        <thead class="table-light">
                            <tr>
                                <th width="60px" class="text-center">收藏</th>
                                <th width="90px"><a href="?sort=stock_code&order={% if sort_by == 'stock_code' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">代码 <i class="sort-icon {% if sort_by == 'stock_code' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="120px"><a href="?sort=stock_name&order={% if sort_by == 'stock_name' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">名称 <i class="sort-icon {% if sort_by == 'stock_name' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="100px"><a href="?sort=industry&order={% if sort_by == 'industry' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">行业 <i class="sort-icon {% if sort_by == 'industry' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="90px"><a href="?sort=latest_price&order={% if sort_by == 'latest_price' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">当前价(元) <i class="sort-icon {% if sort_by == 'latest_price' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="90px"><a href="?sort=change_percent&order={% if sort_by == 'change_percent' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">涨跌幅(%) <i class="sort-icon {% if sort_by == 'change_percent' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="90px"><a href="?sort=market_cap&order={% if sort_by == 'market_cap' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">市值(亿) <i class="sort-icon {% if sort_by == 'market_cap' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="80px"><a href="?sort=pe_ratio&order={% if sort_by == 'pe_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PE(倍) <i class="sort-icon {% if sort_by == 'pe_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="80px"><a href="?sort=pb_ratio&order={% if sort_by == 'pb_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PB(倍) <i class="sort-icon {% if sort_by == 'pb_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="80px"><a href="?sort=roe&order={% if sort_by == 'roe' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">ROE(%) <i class="sort-icon {% if sort_by == 'roe' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="100px"><a href="?sort=net_profit_growth&order={% if sort_by == 'net_profit_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">净利增长(%) <i class="sort-icon {% if sort_by == 'net_profit_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="100px"><a href="?sort=revenue_growth&order={% if sort_by == 'revenue_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">营收增长(%) <i class="sort-icon {% if sort_by == 'revenue_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="90px"><a href="?sort=gross_profit_margin&order={% if sort_by == 'gross_profit_margin' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">毛利率(%) <i class="sort-icon {% if sort_by == 'gross_profit_margin' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="90px"><a href="?sort=debt_ratio&order={% if sort_by == 'debt_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">负债率(%) <i class="sort-icon {% if sort_by == 'debt_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                <th width="80px">连续ROE</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stock in page_obj %}
                            <tr class="align-middle">
                                <td class="text-center">
                                    <button class="btn btn-sm btn-warning favorite-btn"
                                            data-code="{{ stock.stock_code }}"
                                            data-name="{{ stock.stock_name }}"
                                            title="添加到收藏">
                                        <i class="ti ti-star" style="font-size: 0.8rem;"></i>
                                    </button>
                                    <span class="favorite-icon d-none" data-code="{{ stock.stock_code }}">
                                        <i class="ti ti-star-filled text-warning" style="font-size: 0.8rem;"></i>
                                    </span>
                                </td>
                                <td><a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-primary fw-semibold">{{ stock.stock_code }}</a></td>
                                <td><span class="fw-medium">{{ stock.stock_name|truncatechars:12 }}</span></td>
                                <td><span class="badge bg-light text-dark" style="font-size: 0.7rem;">{{ stock.industry|truncatechars:8 }}</span></td>
                                <td>
                                    {% if stock.latest_price %}
                                        <span class="fw-medium">{{ stock.latest_price|floatformat:2 }}</span>
                                    {% else %}-{% endif %}
                                </td>
                                <td>
                                    {% if stock.change_percent != None %}
                                        <span class="badge {% if stock.change_percent > 0 %}bg-danger-subtle text-danger{% elif stock.change_percent < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                            {% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:1 }}%
                                        </span>
                                    {% else %}-{% endif %}
                                </td>
                                <td>{% if stock.market_cap %}{{ stock.market_cap|floatformat:0 }}{% else %}-{% endif %}</td>
                                <td>{% if stock.pe_ratio %}{{ stock.pe_ratio|floatformat:1 }}{% else %}-{% endif %}</td>
                                <td>{% if stock.pb_ratio %}{{ stock.pb_ratio|floatformat:1 }}{% else %}-{% endif %}</td>
                                <td>
                                    {% if stock.roe != None %}
                                        <span class="fw-medium {% if stock.roe > 15 %}text-danger{% elif stock.roe > 8 %}text-warning{% else %}text-muted{% endif %}">
                                            {{ stock.roe|floatformat:1 }}
                                        </span>
                                    {% else %}-{% endif %}
                                </td>
                                <td>
                                    {% if stock.net_profit_growth != None %}
                                        <span class="badge {% if stock.net_profit_growth > 0 %}bg-danger-subtle text-danger{% elif stock.net_profit_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                            {% if stock.net_profit_growth > 0 %}+{% endif %}{{ stock.net_profit_growth|floatformat:0 }}%
                                        </span>
                                    {% else %}-{% endif %}
                                </td>
                                <td>
                                    {% if stock.revenue_growth != None %}
                                        <span class="badge {% if stock.revenue_growth > 0 %}bg-danger-subtle text-danger{% elif stock.revenue_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                            {% if stock.revenue_growth > 0 %}+{% endif %}{{ stock.revenue_growth|floatformat:0 }}%
                                        </span>
                                    {% else %}-{% endif %}
                                </td>
                                <td>
                                    {% if stock.gross_profit_margin != None %}
                                        <span class="fw-medium {% if stock.gross_profit_margin > 30 %}text-danger{% elif stock.gross_profit_margin > 15 %}text-warning{% else %}text-muted{% endif %}">
                                            {{ stock.gross_profit_margin|floatformat:1 }}
                                        </span>
                                    {% else %}-{% endif %}
                                </td>
                                <td>
                                    {% if stock.debt_ratio != None %}
                                        <span class="fw-medium {% if stock.debt_ratio > 60 %}text-danger{% elif stock.debt_ratio > 40 %}text-warning{% else %}text-success{% endif %}">
                                            {{ stock.debt_ratio|floatformat:1 }}
                                        </span>
                                    {% else %}-{% endif %}
                                </td>
                                <td class="text-center">
                                    {% if stock.continuous_roe %}
                                        <span class="badge bg-success-subtle text-success" style="font-size: 0.7rem;">✓</span>
                                    {% else %}
                                        <span class="badge bg-secondary-subtle text-secondary" style="font-size: 0.7rem;">✗</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="15" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="ti ti-search-off fs-1 text-muted mb-3"></i>
                                        <p class="mb-1 fw-medium">没有找到符合条件的股票</p>
                                        <p class="text-muted small">请尝试调整筛选条件</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>

                <!-- 分页 -->
                <div class="mt-4 p-3 pagination-container" style="background: #f8f9fa; border-radius: 0 0 12px 12px;">
                    {% include "includes/pagination.html" with page_obj=page_obj rows_per_page=rows_per_page %}
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 指标说明模态框 -->
<div class="modal modal-blur fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-help me-2"></i>财务指标说明
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-chart-line me-2"></i>估值指标</h6>
                        <div class="mb-3">
                            <strong>PE (市盈率)</strong>
                            <p class="text-muted small mb-2">股价 ÷ 每股收益，反映投资回收期</p>
                            <span class="badge bg-success-subtle text-success">优秀: &lt;15</span>
                            <span class="badge bg-warning-subtle text-warning">一般: 15-30</span>
                            <span class="badge bg-danger-subtle text-danger">偏高: &gt;30</span>
                        </div>
                        <div class="mb-3">
                            <strong>PB (市净率)</strong>
                            <p class="text-muted small mb-2">股价 ÷ 每股净资产，反映资产价值</p>
                            <span class="badge bg-success-subtle text-success">优秀: &lt;2</span>
                            <span class="badge bg-warning-subtle text-warning">一般: 2-5</span>
                            <span class="badge bg-danger-subtle text-danger">偏高: &gt;5</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-trending-up me-2"></i>盈利能力</h6>
                        <div class="mb-3">
                            <strong>ROE (净资产收益率)</strong>
                            <p class="text-muted small mb-2">净利润 ÷ 净资产，反映盈利能力</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;15%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 8-15%</span>
                            <span class="badge bg-success-subtle text-success">一般: &lt;8%</span>
                        </div>
                        <div class="mb-3">
                            <strong>ROA (总资产收益率)</strong>
                            <p class="text-muted small mb-2">净利润 ÷ 总资产，反映资产使用效率</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;5%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 2-5%</span>
                            <span class="badge bg-success-subtle text-success">一般: &lt;2%</span>
                        </div>
                        <div class="mb-3">
                            <strong>净利率</strong>
                            <p class="text-muted small mb-2">净利润 ÷ 营业收入，反映盈利效率</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;10%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 5-10%</span>
                            <span class="badge bg-success-subtle text-success">一般: &lt;5%</span>
                        </div>
                        <div class="mb-3">
                            <strong>毛利率</strong>
                            <p class="text-muted small mb-2">毛利润 ÷ 营业收入，反映产品竞争力</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;30%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 15-30%</span>
                            <span class="badge bg-muted-subtle text-muted">一般: &lt;15%</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-rocket me-2"></i>成长性指标</h6>
                        <div class="mb-3">
                            <strong>营收增长率</strong>
                            <p class="text-muted small mb-2">本期营收相比去年同期的增长幅度</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;20%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 10-20%</span>
                            <span class="badge bg-success-subtle text-success">下降: &lt;0%</span>
                        </div>
                        <div class="mb-3">
                            <strong>净利润增长率</strong>
                            <p class="text-muted small mb-2">本期净利润相比去年同期的增长幅度</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;25%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 10-25%</span>
                            <span class="badge bg-success-subtle text-success">下降: &lt;0%</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-shield-check me-2"></i>财务健康度</h6>
                        <div class="mb-3">
                            <strong>资产负债率</strong>
                            <p class="text-muted small mb-2">总负债 ÷ 总资产，反映财务风险</p>
                            <span class="badge bg-success-subtle text-success">安全: &lt;40%</span>
                            <span class="badge bg-warning-subtle text-warning">适中: 40-60%</span>
                            <span class="badge bg-danger-subtle text-danger">偏高: &gt;60%</span>
                        </div>
                        <div class="mb-3">
                            <strong>流动比率</strong>
                            <p class="text-muted small mb-2">流动资产 ÷ 流动负债，反映短期偿债能力</p>
                            <span class="badge bg-success-subtle text-success">安全: &gt;2</span>
                            <span class="badge bg-warning-subtle text-warning">适中: 1-2</span>
                            <span class="badge bg-danger-subtle text-danger">偏低: &lt;1</span>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="ti ti-info-circle me-2"></i>
                    <strong>颜色说明：</strong>
                    <span class="text-danger">红色</span>表示上涨/优秀，
                    <span class="text-success">绿色</span>表示下跌/较差，
                    <span class="text-warning">黄色</span>表示中等水平。
                    具体标准因行业而异，请结合行业特点分析。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 全局变量
    var favoriteStocks = [];

    // 显示通知函数（全局）
    function showNotification(type, title, message) {
        const notification = document.createElement('div');
        notification.className = 'toast show position-fixed top-0 end-0 m-3';
        notification.style.zIndex = '9999';

        let iconClass = 'ti-info-circle';
        if (type === 'success') iconClass = 'ti-check';
        else if (type === 'danger') iconClass = 'ti-alert-triangle';
        else if (type === 'warning') iconClass = 'ti-alert-triangle';

        notification.innerHTML = `
            <div class="toast-header bg-${type} text-white">
                <i class="ti ${iconClass} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close btn-close-white" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="toast-body">${message}</div>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 500);
        }, 3000);
    }



    document.addEventListener('DOMContentLoaded', function() {
        // 添加样式
        var style = document.createElement('style');
        style.textContent = '.sort-link { color: inherit; text-decoration: none; cursor: pointer; width: 100%; display: inline-flex; align-items: center; justify-content: space-between; padding: 0.25rem 0.5rem; border-radius: 0.25rem; transition: background-color 0.2s; } .sort-link:hover { color: #0d6efd; background-color: rgba(13, 110, 253, 0.05); } .sort-icon { margin-left: 0.5rem; opacity: 0.5; transition: opacity 0.2s; } .sort-link:hover .sort-icon { opacity: 1; } th { white-space: nowrap; transition: background-color 0.3s; padding: 0.4rem 0.5rem !important; font-weight: 500; } td { padding: 0.3rem 0.5rem !important; } th:hover { background-color: rgba(13, 110, 253, 0.05); } .bg-light-primary { background-color: rgba(13, 110, 253, 0.1); } .bg-light-info { background-color: rgba(13, 202, 240, 0.1); } .rounded-pill { border-radius: 50rem; } .table-sm { font-size: 0.75rem; } .card-body.p-2 .table { margin-bottom: 0.5rem; } .favorite-icon { cursor: pointer; } .favorite-icon.active { display: inline-block !important; } @keyframes pulse { 0% { box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7); } 70% { box-shadow: 0 0 0 10px rgba(25, 135, 84, 0); } 100% { box-shadow: 0 0 0 0 rgba(25, 135, 84, 0); } } .pulse-animation { animation: pulse 1.5s infinite; } .favorite-btn.disabled { opacity: 0.6; cursor: not-allowed; }';
        document.head.appendChild(style);

        // 元素引用
        var favoriteIcons = document.querySelectorAll('.favorite-icon');
        var favoriteBtns = document.querySelectorAll('.favorite-btn');

        // 获取已收藏的股票
        function loadFavoriteStocks() {
            fetch('{% url "financial_analysis:get_favorite_stocks" %}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        favoriteStocks = data.favorites;
                        updateFavoriteIcons();
                        updateFavoriteButtons();
                    }
                })
                .catch(error => {
                    console.error('Error loading favorites:', error);
                });
        }

        // 更新收藏图标显示
        function updateFavoriteIcons() {
            for (var i = 0; i < favoriteIcons.length; i++) {
                var icon = favoriteIcons[i];
                var stockCode = icon.dataset.code;
                if (favoriteStocks.indexOf(stockCode) !== -1) {
                    icon.classList.remove('d-none');
                    icon.classList.add('active');
                } else {
                    icon.classList.add('d-none');
                    icon.classList.remove('active');
                }
            }
        }

        // 更新收藏按钮状态
        function updateFavoriteButtons() {
            for (var i = 0; i < favoriteBtns.length; i++) {
                var btn = favoriteBtns[i];
                var stockCode = btn.dataset.code;
                if (favoriteStocks.indexOf(stockCode) !== -1) {
                    btn.classList.remove('btn-warning');
                    btn.classList.add('btn-success');
                    btn.setAttribute('title', '已收藏');
                    btn.disabled = true;
                    btn.classList.add('disabled');
                } else {
                    btn.classList.add('btn-warning');
                    btn.classList.remove('btn-success');
                    btn.setAttribute('title', '添加到收藏');
                    btn.disabled = false;
                    btn.classList.remove('disabled');
                }
            }
        }

        // 添加收藏按钮点击事件
        for (var i = 0; i < favoriteBtns.length; i++) {
            (function(btn) {
                btn.addEventListener('click', function() {
                    var stockCode = this.dataset.code;
                    var stockName = this.dataset.name;

                    // 如果已经收藏过，则不再处理
                    if (favoriteStocks.indexOf(stockCode) !== -1) {
                        return;
                    }

                    // 显示加载状态
                    this.innerHTML = '<i class="ti ti-loader ti-spin"></i>';
                    this.disabled = true;

                    var self = this;
                    // 发送AJAX请求
                    fetch('{% url "financial_analysis:ajax_add_favorite" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: JSON.stringify({
                            stock_code: stockCode,
                            stock_name: stockName
                        })
                    })
                    .then(function(response) { return response.json(); })
                    .then(function(data) {
                        if (data.success) {
                            // 成功处理
                            showNotification('success', '收藏成功', data.message);

                            // 如果是新添加的收藏，则更新收藏列表
                            if (data.created && favoriteStocks.indexOf(data.stock_code) === -1) {
                                favoriteStocks.push(data.stock_code);
                            }

                            // 更新UI
                            updateFavoriteIcons();
                            updateFavoriteButtons();
                        } else {
                            // 失败处理
                            showNotification('danger', '收藏失败', data.message);
                            self.innerHTML = '<i class="ti ti-star"></i>';
                            self.disabled = false;
                        }
                    })
                    .catch(function(error) {
                        console.error('Error:', error);
                        showNotification('danger', '收藏失败', '网络错误，请稍后再试');
                        self.innerHTML = '<i class="ti ti-star"></i>';
                        self.disabled = false;
                    });
                });
            })(favoriteBtns[i]);
        }

        // 初始化收藏功能
        loadFavoriteStocks();


    });





    // 清空筛选条件
    function clearFilters() {
        document.getElementById('screenerForm').reset();
        showNotification('info', '条件已清空', '所有筛选条件已重置。');
    }

    // 处理预设策略开关
    function handlePresetStrategy(strategyKey, isChecked) {
        console.log('预设策略开关:', strategyKey, '状态:', isChecked);

        if (isChecked) {
            // 取消其他预设策略的选中状态
            var allPresetSwitches = document.querySelectorAll('.preset-strategy');
            for (var i = 0; i < allPresetSwitches.length; i++) {
                if (allPresetSwitches[i].id !== strategyKey) {
                    allPresetSwitches[i].checked = false;
                }
            }

            // 显示提示信息
            var strategyNames = {
                'value_stocks': '价值股筛选',
                'growth_stocks': '成长股筛选',
                'quality_stocks': '优质股筛选',
                'dividend_stocks': '分红股筛选',
                'small_cap_growth': '小盘成长股',
                'undervalued_stocks': '低估值股票'
            };

            var strategyName = strategyNames[strategyKey] || strategyKey;
            showNotification('success', '策略已选择', '已选择"' + strategyName + '"策略，点击"开始筛选"按钮查看结果。');
        } else {
            showNotification('info', '策略已取消', '预设策略已取消，点击"开始筛选"按钮查看所有股票。');
        }
    }


        // 初始化收藏功能
        loadFavoriteStocks();
    });

    // 异步搜索功能
    function performAsyncSearch() {
        console.log('开始异步搜索...');

        // 显示加载状态
        showLoadingState();

        // 获取表单数据
        var form = document.getElementById('screenerForm');
        var formData = new FormData(form);

        // 转换为URL参数
        var params = new URLSearchParams();
        for (var pair of formData.entries()) {
            if (pair[1]) { // 只添加非空值
                params.append(pair[0], pair[1]);
            }
        }

        // 发送AJAX请求
        fetch(window.location.pathname + '?' + params.toString(), {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(function(response) {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(function(html) {
            // 解析返回的HTML
            var parser = new DOMParser();
            var doc = parser.parseFromString(html, 'text/html');

            // 更新结果区域
            updateSearchResults(doc);

            // 更新URL（不刷新页面）
            var newUrl = window.location.pathname + '?' + params.toString();
            window.history.pushState({}, '', newUrl);

            // 隐藏加载状态
            hideLoadingState();

            // 重新初始化收藏功能
            initializeFavoriteButtons();
            loadFavoriteStocks();

            // 显示搜索完成提示
            var resultsCount = doc.querySelector('.stats-info .badge');
            var count = resultsCount ? resultsCount.textContent.match(/\d+/) : ['0'];
            showNotification('success', '筛选完成', '找到 ' + count[0] + ' 只符合条件的股票');

            console.log('异步搜索完成');
        })
        .catch(function(error) {
            console.error('搜索失败:', error);
            hideLoadingState();
            showNotification('error', '搜索失败', '请稍后重试或刷新页面。');
        });
    }

    // 显示加载状态
    function showLoadingState() {
        var resultsContainer = document.querySelector('.results-container');
        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="table-card"><div class="table-header d-flex justify-content-between align-items-center"><div class="d-flex align-items-center"><i class="ti ti-table me-2"></i><h6 class="mb-0">筛选结果</h6></div><div class="d-flex align-items-center stats-info"><div class="badge bg-white text-primary me-3"><i class="ti ti-chart-bar me-1"></i>搜索中...</div></div></div><div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-3 text-muted">正在筛选股票，请稍候...</p></div></div>';
        }

        // 禁用搜索按钮
        var searchButton = document.querySelector('button[onclick="performAsyncSearch()"]');
        if (searchButton) {
            searchButton.disabled = true;
            searchButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status"></span>筛选中...';
        }
    }

    // 隐藏加载状态
    function hideLoadingState() {
        // 恢复搜索按钮
        var searchButton = document.querySelector('button[onclick="performAsyncSearch()"]');
        if (searchButton) {
            searchButton.disabled = false;
            searchButton.innerHTML = '<i class="ti ti-search me-1"></i>开始筛选';
        }
    }

    // 更新搜索结果
    function updateSearchResults(doc) {
        // 更新整个结果容器
        var newResultsContainer = doc.querySelector('.results-container');
        var currentResultsContainer = document.querySelector('.results-container');
        if (newResultsContainer && currentResultsContainer) {
            currentResultsContainer.innerHTML = newResultsContainer.innerHTML;
        }

        // 更新分页信息
        var newPagination = doc.querySelector('.pagination-container');
        var currentPagination = document.querySelector('.pagination-container');
        if (newPagination && currentPagination) {
            currentPagination.innerHTML = newPagination.innerHTML;
        }

        // 重新绑定分页链接的点击事件
        bindPaginationEvents();
    }

    // 绑定分页链接的点击事件
    function bindPaginationEvents() {
        var paginationLinks = document.querySelectorAll('.pagination-container a');
        for (var i = 0; i < paginationLinks.length; i++) {
            paginationLinks[i].addEventListener('click', function(e) {
                e.preventDefault();
                var url = this.href;

                // 显示加载状态
                showLoadingState();

                // 发送AJAX请求
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(function(response) {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(function(html) {
                    var parser = new DOMParser();
                    var doc = parser.parseFromString(html, 'text/html');

                    updateSearchResults(doc);

                    // 更新URL
                    window.history.pushState({}, '', url);

                    hideLoadingState();

                    // 重新初始化收藏功能
                    initializeFavoriteButtons();
                    loadFavoriteStocks();

                    // 滚动到顶部
                    document.querySelector('.results-container').scrollIntoView({ behavior: 'smooth' });
                })
                .catch(function(error) {
                    console.error('分页加载失败:', error);
                    hideLoadingState();
                    showNotification('error', '加载失败', '请稍后重试。');
                });
            });
        }
    }

    // 重新初始化收藏按钮事件
    function initializeFavoriteButtons() {
        var favoriteBtns = document.querySelectorAll('.favorite-btn');

        for (var i = 0; i < favoriteBtns.length; i++) {
            (function(btn) {
                // 移除旧的事件监听器（如果有的话）
                btn.replaceWith(btn.cloneNode(true));
                btn = document.querySelector('[data-code="' + btn.dataset.code + '"].favorite-btn');

                btn.addEventListener('click', function() {
                    var stockCode = this.dataset.code;
                    var stockName = this.dataset.name;

                    // 如果已经收藏过，则不再处理
                    if (favoriteStocks.indexOf(stockCode) !== -1) {
                        return;
                    }

                    // 显示加载状态
                    this.innerHTML = '<i class="ti ti-loader ti-spin"></i>';
                    this.disabled = true;

                    var self = this;
                    // 发送AJAX请求
                    fetch('{% url "financial_analysis:ajax_add_favorite" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: JSON.stringify({
                            stock_code: stockCode,
                            stock_name: stockName
                        })
                    })
                    .then(function(response) { return response.json(); })
                    .then(function(data) {
                        if (data.success) {
                            showNotification('success', '收藏成功', data.message);
                            if (data.created && favoriteStocks.indexOf(data.stock_code) === -1) {
                                favoriteStocks.push(data.stock_code);
                            }
                            updateFavoriteIcons();
                            updateFavoriteButtons();
                        } else {
                            showNotification('danger', '收藏失败', data.message);
                            self.innerHTML = '<i class="ti ti-star"></i>';
                            self.disabled = false;
                        }
                    })
                    .catch(function(error) {
                        console.error('Error:', error);
                        showNotification('danger', '收藏失败', '网络错误，请稍后再试');
                        self.innerHTML = '<i class="ti ti-star"></i>';
                        self.disabled = false;
                    });
                });
            })(favoriteBtns[i]);
        }
    }






</script>
{% endblock %}