{% extends 'base.html' %}

{% block title %}涨跌停数据 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">涨跌停数据</h2>
        <div class="text-muted mt-1">查看当日涨停和跌停的股票列表</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <form method="get" class="d-flex">
              <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
              <select class="form-select ms-2" name="type">
                <option value="涨停" {% if limit_type == '涨停' %}selected{% endif %}>涨停</option>
                <option value="跌停" {% if limit_type == '跌停' %}selected{% endif %}>跌停</option>
              </select>
              <select class="form-select ms-2" name="industry">
                <option value="">全部行业</option>
                {% for ind in industries %}
                  {% if ind %}
                  <option value="{{ ind }}" {% if industry == ind %}selected{% endif %}>{{ ind }}</option>
                  {% endif %}
                {% endfor %}
              </select>
              <button type="submit" class="btn ms-2">查询</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">涨停数量</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 text-up">{{ limit_up_count }}</div>
            <div class="d-flex mb-2">
              <div>占比</div>
              <div class="ms-auto">
                {% if limit_up_count > 0 %}
                {{ limit_up_count|floatformat:2 }}%
                {% else %}
                0.00%
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">跌停数量</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 text-down">{{ limit_down_count }}</div>
            <div class="d-flex mb-2">
              <div>占比</div>
              <div class="ms-auto">
                {% if limit_down_count > 0 %}
                {{ limit_down_count|floatformat:2 }}%
                {% else %}
                0.00%
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">涨跌停比</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if limit_down_count > 0 %}
              {{ limit_up_count|floatformat:0 }}:{{ limit_down_count|floatformat:0 }}
              {% else %}
              {{ limit_up_count|floatformat:0 }}:0
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>市场情绪</div>
              <div class="ms-auto">
                {% if limit_up_count > limit_down_count %}
                <span class="text-up">偏多</span>
                {% elif limit_up_count < limit_down_count %}
                <span class="text-down">偏空</span>
                {% else %}
                <span>中性</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">当前查询</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ limit_type }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if limit_type == '涨停' %}
              <span class="text-up">{{ stocks.paginator.count }}</span>
              {% else %}
              <span class="text-down">{{ stocks.paginator.count }}</span>
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>行业</div>
              <div class="ms-auto">
                {{ industry|default:"全部" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 涨跌停表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">{{ limit_type }}数据 ({{ selected_date|date:"Y-m-d" }})</h3>
      </div>

      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>所属行业</th>
              <th>最新价</th>
              <th>涨跌幅</th>
              <th>连板数</th>
              <th>炸板次数</th>
              <th>首次封板时间</th>
              <th>最后封板时间</th>
              <th>封单资金</th>
              <th>换手率</th>
              <th>流通市值</th>
            </tr>
          </thead>
          <tbody>
            {% for stock in stocks %}
            <tr>
              <td>{{ stock.stock_code }}</td>
              <td>{{ stock.stock_name }}</td>
              <td>{{ stock.industry|default:"-" }}</td>
              <td>{{ stock.latest_price|floatformat:2 }}</td>
              <td>
                {% if stock.change_ratio > 0 %}
                <span class="text-up">+{{ stock.change_ratio|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ stock.change_ratio|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>{{ stock.continuous_limit }}</td>
              <td>{{ stock.break_count }}</td>
              <td>{{ stock.first_time|time:"H:i:s" }}</td>
              <td>{{ stock.last_time|time:"H:i:s" }}</td>
              <td>{{ stock.fund_amount|floatformat:2 }}万</td>
              <td>{{ stock.turnover_ratio|floatformat:2 }}%</td>
              <td>{{ stock.circulation_market_value|floatformat:2 }}亿</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="12" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前日期没有{{ limit_type }}数据，请尝试选择其他日期。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% include "includes/pagination.html" with page_obj=stocks rows_per_page=per_page %}

    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })
  });
</script>
{% endblock %}
