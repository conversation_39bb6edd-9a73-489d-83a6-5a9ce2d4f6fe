{% extends "base.html" %} {% load humanize %} {% block title %}{{ stock.stock_name }}({{ stock.stock_code }}) - 股票详情{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <div class="d-flex align-items-baseline">
          <h2 class="page-title mb-0">{{ stock.stock_name }}({{ stock.stock_code }})</h2>
          {% if stock.industry %}
          <a href="{% url 'market_data:industry_stocks' stock.industry %}" class="badge bg-azure-lt text-reset ms-2">{{ stock.industry }}</a>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 数据采集状态提示 -->
{% if data_status_message %}
<div class="container-xl mb-3">
  <div class="alert alert-info alert-dismissible" role="alert">
    <div class="d-flex">
      <div>
        {% if data_status.financial == 'complete' or data_status.history == 'complete' %}
        <i class="ti ti-check text-success"></i>
        {% elif data_status.financial == 'fetching' or data_status.history == 'fetching' %}
        <i class="ti ti-loader-2 text-primary"></i>
        {% elif data_status.financial == 'error' or data_status.history == 'error' %}
        <i class="ti ti-alert-triangle text-danger"></i>
        {% else %}
        <i class="ti ti-info-circle text-warning"></i>
        {% endif %}
      </div>
      <div class="ms-2">{{ data_status_message }}</div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
  </div>
</div>
{% endif %}

<!-- 历史数据状态信息 -->
{% if history_data_status %}
<div class="container-xl mb-3">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">数据状态</h3>
    </div>
    <div class="card-body">
      <div class="datagrid">
        <div class="datagrid-item">
          <div class="datagrid-title">历史数据状态</div>
          <div class="datagrid-content">
            <span
              class="badge {% if history_data_status.status == 'complete' %}bg-success{% elif history_data_status.status == 'error' %}bg-danger{% elif history_data_status.status == 'fetching' %}bg-primary{% else %}bg-warning{% endif %}"
            >
              {{ history_data_status.get_status_display }}
            </span>
          </div>
        </div>
        <div class="datagrid-item">
          <div class="datagrid-title">记录数量</div>
          <div class="datagrid-content">{{ history_data_status.record_count|default:"0" }}</div>
        </div>
        <div class="datagrid-item">
          <div class="datagrid-title">数据起始日期</div>
          <div class="datagrid-content">{{ history_data_status.date_from|default:"-" }}</div>
        </div>
        <div class="datagrid-item">
          <div class="datagrid-title">数据结束日期</div>
          <div class="datagrid-content">{{ history_data_status.date_to|default:"-" }}</div>
        </div>
        <div class="datagrid-item">
          <div class="datagrid-title">最后更新时间</div>
          <div class="datagrid-content">{{ history_data_status.last_update }}</div>
        </div>
        {% if history_data_status.error_message %}
        <div class="datagrid-item">
          <div class="datagrid-title">错误信息</div>
          <div class="datagrid-content text-danger">{{ history_data_status.error_message }}</div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endif %}

<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <!-- 左侧栏 -->
      <div class="col-lg-6 col-md-12">
        <!-- 最新行情卡片 -->
        <div class="card mb-3">
          <div class="card-header">
            <h3 class="card-title">最新行情</h3>
          </div>
          <div class="card-body">
            {% if latest_quote %}
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <div class="d-flex align-items-baseline">
                    <h1 class="mb-0 me-2 {% if latest_quote.change_percent > 0 %}text-danger{% elif latest_quote.change_percent < 0 %}text-success{% endif %}">
                      {{ latest_quote.close_price|floatformat:2 }}
                    </h1>
                    <span class="{% if latest_quote.change_percent > 0 %}text-danger{% elif latest_quote.change_percent < 0 %}text-success{% endif %}">
                      {{ latest_quote.change_percent|floatformat:2 }}%
                    </span>
                  </div>
                  <small class="text-muted">{{ latest_quote.trade_date|date:"Y-m-d" }}</small>
                </div>
                <table class="table table-sm">
                  <tr>
                    <td>开盘价：</td>
                    <td class="text-end">{{ latest_quote.open_price|floatformat:2 }}</td>
                  </tr>
                  <tr>
                    <td>最高价：</td>
                    <td class="text-end">{{ latest_quote.high_price|floatformat:2 }}</td>
                  </tr>
                  <tr>
                    <td>最低价：</td>
                    <td class="text-end">{{ latest_quote.low_price|floatformat:2 }}</td>
                  </tr>
                </table>
              </div>
              <div class="col-md-6">
                <table class="table table-sm">
                  <tr>
                    <td>成交量：</td>
                    <td class="text-end">{{ latest_quote.volume|intcomma }}万手</td>
                  </tr>
                  <tr>
                    <td>成交额：</td>
                    <td class="text-end">{{ latest_quote.amount|intcomma }}亿元</td>
                  </tr>
                  <tr>
                    <td>换手率：</td>
                    <td class="text-end">{{ latest_quote.turnover_rate|floatformat:2 }}%</td>
                  </tr>
                  <tr>
                    <td>市盈率：</td>
                    <td class="text-end">{{ latest_quote.pe_ratio|floatformat:2 }}</td>
                  </tr>
                  <tr>
                    <td>市净率：</td>
                    <td class="text-end">{{ latest_quote.pb_ratio|floatformat:2 }}</td>
                  </tr>
                </table>
              </div>
            </div>
            {% else %}
            <div class="empty">
              <div class="empty-img">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="icon icon-tabler icon-tabler-chart-line"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                  <path d="M4 19l16 0"></path>
                  <path d="M4 15l4 -6l4 2l4 -5l4 4"></path>
                </svg>
              </div>
              <p class="empty-title">暂无行情数据</p>
              <p class="empty-subtitle text-muted">该股票暂时没有最新行情数据</p>
            </div>
            {% endif %}
          </div>
        </div>

        <!-- 财务指标卡片 -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <div>
              <h3 class="card-title mb-0">财务指标</h3>
              {% if latest_indicator %}
              <small class="text-muted">报告期：{{ latest_indicator.report_date|date:"Y-m-d" }}</small>
              {% endif %}
            </div>
            <a href="{% url 'financial_analysis:financial_indicator' stock.stock_code %}" class="btn btn-sm btn-primary"> 查看完整财务指标 </a>
          </div>
          <div class="card-body">
            {% if latest_indicator %}
            <div class="row g-3">
              <!-- 收入和利润 -->
              <div class="col-12">
                <div class="row g-3">
                  <div class="col-sm-6">
                    <div class="card">
                      <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                          <div class="subheader">营业收入(亿)</div>
                          <div
                            class="ms-auto {% if latest_indicator.total_revenue_growth > 0 %}text-danger{% elif latest_indicator.total_revenue_growth < 0 %}text-success{% endif %}"
                          >
                            {{ latest_indicator.total_revenue_growth|floatformat:2 }}%
                          </div>
                        </div>
                        <div class="h3 mb-0">{{ latest_indicator.total_revenue_display|floatformat:2|intcomma }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="card">
                      <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                          <div class="subheader">每股收益</div>
                        </div>
                        <div class="h3 mb-0">{{ latest_indicator.eps|floatformat:3 }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 其他指标 -->
              <div class="col-12">
                <div class="row g-3">
                  <div class="col-6 col-sm-3">
                    <div class="card">
                      <div class="card-body p-2">
                        <div class="subheader">每股净资产</div>
                        <div class="h4 mb-0">{{ latest_indicator.nav|floatformat:2 }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-sm-3">
                    <div class="card">
                      <div class="card-body p-2">
                        <div class="subheader">ROE</div>
                        <div class="h4 mb-0 {% if latest_indicator.roe > 0 %}text-danger{% elif latest_indicator.roe < 0 %}text-success{% endif %}">
                          {{ latest_indicator.roe|floatformat:2 }}%
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-sm-3">
                    <div class="card">
                      <div class="card-body p-2">
                        <div class="subheader">资产负债率</div>
                        <div class="h4 mb-0">{{ latest_indicator.debt_asset_ratio|floatformat:2 }}%</div>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-sm-3">
                    <div class="card">
                      <div class="card-body p-2">
                        <div class="subheader">每股经营现金流</div>
                        <div class="h4 mb-0">{{ latest_indicator.ocf_per_share|floatformat:2 }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {% else %}
            <div class="empty">
              <div class="empty-img">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="icon icon-tabler icon-tabler-chart-line"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                  <path d="M4 19l16 0"></path>
                  <path d="M4 15l4 -6l4 2l4 -5l4 4"></path>
                </svg>
              </div>
              <p class="empty-title">暂无财务数据</p>
              <p class="empty-subtitle text-muted">该股票暂时没有财务指标数据</p>
            </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- 右侧栏 -->
      <div class="col-lg-6 col-md-12">
        <!-- 历史行情数据卡片 -->
        <div class="card mb-3">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">最近交易行情</h3>
            <a href="{% url 'market_data:stock_history' stock.stock_code %}" class="btn btn-sm btn-primary"> 查看更多 </a>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive" style="max-height: 400px; overflow-y: auto">
              <table class="table table-vcenter card-table table-striped">
                <thead style="position: sticky; top: 0; background: white; z-index: 1">
                  <tr>
                    <th>日期</th>
                    <th class="text-end">收盘价</th>
                    <th class="text-end">涨跌幅</th>
                    <th class="text-end">成交量</th>
                    <th class="text-end">换手率</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in history_data %}
                  <tr>
                    <td>{{ item.trade_date|date:"Y-m-d" }}</td>
                    <td class="text-end">{{ item.close_price|floatformat:2 }}</td>
                    <td class="text-end {% if item.change_percent > 0 %}text-danger{% elif item.change_percent < 0 %}text-success{% endif %}">
                      {{ item.change_percent|floatformat:2 }}%
                    </td>
                    <td class="text-end">{{ item.volume|intcomma }}万手</td>
                    <td class="text-end">{{ item.turnover_rate|floatformat:2 }}%</td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="5" class="text-center">暂无历史数据</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 分红记录卡片 -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <div>
              <h3 class="card-title mb-0">最新分红记录</h3>
              <small class="text-muted">显示最近5条分红记录</small>
            </div>
            <a href="{% url 'financial_analysis:dividend_list' %}?q={{ stock.stock_code }}" class="btn btn-sm btn-primary">
              <i class="ti ti-list"></i>
              查看更多
            </a>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive" style="max-height: 400px; overflow-y: auto">
              <table class="table table-vcenter card-table table-striped m-0">
                <thead>
                  <tr>
                    <th>分红年度</th>
                    <th>现金分红<br /><small class="text-muted">(元/10股)</small></th>
                    <th>送股<br /><small class="text-muted">(股/10股)</small></th>
                    <th>转增<br /><small class="text-muted">(股/10股)</small></th>
                    <th>合计<br /><small class="text-muted">(元/10股)</small></th>
                    <th>公告日期</th>
                    <th>股权登记日</th>
                    <th>除权除息日</th>
                    <th>红利发放日</th>
                    <th>实施状态</th>
                  </tr>
                </thead>
                <tbody>
                  {% for dividend in latest_dividends %}
                  <tr>
                    <td>{{ dividend.registration_date|date:"Y" }}</td>
                    <td class="text-danger fw-bold">{% if dividend.cash_dividend %} {{ dividend.cash_dividend|floatformat:3 }} {% else %} -- {% endif %}</td>
                    <td class="text-danger fw-bold">{% if dividend.share_dividend %} {{ dividend.share_dividend|floatformat:3 }} {% else %} -- {% endif %}</td>
                    <td class="text-danger fw-bold">{% if dividend.share_transfer %} {{ dividend.share_transfer|floatformat:3 }} {% else %} -- {% endif %}</td>
                    <td class="text-danger fw-bold">{% if dividend.cash_dividend %} {{ dividend.cash_dividend|floatformat:3 }} {% else %} -- {% endif %}</td>
                    <td>
                      {% if dividend.announce_date %}
                      <span class="text-muted" data-bs-toggle="tooltip" title="{{ dividend.announce_date|date:'Y-m-d' }}">
                        {{ dividend.announce_date|date:"Y-m-d" }}
                      </span>
                      {% else %} -- {% endif %}
                    </td>
                    <td>
                      {% if dividend.registration_date %}
                      <span class="text-muted" data-bs-toggle="tooltip" title="{{ dividend.registration_date|date:'Y-m-d' }}">
                        {{ dividend.registration_date|date:"Y-m-d" }}
                      </span>
                      {% else %} -- {% endif %}
                    </td>
                    <td>
                      {% if dividend.ex_dividend_date %}
                      <span class="text-muted" data-bs-toggle="tooltip" title="{{ dividend.ex_dividend_date|date:'Y-m-d' }}">
                        {{ dividend.ex_dividend_date|date:"Y-m-d" }}
                      </span>
                      {% else %} -- {% endif %}
                    </td>
                    <td>
                      {% if dividend.payment_date %}
                      <span class="text-muted" data-bs-toggle="tooltip" title="{{ dividend.payment_date|date:'Y-m-d' }}">
                        {{ dividend.payment_date|date:"Y-m-d" }}
                      </span>
                      {% else %} -- {% endif %}
                    </td>
                    <td>
                      {% if dividend.implementation_status %} {% if '实施' in dividend.implementation_status %}
                      <span class="badge bg-success">实施</span>
                      {% elif '预案' in dividend.implementation_status %}
                      <span class="badge bg-warning">预案</span>
                      {% else %}
                      <span class="badge bg-secondary">{{ dividend.implementation_status }}</span>
                      {% endif %} {% else %}
                      <span class="badge bg-secondary">--</span>
                      {% endif %}
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="10" class="text-center text-muted">
                      <div class="empty">
                        <div class="empty-icon">
                          <i class="ti ti-mood-sad"></i>
                        </div>
                        <p class="empty-title">暂无分红记录</p>
                        <p class="empty-subtitle text-muted">该股票暂时没有分红记录数据</p>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<style>
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }
  .subheader {
    font-size: 0.625rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    line-height: 1.6;
    color: #626976;
  }
  .table-sm td {
    padding: 0.3rem;
  }
  .empty {
    padding: 2rem 0;
    text-align: center;
  }
  .empty-title {
    font-size: 1rem;
    line-height: 1.4285714286;
    font-weight: 600;
  }
  /* 使卡片在每个行中保持相同高度 */
  .row-cards > .col {
    display: flex;
    flex-direction: column;
  }
  .row-cards .card {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
  }
  .row-cards .card .card-body {
    flex: 1 1 auto;
  }
  /* 表格样式优化 */
  .table thead th {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
  }
</style>
{% endblock %}
