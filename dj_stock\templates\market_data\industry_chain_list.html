{% extends "base.html" %} {% block title %}产业链列表 | 股票数据分析系统{% endblock %} {% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
  <h1 class="h2">产业链列表</h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    {% if available_dates %}
    <div class="dropdown me-2">
      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
        {% if selected_date %}{{ selected_date|date:"Y-m-d" }}{% else %}选择日期{% endif %}
      </button>
      <ul class="dropdown-menu">
        {% for date in available_dates %}
        <li>
          <a
            class="dropdown-item {% if date == selected_date %}active{% endif %}"
            href="?date={{ date|date:'Y-m-d' }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
          >
            {{ date|date:"Y-m-d" }}
          </a>
        </li>
        {% endfor %}
      </ul>
    </div>
    {% endif %}
    <div class="dropdown">
      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">排序方式</button>
      <ul class="dropdown-menu">
        <li>
          <a
            class="dropdown-item {% if sort_by == '-change_percent' %}active{% endif %}"
            href="?sort=-change_percent{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            涨跌幅 (降序)
          </a>
        </li>
        <li>
          <a
            class="dropdown-item {% if sort_by == 'change_percent' %}active{% endif %}"
            href="?sort=change_percent{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            涨跌幅 (升序)
          </a>
        </li>
        <li>
          <a
            class="dropdown-item {% if sort_by == '-total_market_value' %}active{% endif %}"
            href="?sort=-total_market_value{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            总市值 (降序)
          </a>
        </li>
        <li>
          <a
            class="dropdown-item {% if sort_by == '-turnover_rate' %}active{% endif %}"
            href="?sort=-turnover_rate{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_date %}&date={{ selected_date|date:'Y-m-d' }}{% endif %}"
          >
            换手率 (降序)
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>

<div class="card">
  <div class="card-header">
    <h3 class="card-title">产业链列表</h3>
    <div class="card-actions">
      <form method="get" action="{% url 'market_data:industry_chain_list' %}">
        <div class="input-group">
          <input type="text" class="form-control" placeholder="搜索产业链..." name="search" value="{{ search_query }}" />
          <button type="submit" class="btn btn-primary">
            <i class="ti ti-search"></i>
            搜索
          </button>
        </div>
      </form>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-vcenter card-table">
      <thead>
        <tr>
          <th>板块代码</th>
          <th>板块名称</th>
          <th>最新价</th>
          <th>涨跌幅</th>
          <th>涨跌额</th>
          <th>总市值(亿)</th>
          <th>换手率</th>
          <th>上涨/下跌家数</th>
          <th>领涨股票</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        {% for item in page_obj %}
        <tr>
          <td>{{ item.board_code }}</td>
          <td>{{ item.board_name }}</td>
          <td>{{ item.latest_price|floatformat:2 }}</td>
          <td class="{% if item.change_percent > 0 %}text-danger{% elif item.change_percent < 0 %}text-success{% endif %}">
            {{ item.change_percent|floatformat:2 }}%
          </td>
          <td class="{% if item.change_amount > 0 %}text-danger{% elif item.change_amount < 0 %}text-success{% endif %}">
            {{ item.change_amount|floatformat:2 }}
          </td>
          <td>{{ item.total_market_value|floatformat:2 }}</td>
          <td>{{ item.turnover_rate|floatformat:2 }}%</td>
          <td>
            <span class="text-danger">{{ item.up_count }}</span> /
            <span class="text-success">{{ item.down_count }}</span>
          </td>
          <td>
            {% if item.leading_stock %} {{ item.leading_stock }}
            <span class="{% if item.leading_stock_change_percent > 0 %}text-danger{% elif item.leading_stock_change_percent < 0 %}text-success{% endif %}">
              ({{ item.leading_stock_change_percent|floatformat:2 }}%)
            </span>
            {% else %} - {% endif %}
          </td>
          <td>
            <a href="{% url 'market_data:industry_board_detail' item.board_code %}" class="btn btn-primary btn-sm"> 详情 </a>
          </td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="10" class="text-center">暂无数据</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% if page_obj %}
  <div class="card-footer d-flex align-items-center">
    <p class="m-0 text-muted">共 <span>{{ total_items }}</span> 个产业链</p>
    {% include "includes/pagination.html" with page_obj=page_obj %}
  </div>
  {% endif %}
</div>

<!-- 产业链说明 -->
<div class="row mt-3">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">产业链说明</h3>
      </div>
      <div class="card-body">
        <div class="datagrid">
          <div class="datagrid-item">
            <div class="datagrid-title">产业链定义</div>
            <div class="datagrid-content">产业链是指围绕核心产业，由上下游相关产业组成的产业集合。</div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">上游企业</div>
            <div class="datagrid-content">为核心产业提供原材料、零部件等基础生产要素的企业。</div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">下游企业</div>
            <div class="datagrid-content">采购和使用核心产业产品的企业，包括分销商和终端用户。</div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">产业链分析</div>
            <div class="datagrid-content">通过分析产业链上下游企业的关系，可以更好地理解行业发展趋势和投资机会。</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
