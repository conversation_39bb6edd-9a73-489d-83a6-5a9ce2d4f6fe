# Generated by Django 5.1.7 on 2025-04-12 14:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('financial_analysis', '0002_alter_stockfinancialindicator_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockIndustryPE',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('industry_code', models.CharField(max_length=20, verbose_name='行业代码')),
                ('industry_name', models.CharField(max_length=50, verbose_name='行业名称')),
                ('trade_date', models.DateField(verbose_name='交易日期')),
                ('pe', models.FloatField(blank=True, null=True, verbose_name='市盈率')),
                ('pe_ttm', models.FloatField(blank=True, null=True, verbose_name='市盈率TTM')),
                ('pb', models.FloatField(blank=True, null=True, verbose_name='市净率')),
                ('ps', models.FloatField(blank=True, null=True, verbose_name='市销率')),
                ('ps_ttm', models.FloatField(blank=True, null=True, verbose_name='市销率TTM')),
                ('pcf', models.FloatField(blank=True, null=True, verbose_name='市现率')),
                ('pcf_ttm', models.FloatField(blank=True, null=True, verbose_name='市现率TTM')),
                ('dv_ratio', models.FloatField(blank=True, null=True, verbose_name='股息率')),
                ('dv_ttm', models.FloatField(blank=True, null=True, verbose_name='股息率TTM')),
                ('total_mv', models.FloatField(blank=True, null=True, verbose_name='总市值(亿元)')),
                ('industry_type', models.CharField(max_length=20, verbose_name='行业分类标准')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '行业市盈率数据',
                'verbose_name_plural': '行业市盈率数据',
                'db_table': 'stock_industry_pe',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StockShareholderDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shareholder_name', models.CharField(max_length=100, verbose_name='股东名称')),
                ('shareholder_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='股东类型')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('report_period', models.DateField(verbose_name='报告期')),
                ('holding_amount', models.FloatField(blank=True, null=True, verbose_name='期末持股数量（股）')),
                ('holding_change', models.FloatField(blank=True, null=True, verbose_name='期末持股数量变化（股）')),
                ('holding_change_ratio', models.FloatField(blank=True, null=True, verbose_name='期末持股数量变化比例（%）')),
                ('holding_trend', models.CharField(blank=True, max_length=20, null=True, verbose_name='期末持股变动趋势')),
                ('market_value', models.FloatField(blank=True, null=True, verbose_name='期末持股流通市值（元）')),
                ('announce_date', models.DateField(blank=True, null=True, verbose_name='公告日')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '股东持股明细',
                'verbose_name_plural': '股东持股明细',
                'db_table': 'stock_shareholder_detail',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TechnicalIndicator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=100, verbose_name='股票名称')),
                ('date', models.DateField(verbose_name='统计日期')),
                ('indicator_type', models.CharField(max_length=50, verbose_name='指标类型')),
                ('indicator_value', models.FloatField(blank=True, null=True, verbose_name='指标值')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '技术指标',
                'verbose_name_plural': '技术指标',
                'db_table': 'technical_indicator',
                'managed': False,
            },
        ),
        migrations.DeleteModel(
            name='StockFinancial',
        ),
        migrations.AlterModelOptions(
            name='stockfinancialindicator',
            options={'managed': True, 'verbose_name': '股票财务指标', 'verbose_name_plural': '股票财务指标'},
        ),
        migrations.AlterModelOptions(
            name='stockshareholderstatistics',
            options={'ordering': ['-report_date', 'shareholder_name'], 'verbose_name': '十大股东持股统计数据', 'verbose_name_plural': '十大股东持股统计数据'},
        ),
    ]
