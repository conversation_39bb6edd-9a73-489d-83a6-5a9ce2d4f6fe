from django.urls import path
from .views import views
from .views.shareholder_views import (
    shareholder_detail_list,
    shareholder_detail_by_stock,
    industry_pe_list,
    IndustryPEListView,
)



app_name = "financial_analysis"


urlpatterns = [
    path("dividend/", views.dividend_list, name="dividend_list"),
    path(
        "financial-report/", views.financial_report_list, name="financial_report_list"
    ),
    path(
        "financial-indicator/<str:code>/",
        views.financial_indicator,
        name="financial_indicator",
    ),
    path(
        "shareholder-statistics/<str:code>/",
        views.shareholder_statistics,
        name="shareholder_statistics",
    ),
    path(
        "technical-indicators/",
        views.technical_indicator_list,
        name="technical_indicator_list",
    ),
    path(
        "technical-indicators/<int:pk>/",
        views.technical_indicator_detail,
        name="technical_indicator_detail",
    ),
    path("industry-chains/", views.industry_chain_list, name="industry_chain_list"),
    path(
        "industry-chains/<str:chain_code>/",
        views.industry_chain_detail,
        name="industry_chain_detail",
    ),
    path(
        "financial-screener/",
        views.FinancialScreenerView.as_view(),
        name="financial_screener",
    ),

    # 股东持股相关URL
    path(
        "shareholder-detail/", shareholder_detail_list, name="shareholder_detail_list"
    ),
    path(
        "shareholder-detail/<str:code>/",
        shareholder_detail_by_stock,
        name="shareholder_detail_by_stock",
    ),
    # 行业市盈率相关URL
    path("industry-pe/", industry_pe_list, name="industry_pe_list"),
    path(
        "industry-pe-class/",
        IndustryPEListView.as_view(),
        name="industry_pe_list_class",
    ),
    # 股票收藏相关URL
    path("batch-favorite/", views.batch_favorite, name="batch_favorite"),
    path("favorites/", views.favorite_list, name="favorite_list"),
    path(
        "remove-favorite/<int:favorite_id>/",
        views.remove_favorite,
        name="remove_favorite",
    ),
    path("get-favorite-stocks/", views.get_favorite_stocks, name="get_favorite_stocks"),
    path(
        "add-favorite/<str:stock_code>/<str:stock_name>/",
        views.add_favorite,
        name="add_favorite",
    ),
    path(
        "ajax-add-favorite/",
        views.ajax_add_favorite,
        name="ajax_add_favorite",
    ),

    # 智能选股助手
    path("investment-assistant/", views.investment_assistant, name="investment_assistant"),
]
