# -*- coding: utf-8 -*-
"""
板块资金分析视图模块

提供行业板块和概念板块的资金流向分析功能
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.db.models import Q, Sum, Avg, Count
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import json
import logging

from .base_views import (
    StockSectorFundFlow,
    StockBoardConcept,
    StockIndustryBoard,
)

logger = logging.getLogger(__name__)


def sector_analysis(request):
    """板块资金分析主页面"""
    
    # 获取筛选参数
    sector_type = request.GET.get('sector_type', 'all')  # all, 行业板块, 概念板块
    time_range = request.GET.get('time_range', '7')  # 7, 15, 30, 60天
    sort_by = request.GET.get('sort_by', 'net_inflow_amount')  # 排序字段
    flow_direction = request.GET.get('flow_direction', 'all')  # all, inflow, outflow
    min_amount = request.GET.get('min_amount', '')  # 最小资金流入
    
    # 获取时间范围
    days = int(time_range)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)
    
    # 构建查询条件
    query = Q(trade_date__gte=start_date, trade_date__lte=end_date)
    
    if sector_type != 'all':
        query &= Q(sector_type=sector_type)
    
    if flow_direction == 'inflow':
        query &= Q(net_inflow_amount__gt=0)
    elif flow_direction == 'outflow':
        query &= Q(net_inflow_amount__lt=0)
    
    if min_amount:
        try:
            min_val = float(min_amount) * 100000000  # 转换为元
            query &= Q(net_inflow_amount__gte=min_val)
        except ValueError:
            pass
    
    # 获取板块资金流向数据并按板块聚合
    sector_summary = (
        StockSectorFundFlow.objects.filter(query)
        .values('sector_name', 'sector_type')
        .annotate(
            total_inflow=Sum('net_inflow_amount'),
            avg_inflow=Avg('net_inflow_amount'),
            max_inflow=Sum('net_inflow_amount'),
            trading_days=Count('trade_date'),
            avg_rate=Avg('net_inflow_rate'),
            total_super_big=Sum('super_big_net_inflow'),
            total_big=Sum('big_net_inflow'),
            total_medium=Sum('medium_net_inflow'),
            total_small=Sum('small_net_inflow'),
        )
        .order_by(f'-{sort_by}' if sort_by.startswith('total_') or sort_by.startswith('avg_') else f'-total_inflow')
    )
    
    # 转换金额单位为亿元
    for item in sector_summary:
        item['total_inflow'] = round((item['total_inflow'] or 0) / 100000000, 2)
        item['avg_inflow'] = round((item['avg_inflow'] or 0) / 100000000, 2)
        item['total_super_big'] = round((item['total_super_big'] or 0) / 100000000, 2)
        item['total_big'] = round((item['total_big'] or 0) / 100000000, 2)
        item['total_medium'] = round((item['total_medium'] or 0) / 100000000, 2)
        item['total_small'] = round((item['total_small'] or 0) / 100000000, 2)
    
    # 分页
    paginator = Paginator(sector_summary, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 获取统计数据
    stats = get_sector_stats(query)

    # 获取热门板块（最近3天）
    hot_sectors = get_hot_sectors()

    # 计算总净流入数据（基于筛选后的原始数据）
    filtered_data = StockSectorFundFlow.objects.filter(query)
    total_stats = filtered_data.aggregate(
        total_net_inflow=Sum('net_inflow_amount'),
        total_super_big=Sum('super_big_net_inflow'),
        total_big=Sum('big_net_inflow'),
        total_medium=Sum('medium_net_inflow'),
        total_small=Sum('small_net_inflow'),
        inflow_count=Count('id', filter=Q(net_inflow_amount__gt=0)),
        outflow_count=Count('id', filter=Q(net_inflow_amount__lt=0)),
    )

    # 转换为亿元
    for key in ['total_net_inflow', 'total_super_big', 'total_big', 'total_medium', 'total_small']:
        if total_stats[key]:
            total_stats[key] = round(total_stats[key] / 100000000, 2)
        else:
            total_stats[key] = 0

    context = {
        'page_obj': page_obj,
        'sector_type': sector_type,
        'time_range': time_range,
        'sort_by': sort_by,
        'flow_direction': flow_direction,
        'min_amount': min_amount,
        'stats': stats,
        'hot_sectors': hot_sectors,
        'total_stats': total_stats,
    }
    
    return render(request, 'market_data/sector_analysis.html', context)


def get_sector_stats(query):
    """获取板块统计数据"""
    
    # 总体统计
    total_stats = StockSectorFundFlow.objects.filter(query).aggregate(
        total_sectors=Count('sector_name', distinct=True),
        total_inflow=Sum('net_inflow_amount'),
        avg_inflow=Avg('net_inflow_amount'),
        inflow_sectors=Count('sector_name', filter=Q(net_inflow_amount__gt=0), distinct=True),
        outflow_sectors=Count('sector_name', filter=Q(net_inflow_amount__lt=0), distinct=True),
    )
    
    # 行业板块统计
    industry_stats = StockSectorFundFlow.objects.filter(
        query, sector_type='行业板块'
    ).aggregate(
        count=Count('sector_name', distinct=True),
        total_inflow=Sum('net_inflow_amount'),
    )
    
    # 概念板块统计
    concept_stats = StockSectorFundFlow.objects.filter(
        query, sector_type='概念板块'
    ).aggregate(
        count=Count('sector_name', distinct=True),
        total_inflow=Sum('net_inflow_amount'),
    )
    
    return {
        'total': total_stats,
        'industry': industry_stats,
        'concept': concept_stats,
    }


def get_hot_sectors(days=3):
    """获取热门板块（最近几天资金流入最多的板块）"""
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)
    
    hot_sectors = (
        StockSectorFundFlow.objects.filter(
            trade_date__gte=start_date,
            trade_date__lte=end_date
        )
        .values('sector_name', 'sector_type')
        .annotate(
            total_inflow=Sum('net_inflow_amount'),
            avg_rate=Avg('net_inflow_rate'),
        )
        .filter(total_inflow__gt=0)
        .order_by('-total_inflow')[:10]
    )
    
    for item in hot_sectors:
        item['total_inflow'] = round((item['total_inflow'] or 0) / 100000000, 2)
        item['avg_rate'] = round(item['avg_rate'] or 0, 2)
    
    return hot_sectors


def api_sector_trend_comparison(request):
    """API: 板块趋势对比数据"""
    
    sector_names = request.GET.getlist('sectors[]')
    days = int(request.GET.get('days', 30))
    
    if not sector_names:
        return JsonResponse({'error': '请选择要对比的板块'}, status=400)
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)
    
    # 获取各板块的历史数据
    trend_data = {}
    
    for sector_name in sector_names:
        sector_data = (
            StockSectorFundFlow.objects.filter(
                sector_name=sector_name,
                trade_date__gte=start_date,
                trade_date__lte=end_date
            )
            .order_by('trade_date')
            .values('trade_date', 'net_inflow_amount', 'net_inflow_rate')
        )
        
        trend_data[sector_name] = [
            {
                'date': item['trade_date'].strftime('%Y-%m-%d'),
                'amount': round((item['net_inflow_amount'] or 0) / 100000000, 2),
                'rate': round(item['net_inflow_rate'] or 0, 2),
            }
            for item in sector_data
        ]
    
    return JsonResponse({
        'success': True,
        'data': trend_data,
        'period': f'{days}天'
    })


def api_sector_fund_structure(request):
    """API: 板块资金结构分析"""
    
    sector_name = request.GET.get('sector_name')
    days = int(request.GET.get('days', 30))
    
    if not sector_name:
        return JsonResponse({'error': '请指定板块名称'}, status=400)
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)
    
    # 获取资金结构数据
    fund_structure = StockSectorFundFlow.objects.filter(
        sector_name=sector_name,
        trade_date__gte=start_date,
        trade_date__lte=end_date
    ).aggregate(
        super_big=Sum('super_big_net_inflow'),
        big=Sum('big_net_inflow'),
        medium=Sum('medium_net_inflow'),
        small=Sum('small_net_inflow'),
    )
    
    # 转换为亿元
    for key in fund_structure:
        fund_structure[key] = round((fund_structure[key] or 0) / 100000000, 2)
    
    return JsonResponse({
        'success': True,
        'sector_name': sector_name,
        'fund_structure': fund_structure,
        'period': f'{days}天'
    })
