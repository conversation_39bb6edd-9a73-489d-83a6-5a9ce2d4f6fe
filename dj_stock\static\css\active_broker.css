.stock-container {
  max-width: 600px;
}

.stock-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 5px;
}

.stock-card {
  display: block;
  border-radius: 4px;
  padding: 5px;
  margin-bottom: 2px;
  text-decoration: none;
  color: #333;
  transition: all 0.2s ease;
}

.stock-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stock-card.up {
  background: rgba(255, 235, 235, 1);
  border-left: 3px solid #dc3545;
}

.stock-card.down {
  background: rgba(235, 255, 235, 1);
  border-left: 3px solid #28a745;
}

.stock-card.flat {
  background: #f0f0f0;
  border-left: 3px solid #6c757d;
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.stock-code {
  font-weight: bold;
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stock-change {
  font-size: 0.7rem;
  padding: 2px 4px;
  border-radius: 3px;
}

.stock-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.75rem;
}

.toggle-stocks-btn {
  font-size: 0.75rem;
  padding: 2px 8px;
  margin-top: 4px;
}

.stock-grid-expanded {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 5px;
  margin-top: 8px;
}
