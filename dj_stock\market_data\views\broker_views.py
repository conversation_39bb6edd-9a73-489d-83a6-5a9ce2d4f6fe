from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Sum, Count
from django.core.paginator import Paginator

from market_data.models import StockActiveBroker, StockDailyQuote
from market_data.views.base_views import get_pagination
import json


def active_broker_list(request):
    """活跃营业部列表视图"""
    # 获取查询参数
    broker_name = request.GET.get("broker_name", "")
    date = request.GET.get("date", "")
    sort = request.GET.get("sort", "-trade_date")
    per_page = int(request.GET.get("per_page", 50))

    # 获取可用的交易日期
    available_dates = (
        StockActiveBroker.objects.values_list("trade_date", flat=True)
        .distinct()
        .order_by("-trade_date")
    )

    # 构建基础查询
    queryset = StockActiveBroker.objects.all()

    # 应用过滤条件
    if broker_name:
        queryset = queryset.filter(broker_name__contains=broker_name)
    if date:
        queryset = queryset.filter(trade_date=date)

    # 应用排序
    valid_sort_fields = [
        "-trade_date",
        "trade_date",
        "-buy_amount",
        "buy_amount",
        "-sell_amount",
        "sell_amount",
        "-net_amount",
        "net_amount",
    ]
    if sort in valid_sort_fields:
        queryset = queryset.order_by(sort)
    else:
        queryset = queryset.order_by("-trade_date", "-buy_amount")
        sort = "-trade_date"

    # 计算统计数据
    stats = StockActiveBroker.objects.aggregate(
        total_buy_amount=Sum("buy_amount"),
        total_sell_amount=Sum("sell_amount"),
        total_net_amount=Sum("net_amount"),
    )

    # 将金额转换为亿元
    total_buy_amount = float(stats["total_buy_amount"] or 0) / 100000000
    total_sell_amount = float(stats["total_sell_amount"] or 0) / 100000000
    total_net_amount = float(stats["total_net_amount"] or 0) / 100000000

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(queryset, page, per_page)

    # 处理买入股票的数据，添加涨跌幅信息
    for item in page_obj:
        # 将金额转换为万元
        if item.buy_amount is not None:
            item.buy_amount = float(item.buy_amount) / 10000
        if item.sell_amount is not None:
            item.sell_amount = float(item.sell_amount) / 10000
        if item.net_amount is not None:
            item.net_amount = float(item.net_amount) / 10000

        if item.buy_stocks:
            try:
                # 尝试获取买入股票列表，可能是JSON格式或者普通的文本
                buy_stocks_list = []
                if item.buy_stocks.startswith("[") and item.buy_stocks.endswith("]"):
                    # 如果是JSON格式的字符串，解析成列表
                    try:
                        stocks_data = json.loads(item.buy_stocks)
                        for stock in stocks_data:
                            if isinstance(stock, dict):
                                # 如果是字典格式，直接添加
                                buy_stocks_list.append(stock)
                            else:
                                # 如果是简单字符串，创建字典
                                code_name = (
                                    stock.split(" ", 1) if " " in stock else [stock, ""]
                                )
                                buy_stocks_list.append(
                                    {
                                        "code": code_name[0].strip(),
                                        "name": (
                                            code_name[1].strip()
                                            if len(code_name) > 1
                                            else ""
                                        ),
                                    }
                                )
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，当作普通文本处理
                        stocks = item.buy_stocks.replace("[", "").replace("]", "")
                        stocks = (
                            stocks.replace("，", ",")
                            .replace("；", ";")
                            .replace(";", ",")
                            .split(",")
                        )
                        for stock in stocks:
                            if stock.strip():
                                parts = stock.strip().split(" ", 1)
                                code = parts[0].strip()
                                name = parts[1].strip() if len(parts) > 1 else ""
                                buy_stocks_list.append({"code": code, "name": name})
                else:
                    # 如果是普通文本，按逗号或分号分割
                    stocks = (
                        item.buy_stocks.replace("，", ",")
                        .replace("；", ";")
                        .replace(";", ",")
                        .split(",")
                    )
                    for stock in stocks:
                        if stock.strip():
                            parts = stock.strip().split(" ", 1)
                            code = parts[0].strip()
                            name = parts[1].strip() if len(parts) > 1 else ""
                            buy_stocks_list.append({"code": code, "name": name})

                # 获取股票的涨跌幅信息
                for stock in buy_stocks_list:
                    stock_code = stock.get("code", "").strip()
                    if stock_code:
                        # 标准化股票代码格式（确保前缀为0的代码能正确匹配）
                        if stock_code.isdigit() and len(stock_code) < 6:
                            stock_code = stock_code.zfill(6)
                            stock["code"] = stock_code

                        # 查询当天的股票涨跌幅
                        quote = StockDailyQuote.objects.filter(
                            stock_code=stock_code, trade_date=item.trade_date
                        ).first()

                        if quote and quote.change_percent is not None:
                            stock["change_percent"] = float(quote.change_percent)
                            # 添加收盘价信息
                            stock["close_price"] = (
                                float(quote.close_price) if quote.close_price else None
                            )
                            print(
                                f"股票 {stock_code} 涨跌幅: {stock['change_percent']}%"
                            )  # 调试信息
                        else:
                            # 尝试获取最近的交易数据
                            recent_quote = (
                                StockDailyQuote.objects.filter(stock_code=stock_code)
                                .order_by("-trade_date")
                                .first()
                            )

                            if recent_quote and recent_quote.change_percent is not None:
                                stock["change_percent"] = float(
                                    recent_quote.change_percent
                                )
                                # 添加收盘价信息
                                stock["close_price"] = (
                                    float(recent_quote.close_price)
                                    if recent_quote.close_price
                                    else None
                                )
                                stock["is_recent"] = True  # 标记为非当日数据
                                print(
                                    f"股票 {stock_code} 使用最近数据，涨跌幅: {stock['change_percent']}%"
                                )  # 调试信息
                            else:
                                stock["change_percent"] = None
                                stock["close_price"] = None
                                print(f"股票 {stock_code} 未找到涨跌幅数据")  # 调试信息

                # 如果buy_stocks_list为空但item.buy_stock_count有值，尝试生成虚拟列表
                if (
                    not buy_stocks_list
                    and item.buy_stock_count
                    and item.buy_stock_count > 0
                ):
                    buy_stocks_list = [
                        {
                            "code": "unknown",
                            "name": f"未知股票 {i+1}",
                            "change_percent": None,
                        }
                        for i in range(min(int(item.buy_stock_count), 5))
                    ]

                # 根据涨跌幅排序，先显示涨幅大的
                buy_stocks_list = sorted(
                    buy_stocks_list,
                    key=lambda x: (
                        x.get("change_percent")
                        if x.get("change_percent") is not None
                        else -9999
                    ),
                    reverse=True,
                )

                item.parsed_buy_stocks = buy_stocks_list
            except Exception as e:
                # 如果解析失败，设置为空列表
                item.parsed_buy_stocks = []
                print(f"解析股票数据出错: {str(e)}")
        else:
            item.parsed_buy_stocks = []

        # 添加ID属性，用于前端展开/折叠功能
        item.id = f"row_{item.pk}"

    context = {
        "page_obj": page_obj,
        "broker_name": broker_name,
        "date": date,
        "sort": sort,
        "per_page": per_page,
        "available_dates": available_dates,
        "total_buy_amount": total_buy_amount,
        "total_sell_amount": total_sell_amount,
        "total_net_amount": total_net_amount,
    }

    return render(request, "market_data/active_broker.html", context)


def active_broker_detail(request, broker_name):
    """活跃营业部详情视图"""
    # 获取查询参数
    date = request.GET.get("date", "")

    # 获取可用的交易日期
    available_dates = (
        StockActiveBroker.objects.filter(broker_name=broker_name)
        .values_list("trade_date", flat=True)
        .distinct()
        .order_by("-trade_date")
    )

    # 构建基础查询
    queryset = StockActiveBroker.objects.filter(broker_name=broker_name)

    # 应用过滤条件
    if date:
        queryset = queryset.filter(trade_date=date)

    # 排序
    queryset = queryset.order_by("-trade_date", "-net_amount")

    # 计算统计数据
    stats = StockActiveBroker.objects.filter(broker_name=broker_name).aggregate(
        total_buy_amount=Sum("buy_amount"),
        total_sell_amount=Sum("sell_amount"),
        total_net_amount=Sum("net_amount"),
        total_records=Count("pk"),
    )

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(queryset, page, 20)

    # 处理买入股票的数据，添加涨跌幅信息
    for item in page_obj:
        # 将金额转换为万元
        if item.buy_amount is not None:
            item.buy_amount = float(item.buy_amount) / 10000
        if item.sell_amount is not None:
            item.sell_amount = float(item.sell_amount) / 10000
        if item.net_amount is not None:
            item.net_amount = float(item.net_amount) / 10000

        if item.buy_stocks:
            try:
                # 尝试获取买入股票列表，可能是JSON格式或者普通的文本
                buy_stocks_list = []
                if item.buy_stocks.startswith("[") and item.buy_stocks.endswith("]"):
                    # 如果是JSON格式的字符串，解析成列表
                    try:
                        stocks_data = json.loads(item.buy_stocks)
                        for stock in stocks_data:
                            if isinstance(stock, dict):
                                # 如果是字典格式，直接添加
                                buy_stocks_list.append(stock)
                            else:
                                # 如果是简单字符串，创建字典
                                code_name = (
                                    stock.split(" ", 1) if " " in stock else [stock, ""]
                                )
                                buy_stocks_list.append(
                                    {
                                        "code": code_name[0].strip(),
                                        "name": (
                                            code_name[1].strip()
                                            if len(code_name) > 1
                                            else ""
                                        ),
                                    }
                                )
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，当作普通文本处理
                        stocks = item.buy_stocks.replace("[", "").replace("]", "")
                        stocks = (
                            stocks.replace("，", ",")
                            .replace("；", ";")
                            .replace(";", ",")
                            .split(",")
                        )
                        for stock in stocks:
                            if stock.strip():
                                parts = stock.strip().split(" ", 1)
                                code = parts[0].strip()
                                name = parts[1].strip() if len(parts) > 1 else ""
                                buy_stocks_list.append({"code": code, "name": name})
                else:
                    # 如果是普通文本，按逗号或分号分割
                    stocks = (
                        item.buy_stocks.replace("，", ",")
                        .replace("；", ";")
                        .replace(";", ",")
                        .split(",")
                    )
                    for stock in stocks:
                        if stock.strip():
                            parts = stock.strip().split(" ", 1)
                            code = parts[0].strip()
                            name = parts[1].strip() if len(parts) > 1 else ""
                            buy_stocks_list.append({"code": code, "name": name})

                # 获取股票的涨跌幅信息
                for stock in buy_stocks_list:
                    stock_code = stock.get("code", "").strip()
                    if stock_code:
                        # 标准化股票代码格式（确保前缀为0的代码能正确匹配）
                        if stock_code.isdigit() and len(stock_code) < 6:
                            stock_code = stock_code.zfill(6)
                            stock["code"] = stock_code

                        # 查询当天的股票涨跌幅
                        quote = StockDailyQuote.objects.filter(
                            stock_code=stock_code, trade_date=item.trade_date
                        ).first()

                        if quote and quote.change_percent is not None:
                            stock["change_percent"] = float(quote.change_percent)
                            # 添加收盘价信息
                            stock["close_price"] = (
                                float(quote.close_price) if quote.close_price else None
                            )
                            print(
                                f"股票 {stock_code} 涨跌幅: {stock['change_percent']}%"
                            )  # 调试信息
                        else:
                            # 尝试获取最近的交易数据
                            recent_quote = (
                                StockDailyQuote.objects.filter(stock_code=stock_code)
                                .order_by("-trade_date")
                                .first()
                            )

                            if recent_quote and recent_quote.change_percent is not None:
                                stock["change_percent"] = float(
                                    recent_quote.change_percent
                                )
                                # 添加收盘价信息
                                stock["close_price"] = (
                                    float(recent_quote.close_price)
                                    if recent_quote.close_price
                                    else None
                                )
                                stock["is_recent"] = True  # 标记为非当日数据
                                print(
                                    f"股票 {stock_code} 使用最近数据，涨跌幅: {stock['change_percent']}%"
                                )  # 调试信息
                            else:
                                stock["change_percent"] = None
                                stock["close_price"] = None
                                print(f"股票 {stock_code} 未找到涨跌幅数据")  # 调试信息

                # 如果buy_stocks_list为空但item.buy_stock_count有值，尝试生成虚拟列表
                if (
                    not buy_stocks_list
                    and item.buy_stock_count
                    and item.buy_stock_count > 0
                ):
                    buy_stocks_list = [
                        {
                            "code": "unknown",
                            "name": f"未知股票 {i+1}",
                            "change_percent": None,
                        }
                        for i in range(min(int(item.buy_stock_count), 5))
                    ]

                # 根据涨跌幅排序，先显示涨幅大的
                buy_stocks_list = sorted(
                    buy_stocks_list,
                    key=lambda x: (
                        x.get("change_percent")
                        if x.get("change_percent") is not None
                        else -9999
                    ),
                    reverse=True,
                )

                item.parsed_buy_stocks = buy_stocks_list
            except Exception as e:
                # 如果解析失败，设置为空列表
                item.parsed_buy_stocks = []
        else:
            item.parsed_buy_stocks = []

    # 将统计数据转换为万元
    if stats["total_buy_amount"] is not None:
        stats["total_buy_amount"] = float(stats["total_buy_amount"]) / 10000
    if stats["total_sell_amount"] is not None:
        stats["total_sell_amount"] = float(stats["total_sell_amount"]) / 10000
    if stats["total_net_amount"] is not None:
        stats["total_net_amount"] = float(stats["total_net_amount"]) / 10000

    context = {
        "page_obj": page_obj,
        "broker_name": broker_name,
        "date": date,
        "available_dates": available_dates,
        "total_buy_amount": stats["total_buy_amount"] or 0,
        "total_sell_amount": stats["total_sell_amount"] or 0,
        "total_net_amount": stats["total_net_amount"] or 0,
        "total_records": stats["total_records"] or 0,
    }

    return render(request, "market_data/active_broker_detail.html", context)
